import { useScreenSize } from '@/hooks';
import { cn } from '@/lib/utils';
import { Button } from 'devextreme-react';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  ColumnFixing,
  DataGridRef,
  Editing,
  FilterRow,
  Grouping,
  GroupPanel,
  HeaderFilter,
  Item,
  Pager,
  Paging,
  Scrolling,
  Search,
  StateStoring,
  Toolbar,
} from 'devextreme-react/data-grid';
import { PagerPageSize } from 'devextreme/common/grids';
import { RowDblClickEvent } from 'devextreme/ui/data_grid';
import React, { useEffect, useRef, useState } from 'react';
import { onCellPrepared } from './devex-data-grid-helper';
import { translationWithNamespace } from '@/lib/i18nUtils';

type ColumnBand = { name: string; columns?: ColumnBand[] };

type Props = {
  onAddNewClick?: () => void;
  onEditDoubleClick?: (e: RowDblClickEvent) => void;
  onRefresh?: () => void;
  customToolbar?: React.ReactNode | React.ReactNode[];
  hideSerialNumber?: boolean;
  stateStoring?: boolean;
  columnsBands?: ColumnBand[];
  setRef?: (current: DataGridRef | null) => void;
  onSavedCustom?: (e: any) => void;
};
const allowPageSizes: Array<number | PagerPageSize> = [5, 10, 50, 100, 'all'];
const selectTextOnFocus = (args: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  args.element.querySelector('input.dx-texteditor-input')?.select();
};
const t = translationWithNamespace('common');
export function DevexDataGridEditable({
  children,
  // ref,
  onRefresh,
  onAddNewClick,
  onEditDoubleClick,
  customToolbar,
  setRef,
  onSavedCustom,
  ...props
}: React.PropsWithChildren & React.ComponentProps<typeof DataGrid> & Props) {
  const [expanded, setExpanded] = useState(true);
  const treeListRef = useRef<DataGridRef>(null);
  useEffect(() => {
    if (setRef) {
      setRef(treeListRef.current);
    }
  }, [setRef]);

  const { height } = useScreenSize();
  const dataSource = ((props.dataSource ?? []) as Record<string, any>[]).map((item, index) => ({
    ...item,
    index: index + 1,
  }));

  const columnWidthMapRef = useRef<Map<string, number>>(new Map());
  const isAutoFitting = useRef(false);
  useEffect(() => {
    const instance = treeListRef.current?.instance();
    if (!instance) return;
    instance.beginUpdate();
    instance.option('columnAutoWidth', true);
    instance.option('wordWrapEnabled', true);
    instance.endUpdate();
  }, [treeListRef]);

  return (
    <DataGrid
      keyExpr={'id'}
      ref={treeListRef}
      id="dataGrid"
      // columnAutoWidth={true}
      // allowColumnResizing
      columnResizingMode="widget"
      allowColumnReordering
      showBorders
      showColumnLines
      showRowLines
      hoverStateEnabled
      repaintChangesOnly
      // // rowAlternationEnabled
      wordWrapEnabled
      columnFixing={{
        enabled: false,
      }}
      className={cn(
        'column-header-wrap',
        'max-h-[calc(100vh-9.8rem-32.8px)]',
        height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
      )}
      onRowDblClick={onEditDoubleClick}
      {...props}
      dataSource={dataSource}
      onSaved={() => {
        onSavedCustom?.(dataSource);
      }}
      onEditorPreparing={e => {
        if (e.parentType !== 'dataRow') {
          return;
        }
        // select whole content
        e.editorOptions.onFocusIn = selectTextOnFocus;
        props.onEditorPreparing?.(e);
      }}
      onCellPrepared={e => {
        onCellPrepared(e);
        props.onCellPrepared?.(e);
      }}
      onContextMenuPreparing={e => {
        props.onContextMenuPreparing?.(e);
        if (e.target !== 'header') {
          return;
        }
        if (!e.items) e.items = [];

        // Add a custom menu item
        e.items.push({
          text: t('action.autoFit'),
          onItemClick: () => {
            const instance = treeListRef.current?.instance();
            if (instance) {
              isAutoFitting.current = true;
              // const widthBefore = instance.element().scrollWidth;
              try {
                instance.beginUpdate();
                instance.getVisibleColumns().forEach((column: any) => {
                  if (column.dataField) {
                    if (column.dataField !== 'serialNumber') {
                      instance.columnOption(column.dataField as string, 'width', 'auto');
                    } else {
                      instance.columnOption(column.dataField as string, 'width', 60);
                    }
                  }
                });
                instance.option('wordWrapEnabled', false);
                instance.endUpdate();
                instance.updateDimensions();
                const widthAuto = instance.element().scrollWidth;

                instance.beginUpdate();
                instance.getVisibleColumns().forEach((column: any) => {
                  if (column.dataField) {
                    if (column.dataField !== 'serialNumber') {
                      instance.columnOption(column.dataField as string, 'width', undefined);
                    } else {
                      instance.columnOption(column.dataField as string, 'width', 60);
                    }
                  }
                });
                instance.endUpdate();
                instance.updateDimensions();
                const widthUndefined = instance.element().scrollWidth;

                let attribute = undefined;
                if (widthAuto >= widthUndefined) {
                  attribute = 'auto';
                } else {
                  attribute = undefined;
                }

                instance.getVisibleColumns().forEach((column: any) => {
                  if (column.dataField) {
                    if (column.dataField !== 'serialNumber') {
                      instance.columnOption(column.dataField as string, 'width', attribute);
                    } else {
                      instance.columnOption(column.dataField as string, 'width', 60);
                    }
                  }
                });

                instance.endUpdate();
                instance.updateDimensions();

                instance.getVisibleColumns().forEach((column: any) => {
                  if (column.dataField) {
                    columnWidthMapRef.current.set(
                      column.dataField as string,
                      column.visibleWidth as number
                    );
                  }
                });

                setTimeout(() => {
                  const state = instance.state();
                  instance.state({
                    ...state,
                    columns: (state.columns as Record<string, any>[]).map(column => {
                      return {
                        ...column,
                        width:
                          columnWidthMapRef.current.get(column.dataField as string) || column.width,
                      };
                    }),
                  });
                }, 100);
              } catch (error) {
                console.error('Error autofitting columns:', error);
              } finally {
                setTimeout(() => {
                  isAutoFitting.current = false;
                }, 100);
              }
            }
          },
        });
      }}
      onOptionChanged={e => {
        if (e.name === 'columns' && e.fullName.includes('.width')) {
          if (!isAutoFitting.current) {
            const instance = treeListRef.current?.instance();
            if (instance) {
              instance.option('wordWrapEnabled', true);
            }
          }
        }
      }}
    >
      <ColumnChooser enabled mode="select" height="45rem">
        <ColumnChooserSearch enabled />
        <ColumnChooserSelection allowSelectAll selectByClick recursive />
      </ColumnChooser>
      <FilterRow visible showOperationChooser />
      <Scrolling mode="standard" rowRenderingMode="standard" />
      <Paging enabled defaultPageSize={10} />
      <Pager
        visible
        showInfo
        showNavigationButtons
        showPageSizeSelector
        displayMode="adaptive"
        allowedPageSizes={allowPageSizes}
      />
      <Grouping autoExpandAll={expanded} contextMenuEnabled={true} expandMode="rowClick" />
      <StateStoring enabled={false} />
      {/* <StateStoring
        enabled
        type="custom"
        storageKey={storageKey}
        // customSave={state => {
        //   console.log('state:', state);
        // }}
        customLoad={loadState}
        customSave={customSave}
      /> */}
      <HeaderFilter visible>
        <Search enabled mode="contains" />
      </HeaderFilter>
      {/* <SearchPanel visible /> */}
      <GroupPanel visible={false} />
      {/* <FilterPanel visible /> */}
      <Editing confirmDelete allowUpdating allowDeleting allowAdding useIcons />
      <Toolbar>
        <Item name="groupPanel" />
        <Item location="after">
          <Button
            icon={expanded ? 'chevrondown' : 'chevronleft'}
            onClick={() => setExpanded(prevExpanded => !prevExpanded)}
            hint={expanded ? 'Đóng nhóm' : 'Mở nhóm'}
          />
        </Item>
        {customToolbar}
        {onAddNewClick && (
          <Item>
            <Button icon="plus" onClick={onAddNewClick} hint="Thêm mới" />
          </Item>
        )}
        {onRefresh && (
          <Item location="after">
            <Button icon="refresh" onClick={onRefresh} hint="Lấy lại dữ liệu mới" />
          </Item>
        )}
        <Item name="columnChooserButton" />
        <Item name="exportButton" />
        <Item name="searchPanel" />
      </Toolbar>
      {/* <Column dataField="branchName" /> */}
      <ColumnFixing enabled />
      <Column
        dataField="index"
        caption="STT"
        dataType="number"
        format={',##0,##'}
        alignment="center"
        width={60}
        allowEditing={false}
        allowFiltering={false}
        allowSorting={false}
        fixed={false}
        fixedPosition="left"
      />
      {children}
    </DataGrid>
  );
}
