import { lazy, Suspense, useState, useRef, useEffect } from 'react';
import { Button } from 'devextreme-react/button';

// Sử dụng dynamic import để tải component một cách lazy
const LazyChatDialog = lazy(() =>
  import('./chat-dialog').then(module => ({
    default: module.default,
  }))
);

export function LazyChatButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [hasMoved, setHasMoved] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);

  const toggleChat = () => {
    if (!hasMoved) {
      // Chỉ toggle khi không phải drag
      setIsOpen(!isOpen);
    }
  };

  // Xử lý bắt đầu kéo
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // Chỉ xử lý click chuột trái
    setIsDragging(true);
    setHasMoved(false);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });
    e.preventDefault();
  };

  // Xử lý kéo
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;

    // Đánh dấu đã di chuyển nếu khoảng cách > 5px
    if (!hasMoved && (Math.abs(newX - position.x) > 5 || Math.abs(newY - position.y) > 5)) {
      setHasMoved(true);
    }

    // Giới hạn vị trí trong viewport
    const maxX = window.innerWidth - 60; // 60px là kích thước nút + padding
    const maxY = window.innerHeight - 60;

    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY)),
    });
  };

  // Xử lý kết thúc kéo
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Thêm event listeners cho document
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // Ngăn chọn text khi kéo
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isDragging, dragStart]);

  // Khởi tạo vị trí mặc định
  useEffect(() => {
    const defaultX = window.innerWidth - 80;
    const defaultY = window.innerHeight - 120;
    setPosition({ x: defaultX, y: defaultY });
  }, []);

  return (
    <div
      ref={buttonRef}
      className="fixed z-50"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      <Suspense fallback={null}>
        {isOpen && (
          <div
            className="absolute h-[80dvh] w-[100dvw] overflow-hidden rounded-lg bg-white shadow-xl md:h-[600px] md:w-[600px]"
            style={{
              // Điều chỉnh vị trí dialog dựa trên vị trí nút
              bottom: position.y > window.innerHeight / 2 ? '60px' : 'auto',
              top: position.y <= window.innerHeight / 2 ? '60px' : 'auto',
              right: position.x > window.innerWidth / 2 ? '0px' : 'auto',
              left: position.x <= window.innerWidth / 2 ? '0px' : 'auto',
            }}
          >
            <LazyChatDialog onClose={() => setIsOpen(false)} />
          </div>
        )}
      </Suspense>
      <div
        onMouseDown={handleMouseDown}
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
      >
        <Button
          type="default"
          stylingMode="contained"
          icon="comment"
          onClick={toggleChat}
          className="!h-10 !w-10 !rounded-full bg-gradient-to-b from-[#379ae6] via-[#4da6ea] via-[#5bb3ed] to-[#69c0f0] !shadow-lg transition-all duration-200 hover:!bg-info-600"
        />
      </div>
    </div>
  );
}
