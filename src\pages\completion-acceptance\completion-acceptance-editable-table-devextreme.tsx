import { ErrorMessage } from '@/components/ui/error-message';
import {
  DEFAULT_DECIMAL,
  DEFAULT_DECIMAL_SCALE,
  downloadTemplateLabel,
  PROFESSIONS,
  QUERIES,
  TABLES,
} from '@/constant';
import {
  BiddingMethod,
  BiddingSector,
  CompletionAcceptance,
  CompletionAcceptanceDetail,
  ConstructionTask,
  Contractor,
  CostItem,
  defaultValuesCompletionAcceptance,
  IUserPermission,
  TenderType,
  Unit,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';
import { BasicDialog } from '@/components/basic-dialog';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { useBoolean, useEntity, useScreenSize } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { Button } from 'devextreme-react';
import {
  DataGrid,
  IColumnProps as ColumnProps,
  Toolbar,
  Item,
  Editing,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  FilterRow,
  Scrolling,
  Paging,
  Pager,
  HeaderFilter,
  Search,
  SearchPanel,
  GroupPanel,
  DataGridTypes,
} from 'devextreme-react/data-grid';
import { convertMoneny, getRandomNumber, roundScaleNumber } from '@/lib/number';
import { Column } from 'devextreme/ui/data_grid';
import { cn } from '@/lib/utils';
import { customizeNumberCell, onCellPrepared } from '@/components/devex-data-grid';
import { useCallback } from 'react';
import { optionsPercent } from '../project';

const [defaultRow] = defaultValuesCompletionAcceptance.completionAcceptanceDetails;
const t = translationWithNamespace('completionAcceptance');

type CompletionAcceptanceEditableTableProps = {
  role?: IUserPermission;
  calculateForm: () => void;
};

const selectTextOnFocus = (args: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  args.element.querySelector('input.dx-texteditor-input')?.select();
};

const getCalculatedRowValues = (
  row: CompletionAcceptanceDetail,
  column?: keyof CompletionAcceptanceDetail
): Partial<CompletionAcceptanceDetail> => {
  row.previousPeriodCumulative = row.previousPeriodCumulative ?? 0;

  // Nếu người dùng đang nhập vào trường paymentQuantity, chỉ cập nhật totalAmount
  if (column === 'paymentQuantity') {
    const paymentQuantity = row.paymentQuantity ?? 0;
    return {
      ...row,
      totalAmount: convertMoneny(paymentQuantity * (row.price ?? 0)),
    };
  }

  const previousPeriodCumulative = row.previousPeriodCumulative ?? 0; /// Lũy kế đến hết kỳ trước
  const previousPeriodCumulativePayment = row.previousPeriodCumulativePayment ?? 0; /// Lũy kế thanh toán đến hết kỳ trước
  const contractQuantity = row.contractQuantity ?? 0; /// Theo hợp đồng
  const executionInThisPeriod = row.executionInThisPeriod ?? 0; /// Thực hiện kỳ này
  const userEnteredPaymentQuantity = row.paymentQuantity; // Giá trị người dùng đã nhập

  const cumulativeForThisPeriod = previousPeriodCumulative + executionInThisPeriod; //Lũy kế đến hết kỳ này

  // Chỉ tính toán paymentQuantity tự động khi:
  // 1. Người dùng chưa nhập giá trị (null/undefined/0)
  // 2. Hoặc khi người dùng thay đổi executionInThisPeriod
  let paymentQuantity = userEnteredPaymentQuantity ?? 0;

  // Nếu đang thay đổi executionInThisPeriod hoặc chưa có giá trị paymentQuantity
  if (column === 'executionInThisPeriod' || !userEnteredPaymentQuantity) {
    paymentQuantity = executionInThisPeriod; /// Khối lượng thanh toán

    if (previousPeriodCumulativePayment + paymentQuantity > contractQuantity) {
      paymentQuantity = contractQuantity - previousPeriodCumulativePayment;
    }
  }

  return {
    paymentQuantity,
    cumulativeForThisPeriod,
    remainingQuantity: roundScaleNumber(contractQuantity - cumulativeForThisPeriod), ////  làm tròn 4 số không thì nó trừ nó bị lẻ
    totalAmount: convertMoneny(paymentQuantity * (row.price ?? 0)),
  };
};
// const format = ',##0.##';
// const editorOptions = {
//   format: format,
//   showSpinButtons: false,
// };

export const CompletionAcceptanceEditableTableDevextreme = ({
  role,
  calculateForm,
}: CompletionAcceptanceEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<CompletionAcceptance>();

  const [editableData, id, contractId] = useWatch({
    control,
    name: ['completionAcceptanceDetails', 'id', 'contractId'],
  });
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);

  const calculateFormWrapper = () => {
    setValue('completionAcceptanceDetails', [...editableData]);
    calculateForm();
  };

  const { list: contractors } = useEntity<Contractor>({
    model: 'contractor',
    queryKey: [QUERIES.CONTRACTOR],
  });
  const { list: constructionTasks, fetch: fetchConstructionTaskItems } =
    useEntity<ConstructionTask>({
      model: 'construction-task',
      queryKey: [QUERIES.CONSTRUCTION_TASK],
    });
  const { list: units, fetch: fetchUnits } = useEntity<Unit>({
    model: 'unit',
    queryKey: [QUERIES.UNIT],
  });
  const { fetch: fetchCostItems } = useEntity<CostItem>({
    model: 'cost-item',
    queryKey: [QUERIES.COST_ITEM],
  });
  const { fetch: fetchTenderTypes } = useEntity<TenderType>({
    model: 'tender-type',
    queryKey: [QUERIES.TENDER_TYPE],
  });
  const { fetch: fetchBiddingMethods } = useEntity<BiddingMethod>({
    model: 'bidding-method',
    queryKey: [QUERIES.BIDDING_METHOD],
  });
  const { fetch: fetchBiddingSectors } = useEntity<BiddingSector>({
    model: 'bidding-sector',
    queryKey: [QUERIES.BIDDING_SECTOR],
  });

  const completionAcceptanceEditableColumns: Column[] = [
    {
      cellTemplate: (container, options) => {
        const pageIndex = options.component.pageIndex();
        const pageSize = options.component.pageSize();
        const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
        container.textContent = serialNumber.toString();
      },
      caption: 'STT',
      fixed: true,
      fixedPosition: 'left',
      alignment: 'center',
      width: 50,
      dataField: 'serialNumber',
      allowSorting: false,
      allowFiltering: false,
      allowEditing: false,
      format: ',##0,##',
    },
    {
      dataField: 'constructionTaskId',
      caption: t('fields.completionAcceptanceDetails.constructionTaskId'),
      lookup: { dataSource: constructionTasks, displayExpr: 'name', valueExpr: 'id' },
      width: 200,
      setCellValue: (newData, value) => {
        newData.constructionTaskId = value;

        const task = constructionTasks.find(t => t.id === value);
        newData.constructionTaskName = task?.name;

        const unit = units.find(item => item.id === task?.unitId);
        newData.unitId = unit?.id;
        newData.unitName = unit?.name || '';
      },
      validationRules: [{ type: 'required' }],
    },
    {
      dataField: 'contractorId',
      caption: t('fields.completionAcceptanceDetails.contractorId'),
      lookup: { dataSource: contractors, displayExpr: 'name', valueExpr: 'id' },
      width: 120,
      validationRules: [{ type: 'required' }],
    },
    {
      dataField: 'unitId',
      caption: t('fields.completionAcceptanceDetails.unitId'),
      lookup: { dataSource: units, displayExpr: 'name', valueExpr: 'id' },
      width: 120,
    },

    {
      dataField: 'price',
      caption: t('fields.completionAcceptanceDetails.price'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(0)({ value: e.value }),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'contractQuantity',
      caption: t('fields.completionAcceptanceDetails.contractQuantity'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      allowEditing: false,
      width: 120,
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'previousPeriodCumulative',
      caption: t('fields.completionAcceptanceDetails.previousPeriodCumulative'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'previousPeriodCumulativePayment',
      caption: t('fields.completionAcceptanceDetails.previousPeriodCumulativePayment'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'executionInThisPeriod',
      caption: t('fields.completionAcceptanceDetails.executionInThisPeriod'),
      dataType: 'number',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      format: '#,##0.00',
      setCellValue: (newData, value, row) => {
        const { paymentQuantity, cumulativeForThisPeriod, remainingQuantity, totalAmount } =
          getCalculatedRowValues(
            { ...row, executionInThisPeriod: value } as CompletionAcceptanceDetail,
            'executionInThisPeriod'
          );
        newData.executionInThisPeriod = value;
        newData.paymentQuantity = paymentQuantity;
        newData.cumulativeForThisPeriod = cumulativeForThisPeriod;
        newData.remainingQuantity = remainingQuantity;
        newData.totalAmount = totalAmount;
      },
      width: 120,
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'cumulativeForThisPeriod',
      caption: t('fields.completionAcceptanceDetails.cumulativeForThisPeriod'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      width: 120,
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'remainingQuantity',
      caption: t('fields.completionAcceptanceDetails.remainingQuantity'),
      dataType: 'number',
      format: '#,##0.00',
      allowEditing: false,
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      width: 120,
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'paymentQuantity',
      caption: t('fields.completionAcceptanceDetails.paymentQuantity'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      setCellValue: (newData, value, row) => {
        const { paymentQuantity, cumulativeForThisPeriod, remainingQuantity, totalAmount } =
          getCalculatedRowValues(
            { ...row, paymentQuantity: value } as CompletionAcceptanceDetail,
            'paymentQuantity'
          );
        newData.executionInThisPeriod = value;
        newData.paymentQuantity = paymentQuantity;
        newData.cumulativeForThisPeriod = cumulativeForThisPeriod;
        newData.remainingQuantity = remainingQuantity;
        newData.totalAmount = totalAmount;
      },
      width: 120,
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'startTime',
      caption: t('fields.completionAcceptanceDetails.startTime'),
      dataType: 'date',
      width: 120,
    },
    {
      dataField: 'endTime',
      caption: t('fields.completionAcceptanceDetails.endTime'),
      dataType: 'date',
      width: 120,
    },
    {
      dataField: 'totalAmount',
      caption: t('fields.completionAcceptanceDetails.totalAmount'),
      dataType: 'number',
      // format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL)({ value: e.value }),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'note',
      caption: t('fields.note'),
      dataType: 'string',
      width: 150,
    },
  ];

  const { height } = useScreenSize();

  const onRowInserting = useCallback((e: DataGridTypes.RowInsertingEvent) => {
    e.data.id = -getRandomNumber();
  }, []);

  return (
    <div>
      <DataGrid
        keyExpr={'id'}
        id={TABLES.COMPLETION_ACCEPTANCE_DETAIL}
        dataSource={editableData}
        columnAutoWidth
        allowColumnResizing
        columnResizingMode="widget"
        allowColumnReordering
        showBorders
        showColumnLines
        showRowLines
        wordWrapEnabled
        hoverStateEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        repaintChangesOnly
        remoteOperations={false}
        className={cn(
          'column-header-wrap',
          'max-h-[calc(100vh-9.8rem-32.8px)]',
          height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
        )}
        onInitNewRow={e => {
          e.data = { ...defaultRow };
        }}
        onRowInserting={onRowInserting}
        onRowRemoved={calculateFormWrapper}
        onRowInserted={calculateFormWrapper}
        onRowUpdated={calculateFormWrapper}
        columns={completionAcceptanceEditableColumns}
        onEditorPreparing={e => {
          if (e.parentType !== 'dataRow') {
            return;
          }

          switch (e.dataField) {
            case 'unitId': {
              const rowData = e.row?.data as CompletionAcceptanceDetail;
              const constructionTask = constructionTasks.find(
                item => item.id === rowData.constructionTaskId
              );
              e.editorOptions.dataSource = units.filter(
                item => item.id === constructionTask?.unitId
              );
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
            }
            case 'executionInThisPeriod':
            case 'cumulativeForThisPeriod':
            case 'paymentQuantity':
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
          }
          e.editorOptions.step = 0;
        }}
        onCellPrepared={e => {
          onCellPrepared(e);
        }}
      >
        <Editing
          mode="cell"
          allowUpdating={role?.isCreate || role?.isUpdate}
          allowDeleting={role?.isCreate || role?.isUpdate}
          allowAdding={role?.isCreate || role?.isUpdate}
          confirmDelete={false}
          useIcons
          newRowPosition="last"
        />
        <ColumnChooser enabled mode="select" height="45rem">
          <ColumnChooserSearch enabled />
          <ColumnChooserSelection allowSelectAll selectByClick recursive />
        </ColumnChooser>
        <FilterRow visible showOperationChooser />
        <Scrolling mode="standard" rowRenderingMode="standard" />
        <Paging enabled defaultPageSize={10} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={[5, 10, 50, 100]}
        />
        <HeaderFilter visible>
          <Search enabled mode="contains" />
        </HeaderFilter>
        <SearchPanel visible />
        <GroupPanel visible />
        <Toolbar>
          {errors.completionAcceptanceDetails?.message && (
            <ErrorMessage message={errors.completionAcceptanceDetails?.message} />
          )}
          <Item location="before">
            <Button
              stylingMode="text"
              icon="download"
              text={downloadTemplateLabel}
              type="default"
              onClick={() => {
                window.open(`/templates/mau_import_nghiem_thu_thuc_hien.xlsx`);
              }}
            />
          </Item>
          <Item location="before" locateInMenu="auto">
            <Button
              stylingMode="text"
              icon="upload"
              text="Import Excel"
              type="default"
              onClick={toggleImportForm}
            />
          </Item>

          <Item location="after" name="addRowButton" />
          {(role?.isCreate || role?.isUpdate) && (
            <Item location="after">
              <Button
                icon="trash"
                hint={'Xóa tất cả'}
                onClick={() => {
                  setValue('completionAcceptanceDetails', []);
                  setValue('totalAmount', 0);
                }}
              />
            </Item>
          )}
          <Item location="after" name="columnChooserButton" />
        </Toolbar>
      </DataGrid>

      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<CompletionAcceptanceDetail>
          onApply={data => {
            setValue(
              'completionAcceptanceDetails',
              data.map(item => {
                const row = Object.keys(item).reduce((acc, curr) => {
                  return { ...acc, ...getCalculatedRowValues(item, curr as keyof typeof item) };
                }, {});
                return {
                  ...defaultRow,
                  ...item,
                  ...row,
                  id: item.id,
                  completionAcceptanceId: id,
                  contractAppendixDetailId: null,
                  isOutSide: false,
                  startTime: item.startTime ? new Date(item.startTime) : null,
                  endTime: item.endTime ? new Date(item.endTime) : null,
                };
              })
            );
            calculateForm();
            toggleImportForm();
          }}
          importModel="completion-acceptance"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.COMPLETION_ACCEPTANCE}
          useDevDataGrid={true}
          devDataGridColumns={completionAcceptanceEditableColumns
            .filter(col => col.dataField !== 'serialNumber')
            .map(col => {
              return { ...col } as ColumnProps;
            })}
          professionColumns={completionAcceptanceEditableColumns.map(col => {
            return {
              field: col.dataField,
              header: col.caption,
            };
          })}
          onImported={() => {
            fetchConstructionTaskItems({});
            fetchUnits({});
            fetchCostItems({});
            fetchTenderTypes({});
            fetchBiddingMethods({});
            fetchBiddingSectors({});
          }}
          additionalFormValues={[{ key: 'refId', value: contractId?.toString() || '' }]}
        />
      </BasicDialog>
    </div>
  );
};
