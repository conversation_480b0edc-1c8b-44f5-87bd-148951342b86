/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { PERMISSIONS, QUERIES, viewLabel } from '@/constant';
import { useAuth, useDataTable, usePermission } from '@/hooks';
import { callbackWithTimeout } from '@/lib/utils';
import { createQueryPaginationWithCustomPathFn } from '@/services';
import { WeeklyProjectSchedulePlan, WeeklyProjectSchedulePlanDetail } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { Label } from '@radix-ui/react-label';
import { DateBox, Button } from 'devextreme-react';
import { endOfYear, startOfYear } from 'date-fns';
import { WeeklyProjectSchedulePlanEditableTableDevextreme } from './weekly-project-schedule-plan-editable-table-devextreme';

export const WeeklyProjectSchedulePlanDataTable = () => {
  const { t } = useTranslation('weeklyProjectSchedulePlan');
  const { projects } = useAuth();
  const [year, setYear] = useState<Date>(new Date());
  const role = usePermission(PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN);
  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<WeeklyProjectSchedulePlan, PeriodFilter>({
    invalidateKey: [QUERIES.WEEKLY_PROJECT_SCHEDULE_PLAN],
    initialQuery: {
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${projects.map(i => i.id).join(',') || 0})`,
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: ['weekly-project-schedule-plan/get-all-detail'],
    queryFn: () => {
      return createQueryPaginationWithCustomPathFn<WeeklyProjectSchedulePlanDetail>(
        'weekly-project-schedule-plan/get-all-detail'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'WeeklyProjectSchedulePlanTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
    enabled: false,
  });

  const methods = useForm<{ items: WeeklyProjectSchedulePlanDetail[] }>({
    defaultValues: { items: data?.items || [] },
  });

  useEffect(() => {
    methods.reset({ items: data?.items || [] });
  }, [data?.items, methods]);

  useEffect(() => {
    if (!projects) return;
    queryListMethods.addOrReplaceFilterColumn({
      column: 'ProjectId',
      expression: 'IN',
      keySearch: `(${projects.map(i => i.id).join(',') || 0})`,
    });
    callbackWithTimeout(refetch);
  }, [projects, refetch]);

  return (
    <PageLayout header={t('page.header')}>
      <div className="flex flex-col gap-x-4 sm:flex-row sm:items-center">
        <Label htmlFor="year">{t('fields.year')}</Label>
        <DateBox
          value={year}
          id="year"
          type="date"
          calendarOptions={{
            maxZoomLevel: 'decade',
            minZoomLevel: 'decade',
          }}
          displayFormat={'year'}
          onValueChanged={e => {
            if (e.value) {
              const date = e.value as Date;
              queryListMethods.addOrReplaceFilterDateColumn(
                'BudgetYear',
                startOfYear(date),
                endOfYear(date)
              );
              setYear(date);
            }
          }}
          pickerType="calendar"
          focusStateEnabled={false}
        />
        <Button
          text={viewLabel}
          className="mt-3 w-full sm:mt-0 sm:w-fit"
          stylingMode="contained"
          type="default"
          icon="search"
          onClick={() => {
            callbackWithTimeout(() => {
              queryListMethods.addOrReplaceFilterDateColumn(
                'BudgetYear',
                startOfYear(year),
                endOfYear(year)
              );
              queryListMethods.addOrReplaceFilterColumn({
                column: 'ProjectId',
                expression: 'IN',
                keySearch: `(${projects.map(i => i.id).join(',') || 0})`,
              });
              return refetch().then(() => {
                methods.reset({ items: data?.items || [] });
              });
            });
          }}
        />
      </div>
      <Form {...methods}>
        <form autoComplete="off">
          <WeeklyProjectSchedulePlanEditableTableDevextreme
            role={role}
            budgetYearValue={year.getFullYear()}
          />
        </form>
      </Form>
    </PageLayout>
  );
};
