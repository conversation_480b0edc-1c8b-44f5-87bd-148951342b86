import { PageLayout } from '@/components/page-layout';
import { Form<PERSON>ield, FormLabel } from '@/components/ui/form';
import { enterLabel, TABLES } from '@/constant';
import {
  defaultValuesProjectDisbursementInitial,
  Project,
  ProjectDisbursementInitial,
  ProjectTabChildrenProps,
} from '@/types';
import { Button } from 'devextreme-react';
import { SyntheticEvent } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { InputNumber } from '@/components/ui/input';
import { getRandomNumber } from '@/lib/number';
import { DevexDataGridEditable } from '@/components/devex-data-grid';
import { Column, Item, SearchPanel } from 'devextreme-react/data-grid';
import { optionsMoney } from './options';

const defaultRow = defaultValuesProjectDisbursementInitial;
const selectTextOnFocus = (args: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  args.element.querySelector('input.dx-texteditor-input')?.select();
};

export const ProjectDisbursementInitialsForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { control, setValue } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectDisbursementInitials', 'id'],
  });

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalInvestmentCapital"
              htmlFor="initialTotalInvestmentCapital"
            >
              {t('fields.initialTotalInvestmentCapital')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalInvestmentCapital"
              label={t('fields.initialTotalInvestmentCapital')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalInvestmentCapital')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementCompensationl"
              htmlFor="initialTotalFundingRequirementCompensationl"
              title={t('fields.initialTotalFundingRequirementCompensationlLong')}
            >
              {t('fields.initialTotalFundingRequirementCompensationl')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementCompensationl"
              label={t('fields.initialTotalFundingRequirementCompensationl')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementCompensationl')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementConstructionConsulting"
              htmlFor="initialTotalFundingRequirementConstructionConsulting"
              title={t('fields.initialTotalFundingRequirementConstructionConsultingLong')}
            >
              {t('fields.initialTotalFundingRequirementConstructionConsulting')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementConstructionConsulting"
              label={t('fields.initialTotalFundingRequirementConstructionConsulting')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementConstructionConsulting')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementOther"
              htmlFor="initialTotalFundingRequirementOther"
              title={t('fields.initialTotalFundingRequirementOtherLong')}
            >
              {t('fields.initialTotalFundingRequirementOther')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementOther"
              label={t('fields.initialTotalFundingRequirementOther')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementOther')}`}
              />
            </FormField>
          </div>
        </div>

        <div className="col-span-1 xl:col-span-24">
          <DevexDataGridEditable
            id={TABLES.PROJECT_DISBURSEMENT_INITIALS}
            dataSource={editableData}
            editing={{
              mode: 'cell',
              allowUpdating: role?.isUpdate,
              allowDeleting: role?.isDelete,
              allowAdding: role?.isCreate,
              useIcons: true,
              confirmDelete: false,
              newRowPosition: 'last',
            }}
            wordWrapEnabled
            focusedRowEnabled
            autoNavigateToFocusedRow
            grouping={{ contextMenuEnabled: false }}
            groupPanel={{ visible: false }}
            repaintChangesOnly
            stateStoring={false}
            onEditorPreparing={e => {
              if (e.parentType !== 'dataRow') {
                return;
              }

              e.editorOptions.onFocusIn = selectTextOnFocus;
            }}
            columnFixing={{
              enabled: false,
            }}
            onRowInserting={e => {
              e.data = {
                ...(e.data as ProjectDisbursementInitial),
                id: -getRandomNumber(),
              };
            }}
            onSavedCustom={dataSource => {
              setValue('projectDisbursementInitials', dataSource as ProjectDisbursementInitial[]);
            }}
            onInitNewRow={e => {
              e.data = { ...defaultRow, projectId };
            }}
            customToolbar={
              <>
                <Item name="addRowButton" />
              </>
            }
          >
            <SearchPanel visible={false} />
            {/* <Column
              dataField="index"
              caption="STT"
              dataType="number"
              format={',##0,##'}
              alignment="center"
              allowEditing={false}
              allowFiltering={false}
              allowSorting={false}
              fixed={false}
              fixedPosition="left"
            /> */}
            <Column
              dataField="disbursementYear"
              caption={t('fields.projectDisbursementInitials.disbursementYear')}
              dataType="date"
              format={'dd/MM/yyyy'}
              editorOptions={{
                displayFormat: 'dd/MM/yyyy',
                pickerType: 'calendar',
                // calendarOptions: {
                //   maxZoomLevel: 'decade',
                //   minZoomLevel: 'decade',
                // },
              }}
            />
            <Column
              dataField="totalCompensationDisbursement"
              caption={t('fields.projectDisbursementInitials.totalCompensationDisbursement')}
              {...optionsMoney}
            />
            <Column
              dataField="totalConstructionConsultingDisbursement"
              caption={t(
                'fields.projectDisbursementInitials.totalConstructionConsultingDisbursement'
              )}
              {...optionsMoney}
            />
            <Column
              dataField="totalOtherDisbursement"
              caption={t('fields.projectDisbursementInitials.totalOtherDisbursement')}
              {...optionsMoney}
            />
          </DevexDataGridEditable>
        </div>
      </div>
    </PageLayout>
  );
};
