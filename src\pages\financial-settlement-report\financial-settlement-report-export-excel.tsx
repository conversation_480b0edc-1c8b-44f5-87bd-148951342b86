import { sumProperty } from '@/lib/array';
import { toRoman } from '@/lib/numberToText';
import { FinancialSettlementReport } from '@/types';
import { Alignment, BorderStyle, Cell, Style, Workbook, Worksheet } from 'exceljs';

export const createFinancialSettlementReport = async (
  outputFileName: string = 'outputFile.xlsx',
  formValues: FinancialSettlementReport,
  inputFilePath: string = '/templates/financial-settlement-report/BaoCaoQuyetToanVonDauTu_template.xlsx'
) => {
  // 1. Lấy file từ đường dẫn tĩnh trong thư mục public
  // Đường dẫn tính từ gốc của trang web (do file nằm trong public)
  const response = await fetch(inputFilePath);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // 2. Lấy nội dung file dưới dạng ArrayBuffer
  const arrayBuffer = await response.arrayBuffer();

  // 3. S<PERSON> dụng exceljs để đọc <PERSON>rrayBuffer
  const workbook = new Workbook();
  await workbook.xlsx.load(arrayBuffer);

  // Cập nhật dữ liệu của sheet bìa
  editCoverSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 01
  editOneSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 02
  editTwoSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 03
  editThreeSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 04
  editFourthSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 05
  editFiveSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 06
  editSixSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 07
  editSevenSheet(workbook, formValues);

  // Cập nhật dữ liệu của sheet 08
  editEightSheet(workbook, formValues);

  // 4. Export excel
  workbook.xlsx
    .writeBuffer()
    .then(buffer => {
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = outputFileName;
      a.click();
      window.URL.revokeObjectURL(url);
    })
    .catch(error => console.log('error:', error));
};

// chỉnh sửa sheet Bìa
function editCoverSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Lấy dữ liệu của  sheet Bìa
  const worksheet = workbook.getWorksheet('Bia');

  // Trường hợp không tồn tại sheet Bia thì ngưng xử lý
  if (!worksheet) return;

  const cells = ['B3', 'B21'] as const;
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    B3: {
      projectOwnerName: String(formValues.projectOwnerName)?.toUpperCase(),
    },
    B21: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    const newValue = cellContents[cell];
    if (!newValue) return;
    updateCellValue(worksheet, cell, newValue);
  });
}

// Chỉnh sửa sheet 01
function editOneSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 01
  const worksheet = workbook.getWorksheet('01');
  // Trường hợp không tồn tại sheet Bia thì ngưng xử lý
  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['E5', 'A7', 'A8', 'A11', 'A38', 'A41', 'A44', 'A45', 'A54', 'C54', 'E54'];
  // Danh sách cột công thức
  const formulaColumns = ['C', 'D', 'E'];
  // Viền mặc định
  const defaultBorderStyle = {
    top: { style: 'thin' as BorderStyle },
    left: { style: 'thin' as BorderStyle },
    bottom: { style: 'thin' as BorderStyle },
    right: { style: 'thin' as BorderStyle },
  };
  // font mặc định
  const defaultFont = { bold: false, name: 'Times New Roman', size: 12 };
  const totalApprovedFinalProjectBudget = sumProperty(
    formValues.financialSettlementReportBudgetFunds,
    'approvedFinalProjectBudget'
  );

  // Nội dung cần chỉnh sửa
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    E5: {
      year: new Date().getFullYear().toString(),
    },
    A7: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    A8: {
      totalApprovedFinalProjectBudget: totalApprovedFinalProjectBudget.toLocaleString('en-US', {
        minimumFractionDigits: 0, // Bỏ phần thập phân nếu không cần
        maximumFractionDigits: 0, // Bỏ phần thập phân nếu không cần
      }),
    },
    A11: {
      projectOwnerName: String(formValues.projectOwnerName)?.toUpperCase(),
    },
    A38: {
      damageCosts: formValues.damageCosts,
    },
    A41: {
      projectProgressStatus: formValues.projectProgressStatus,
    },
    A44: {
      projectImplementationReviewAndEvaluation: formValues.projectImplementationReviewAndEvaluation,
    },
    A45: {
      proposal: formValues.proposal,
    },
    A54: {
      formCreator: formValues.formCreator,
    },
    C54: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    E54: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });

  // Danh sách dòng cần chỉnh sửa
  const rows = [36, 31, 23];
  // Danh sách dòng cần merge của bảng Giá trị tài sản hình thành sau đầu tư
  // Hai dòng mặc định gồm dòng header và dòng tổng số
  const mergeRages: number[] = [34, 35];

  // Xử lý cập nhật dữ liệu của từng dòng
  const rowContent: Record<number, (worksheet: Worksheet, startRow: number) => void> = {
    36: function (worksheet: Worksheet, startRow: number) {
      let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn
      const sortedGroups = sortAndAddOrdinal(formValues.financialSettlementReportAssetGroups, 'id');

      sortedGroups.forEach(group => {
        // chèn dòng trống
        worksheet.insertRow(currentRowNumber, [
          group.ordinalNumber,
          group.groupName,
          '',
          group.totalAmount,
          '',
        ]);

        // Lưu index của dòng vừa được insert
        mergeRages.push(currentRowNumber);

        // Cập nhật giá trị của cột A
        const cellA = worksheet.getCell(`A${currentRowNumber}`);
        cellA.alignment = { vertical: 'middle', horizontal: 'center' };
        cellA.border = defaultBorderStyle;
        cellA.font = defaultFont;

        worksheet.unMergeCells(`B${currentRowNumber}`);
        worksheet.unMergeCells(`D${currentRowNumber}`);

        // Cập nhật giá trị của cột B
        const cellB = worksheet.getCell(`B${currentRowNumber}`);
        cellB.alignment = { vertical: 'top', horizontal: 'left' };
        cellB.border = defaultBorderStyle;
        cellB.font = defaultFont;

        // Cập nhật giá trị của cột D
        const cellD = worksheet.getCell(`D${currentRowNumber}`);
        // Đặt định dạng số cho ô theo mẫu "#,##0"
        cellD.numFmt = '#,##0';
        cellD.alignment = { vertical: 'top', horizontal: 'center' };
        cellD.border = defaultBorderStyle;
        cellD.font = defaultFont;

        currentRowNumber++;
      });
    },
    31: function (worksheet: Worksheet, startRow: number) {
      let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn

      // Get về độ lệch của dòng = số dòng được thêm tại bảng vốn đầu tư
      const offsetRow = formValues.financialSettlementReportBudgetFunds.length;

      // Sắp xếp dữ liệu theo id
      const sortedGroups = sortAndAddOrdinal<any>(
        formValues.financialSettlementReportInvestmentCosts,
        'id'
      );

      // Định nghĩa style của columns
      const columnDefinitions = [
        {
          key: 'ordinalNumber',
          style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'costItemTypeName',
          style: { alignment: { horizontal: 'left', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'approvedFinalProjectBudget',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'proposedSettlementValue',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'proposedLiquidationAmount',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
      ];

      // Xử lý chèn dòng
      sortedGroups.forEach(group => {
        // chèn dòng
        const newRow = worksheet.insertRow(currentRowNumber, [
          group.ordinalNumber,
          group.costItemTypeName,
          group.approvedFinalProjectBudget,
          group.proposedSettlementValue,
          0,
        ]);
        newRow.font = { bold: false, name: 'Times New Roman', size: 12 };
        newRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          cell.numFmt = '#,##0';
          cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          // tạo công thức cho cột E
          if (cell.address === `E${currentRowNumber}`) {
            cell.value = {
              formula: `D${currentRowNumber + offsetRow}-C${currentRowNumber + offsetRow}`,
              result: 0,
            };
          }
        });
        currentRowNumber++;
      });

      // Tạo công thức cho dòng Tổng số
      const summaryRow = worksheet.getRow(startRow - 1);
      const summaryColumns = ['C', 'D', 'E'];
      summaryColumns.forEach(column => {
        summaryRow.getCell(column).value = {
          formula: `SUM(${column}${startRow + offsetRow}:${column}${currentRowNumber + offsetRow - 1})`,
          result: 0,
        };
      });
    },
    23: function (worksheet: Worksheet, startRow: number) {
      let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn

      // Định nghĩa style của columns
      const columnDefinitions = [
        {
          key: 'ordinalNumber',
          style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'budgetFundName',
          style: {
            alignment: { horizontal: 'left', vertical: 'middle', wrapText: true, indent: 1 },
          },
        },
        {
          key: 'approvedFinalProjectBudget',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'allocatedPlannedCapital',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
        {
          key: 'disbursedFunds',
          style: { alignment: { horizontal: 'right', vertical: 'middle', wrapText: true } },
        },
      ];

      formValues.financialSettlementReportBudgetFunds.forEach(budgetFund => {
        // chèn dòng
        const newRow = worksheet.insertRow(currentRowNumber, [
          '',
          `+ ` + budgetFund.budgetFundName,
          budgetFund.approvedFinalProjectBudget,
          budgetFund.allocatedPlannedCapital,
          budgetFund.disbursedFunds,
        ]);
        newRow.height = 15.75;
        newRow.font = { bold: false, name: 'Times New Roman', size: 12 };
        newRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          if (colNumber === 2) cell.numFmt = '@';
          if (colNumber >= 3 && colNumber <= 5) cell.numFmt = '#,##0';

          cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
          cell.border = defaultBorderStyle;
        });
        currentRowNumber++;
      });

      // danh sách dòng để tạo công thức
      const formulaRows = [16, 18];

      const formulaRowProcess: Record<number, (worksheet: Worksheet, startRow: number) => void> = {
        18: function (worksheet: Worksheet, formulaRowIndex: number) {
          const targetRow = worksheet.getRow(formulaRowIndex);
          formulaColumns.forEach(column => {
            const sumFormula = `SUM(${column}${startRow}:${column}${currentRowNumber - 1})`;
            targetRow.getCell(column).value = { formula: sumFormula, result: 0 };
          });
        },
        16: function (worksheet: Worksheet, formulaRowIndex: number) {
          const targetRow = worksheet.getRow(formulaRowIndex);
          formulaColumns.forEach(column => {
            // Vị trí bắt đầu là cố định nên gán cứng là 18
            const addFormula = `${column}18+${column + (currentRowNumber + 1)}`;
            targetRow.getCell(column).value = { formula: addFormula, result: 0 };
          });
        },
      };

      formulaRows.forEach((row: number) => {
        formulaRowProcess[row](worksheet, row);
      });

      // Thực hiện merge dòng cho bảng Giá trị tài sản hình thành sau đầu tư
      // Độ lệch được tính bảng tổng số dòng được insert tại bảng của mục I và II
      const offset =
        formValues.financialSettlementReportBudgetFunds.length +
        formValues.financialSettlementReportInvestmentCosts.length;
      if (mergeRages.length === 0) return;
      mergeRages.forEach(rowwIndex => {
        worksheet.mergeCells(`B${rowwIndex + offset}:C${rowwIndex + offset}`);
        worksheet.mergeCells(`D${rowwIndex + offset}:E${rowwIndex + offset}`);
      });
    },
  };

  // Cập nhật dữ liệu của từng dòng
  rows.forEach(row => {
    if (!rowContent[row]) return;
    if (typeof rowContent[row] === 'function') {
      rowContent[row](worksheet, row);
    }
  });
}

// Chỉnh sửa sheet 02
function editTwoSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 02
  const worksheet = workbook.getWorksheet('02');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A6', 'D10', 'B16', 'E16'];

  // Nội dung cần chỉnh sửa của ô
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A6: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    D10: {
      year: new Date().getFullYear().toString(),
    },
    B16: {
      formCreator: formValues.formCreator,
    },
    E16: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });

  // Nhóm dữ liệu theo Loại văn bản (groupDocId)
  const groupedResult = groupData(
    formValues.financialSettlementReportDocumentLists, // Dữ liệu đầu vào
    'groupDocId', // Tên thuộc tính để nhóm
    ['groupDocId', 'groupDocName'], // Thuộc tính cho group cha
    [
      'id',
      'financialSettlementReportId',
      'noDoc',
      'agencyId',
      'agencyName',
      'dateCreate',
      'content',
    ], // Thuộc tính cho các phần tử con
    'documents' // Tên tùy chọn cho mảng con (thay vì 'items' mặc định)
  );

  // Sắp xếp dữ liệu
  const documentGroupedByGroupDocId = sortGroupedData({
    groupedData: JSON.parse(JSON.stringify(groupedResult)),
    parentSortKey: 'groupDocId',
    parentOrdinalFormatter: toRoman, // Dùng số La Mã cho cha
    childrenArrayKey: 'documents',
    childSortKey: 'id',
  });

  const startRow = 10; // Dòng bắt đầu chèn dữ liệu
  let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn

  const columnDefinitions = [
    {
      key: 'ordinal',
      header: 'TT',
      width: 6,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'groupDocName',
      header: 'Tên văn bản',
      width: 33,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'noDoc',
      header: 'Số',
      width: 18,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'dateCreate',
      header: 'ngày ban hành',
      width: 17,
      style: {
        numFmt: `"ngày "dd/mm/yyyy`,
        alignment: { horizontal: 'center', vertical: 'middle', wrapText: true },
      },
    },
    {
      key: 'agencyName',
      header: 'Cơ quan ban hành',
      width: 15,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'approvedTotal',
      header: 'Tổng giá trị được duyệt',
      width: 15,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'note',
      header: 'Ghi chú',
      width: 11,
      style: { alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } },
    },
  ];

  // Thêm dữ liệu vào sheet 02
  documentGroupedByGroupDocId.forEach((group: Record<string, any>) => {
    // Chuẩn bị dữ liệu cho dòng cha (dạng mảng)
    const parentRowDataArray = [
      group['ordinalNumber'], // ô TT
      group['groupDocName'], // ô Tên văn bản
      '', // ô Số
      '', // ngày ban hành
      '', // ô cơ quan ban hành
      '', // Tổng giá trị được duyệt
      '', // ô Ghi chú
    ];
    // Chèn dòng cha
    worksheet.insertRow(currentRowNumber, parentRowDataArray);
    const parentRow = worksheet.getRow(currentRowNumber);

    // Định dạng dòng cha
    parentRow.font = { bold: true, name: 'Times New Roman', size: 12 };
    parentRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
      // Căn lề left-top cho cột B, C, D, E, F, G
      if (colNumber >= 2 && colNumber <= 7) {
        cell.alignment = { horizontal: 'left', vertical: 'top' };
      }
      if (colNumber === 3) cell.border.right = {};
      if (colNumber === 4) cell.border.left = {};
    });
    currentRowNumber++; // Tăng số dòng

    // Chèn các dòng con (documents) nếu có
    if (group.documents && Array.isArray(group.documents)) {
      group.documents.forEach(document => {
        // Chuẩn bị dữ liệu cho dòng con (dạng mảng)
        const childRowDataArray = [
          document.ordinalNumber, // ô TT
          document.content, // ô Tên văn bản
          document.noDoc, // ô Số
          new Date(document.dateCreate as string), // ngày ban hành
          document.agencyName, // ô cơ quan ban hành
          '', // Tổng giá trị được duyệt
          '', // Ghi chú
        ];
        // Chèn dòng con
        worksheet.insertRow(currentRowNumber, childRowDataArray);
        const childRow = worksheet?.getRow(currentRowNumber);

        // Định dạng dòng con
        childRow.font = { bold: false, name: 'Times New Roman', size: 12 };
        childRow.getCell(2).alignment = { wrapText: true }; // Cell 'B' (Nội dung)
        childRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) };
          if (colNumber === 2 || colNumber === 5)
            cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
          if (colNumber === 3) cell.border.right = {};
          if (colNumber === 4) cell.border.left = {};
        });
        currentRowNumber++; // Tăng số dòng
      });
    }
  });
}

// Chỉnh sửa sheet 03
function editThreeSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  const worksheet = workbook.getWorksheet('03');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A8', 'A9', 'A11', 'C26', 'I26', 'B33', 'D33'];

  // Danh sách dòng cần chỉnh sửa
  const rows = [21, 20];

  // Border mặc định
  const defaultBorderStyle = {
    top: { style: 'thin' as BorderStyle },
    left: { style: 'thin' as BorderStyle },
    bottom: { style: 'thin' as BorderStyle },
    right: { style: 'thin' as BorderStyle },
  };

  // Định nghĩa style của columns
  const columnDefinitions = [
    { key: 'ordinalNumber', style: { alignment: { horizontal: 'center', vertical: 'middle' } } },
    {
      key: 'budgetFundName',
      style: { alignment: { numFmt: '@', horizontal: 'left', vertical: 'middle', wrapText: true } },
    },
    {
      key: 'poPlannedCapital',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'poDisbursedTotal',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'poDisbursedAdvance',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'amountPlannedCapital',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'amountAllocatedTotal',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'approvedFinalProjectBudget',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'amountAllocatedCompletedWorkload',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'amountAllocatedAdvance',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'difference',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
    {
      key: 'notes',
      style: { numFmt: '#,##0', alignment: { horizontal: 'right', vertical: 'middle' } },
    },
  ];

  // Nội dung cần chỉnh sửa của ô
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A9: {
      code: formValues.projectCode,
    },
    A8: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    A11: {
      projectOwnerName: String(formValues.projectOwnerName)?.toUpperCase(),
    },
    C26: {
      year: new Date().getFullYear().toString(),
    },
    I26: {
      year: new Date().getFullYear().toString(),
    },
    B33: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    D33: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // --- Danh sách các thuộc tính cần tính tổng ---
  const propertiesToSum: string[] = [
    'poPlannedCapital',
    'poDisbursedTotal',
    'poDisbursedCompletedWorkload',
    'poDisbursedAdvance',
    'amountPlannedCapital',
    'amountAllocatedTotal',
    'amountAllocatedCompletedWorkload',
    'amountAllocatedAdvance',
    'difference',
  ];

  // Group dữ liệu theo nguồn vốn và tính tổng của các năm
  const groupedAndSummedResult = groupAndSum(
    [...formValues.financialSettlementReportDataReconciliationTables],
    'budgetFundName',
    propertiesToSum
  );

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });

  // Xử lý cập nhật dữ liệu của từng dòng
  const rowContent: Record<number, (worksheet: Worksheet, startRow: number) => void> = {
    21: function (worksheet: Worksheet, startRow: number) {
      let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn

      // Tạo dòng Luỹ kế từ khởi công
      groupedAndSummedResult.forEach(group => {
        const rowValues = [
          '',
          '+ ' + String(group['budgetFundName']),
          Number(group['totalpoPlannedCapital']),
          Number(group['totalpoDisbursedTotal']),
          Number(group['totalpoDisbursedCompletedWorkload']),
          Number(group['totalpoDisbursedAdvance']),
          Number(group['totalamountPlannedCapital']),
          Number(group['totalamountAllocatedTotal']),
          Number(group['totalamountAllocatedCompletedWorkload']),
          Number(group['totalamountAllocatedAdvance']),
          Number(group['totaldifference']),
          '', // Ghi chú
        ];
        // chèn dòng
        const newRow = worksheet.insertRow(currentRowNumber, rowValues);
        newRow.font = { bold: false, name: 'Times New Roman', size: 12 };
        newRow.eachCell({ includeEmpty: true }, (cell: Cell, colNumber: number) => {
          cell.border = { ...defaultBorderStyle };
          cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
        });
        currentRowNumber++;
      });

      // Tạo dòng Chi tiết theo năm.
      const groupByBudgetYear = groupData(
        formValues.financialSettlementReportDataReconciliationTables,
        'budgetYear', // Năm ngân sách
        ['budgetYear'],
        [
          'id',
          'budgetFundName',
          'financialSettlementReportId',
          'budgetFundId',
          'poPlannedCapital',
          'poDisbursedTotal',
          'poDisbursedCompletedWorkload',
          'poDisbursedAdvance',
          'amountPlannedCapital',
          'amountAllocatedTotal',
          'amountAllocatedCompletedWorkload',
          'amountAllocatedAdvance',
          'difference',
          'notes',
        ]
      );

      const sortBudgetYearById = sortGroupedData({
        groupedData: JSON.parse(JSON.stringify(groupByBudgetYear)),
        parentSortKey: 'budgetYear',
        childrenArrayKey: 'items',
        childSortKey: 'id',
      });

      const parentRows: number[] = [];
      // Lấy vị trí của dòng chi tiết theo năm
      const detailByYearRowNumber = currentRowNumber;
      // Di chuyển vị trí đến vị trí của dòng cha
      let currentParentRowNumber = currentRowNumber + 1;
      sortBudgetYearById.forEach((budgetYear: Record<string, any>) => {
        const rowValues = [
          '2.' + String(budgetYear.ordinalNumber),
          'Năm ' + String(budgetYear.budgetYear),
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
        ];
        // chèn dòng cha
        const newParentRow = worksheet.insertRow(currentParentRowNumber, rowValues);
        newParentRow.font = { italic: true, bold: true, name: 'Times New Roman', size: 12 };

        // Thêm vị trí dòng cha vào danh sách
        parentRows.push(currentParentRowNumber);
        currentParentRowNumber++;

        // chèn dòng con
        if (budgetYear.items.length > 0) {
          let currentChildRowNumber = currentParentRowNumber;
          const children: Record<string, any>[] = [...budgetYear.items];
          children.forEach((child: Record<string, any>) => {
            const chidRowValues = [
              '',
              '+ ' + child.budgetFundName,
              child.poPlannedCapital,
              child.poDisbursedTotal,
              child.poDisbursedCompletedWorkload,
              child.poDisbursedAdvance,
              child.amountPlannedCapital,
              child.amountAllocatedTotal,
              child.amountAllocatedCompletedWorkload,
              child.amountAllocatedAdvance,
              child.difference,
              child.notes,
            ];
            const newChildRow = worksheet.insertRow(currentChildRowNumber, chidRowValues);
            newChildRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
              cell.border = defaultBorderStyle;
              cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
              cell.font = { bold: false, name: 'Times New Roman', size: 12 };
            });
            currentChildRowNumber++;
          });
          // Gắn style và tạo công thức cho dòng cha
          newParentRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
            // Get ký tự của cột
            const columnLetter = getColumnLetterFromString(cell.address);
            // Set viền mặt định cho ô
            cell.border = defaultBorderStyle;
            // Lấy style từ definition
            cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) };
            // Bỏ qua 2 cột đầu tiên và cột ghi chú
            if (colNumber <= 2 || colNumber === 12) return;

            let cellFormula = columnLetter + currentParentRowNumber.toString();
            if (budgetYear.items.length > 1) {
              const start = columnLetter + currentParentRowNumber.toString();
              const end =
                columnLetter + (currentParentRowNumber + budgetYear.items.length - 1).toString();
              cellFormula = `SUM(${start}:${end})`;
            }
            cell.value = {
              formula: cellFormula,
              result: 0,
            };
          });

          currentParentRowNumber = currentChildRowNumber;
        }
      });
      // Get dòng Chi tiết theo năm
      const detailByYearRow = worksheet.getRow(detailByYearRowNumber);
      // Set style và tạo công thức cho dòng chi tiết theo năm
      detailByYearRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        if (colNumber > 2 && colNumber <= 12) {
          const columnLetter = getColumnLetterFromString(cell.address);
          const parentRowRage = parentRows
            .map(rowNumber => `${columnLetter}${rowNumber}`)
            .join(',');
          const totalFormula = `SUM(${parentRowRage})`;
          cell.value = { formula: totalFormula, result: undefined };
        }
      });
    },
    20: function (worksheet: Worksheet, startRow: number) {
      // Get dòng Luỹ kế từ khởi công
      const accumulatedFromStartConstructionRow = worksheet.getRow(startRow);
      accumulatedFromStartConstructionRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        cell.font = { italic: true, bold: true, name: 'Times New Roman', size: 12 };
        // Bỏ qua 2 ô đầu tiên và ô năm ngoài phạm vi
        if (colNumber === 1 || colNumber === 2 || colNumber > 11) return;

        // Get chữ cái của cột
        const columnLetter = getColumnLetterFromString(cell.address);
        // Tạo công thức
        const start = columnLetter + String(startRow + 1);
        const end = columnLetter + String(startRow + groupedAndSummedResult.length);
        cell.value = {
          formula: `SUM(${start}:${end})`,
          result: undefined,
        };
      });
    },
  };

  // Cập nhật dữ liệu của từng dòng
  rows.forEach(row => {
    if (!rowContent[row]) return;
    rowContent[row](worksheet, row);
  });
}

// Chỉnh sửa sheet04
function editFourthSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  const dataGroupedByCostItemTypeId = groupByCostItemTypeId(
    formValues.financialSettlementReportProposedSettlementInvestmentCosts
  );

  // Sắp xếp dữ liệu theo Tổng mức đầu tư dự án và cho loai chi phí khác nằm ở cuối danh sách
  const sortedData = orderByApprovedFinalProjectBudget(dataGroupedByCostItemTypeId);

  // Định nghĩa dòng làm tròn
  const roundRow: Record<string, any> = {
    costItemTypeId: 0,
    costItemTypeName: 'Làm tròn',
    totalApprovedFinalProjectBudget: 0,
    totalApprovedFinalEstimate: 0,
    totalProposedSettlementValue: 0,
    approvedFinalProjectBudgets: [], // Mảng con rỗng
    ordinalNumber: toRoman(sortedData.length + 1),
  };
  // Thêm dòng làm tròn vào dữ liệu đã được sắp xếp
  sortedData.push(roundRow);

  // Get worksheet 04
  const worksheet = workbook.getWorksheet('04');
  if (!worksheet) return;

  // Thay đổi giá trị của tên dự án
  const projectCell = worksheet.getCell('A6');
  projectCell.value = String(formValues.projectName)?.toUpperCase(); // Gán giá trị mới

  // Thay đổi giá trị của người lập biểu
  const formCreatorCell = worksheet.getCell('B19');
  formCreatorCell.value = String(formValues.formCreator); // Gán giá trị mới

  // Thay đổi giá trị của kế toán trưởng
  const headOfFinanceDepartmentCell = worksheet.getCell('C19');
  headOfFinanceDepartmentCell.value = String(formValues.headOfFinanceDepartment); // Gán giá trị mới

  // Thay đổi giá trị của kế toán trưởng
  const pmoDirectorCell = worksheet.getCell('E19');
  pmoDirectorCell.value = String(formValues.pmoDirector); // Gán giá trị mới

  const startRow = 9; // Dòng bắt đầu chèn dữ liệu
  let currentRowNumber = startRow; // Biến theo dõi dòng hiện tại để chèn

  // --- Định nghĩa Cột (vẫn hữu ích để lấy định dạng và letter) ---
  // Lưu ý: Việc đặt header qua 'columns' sẽ không hoạt động đúng khi chèn thủ công
  // Chúng ta sẽ đặt header bằng cách ghi trực tiếp vào ô
  const columnDefinitions = [
    { key: 'ordinal', header: 'TT', width: 6, style: { alignment: { horizontal: 'center' } } },
    { key: 'name', header: 'Nội dung chi phí', width: 36 },
    {
      key: 'budget',
      header:
        'Tổng mức đầu tư của dự án (dự án thành phần, tiểu dự án độc lập) hoặc dự toán (công trình, hạng mục công trình độc lập) được phê duyệt hoặc điều chỉnh lần cuối',
      width: 18,
      style: { numFmt: '#,##0', alignment: { horizontal: 'right' } },
    },
    {
      key: 'estimate',
      header: 'Dự toán (Tổng dự toán) được phê duyệt hoặc điều chỉnh lần cuối',
      width: 17,
      style: { numFmt: '#,##0', alignment: { horizontal: 'right' } },
    },
    {
      key: 'settlement',
      header: 'Giá trị đề nghị quyết toán',
      width: 20,
      style: { numFmt: '#,##0', alignment: { horizontal: 'right' } },
    },
    { key: 'reason', header: 'Nguyên nhân tăng, giảm', width: 14 },
  ];

  // --- Thiết lập độ rộng cột thủ công ---
  columnDefinitions.forEach((colDef, index) => {
    worksheet.getColumn(index + 1).width = colDef.width;
  });

  // --- Đặt Tiêu đề vào Dòng 9 (startRow) ---
  const headerRow = worksheet.getRow(currentRowNumber);
  headerRow.values = columnDefinitions.map(col => col.header);
  // Định dạng tiêu đề (Row 9)
  headerRow.font = { bold: true, name: 'Times New Roman', size: 12 };
  headerRow.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
  headerRow.eachCell(cell => {
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
  });
  // Set chiều cao cho dòng header
  headerRow.height = 120;

  // Di chuuyển đến dòng 12
  currentRowNumber += 2; // Tăng số dòng hiện tại

  // --- Chèn và Định dạng Dòng Tổng số (startRow + 2) ---
  const totalRow = worksheet.getRow(currentRowNumber);
  totalRow.values = ['', 'Tổng số', 0, 0, 0, '']; // Dữ liệu tạm thời
  const totalRowActualNumber = currentRowNumber; // Lưu số dòng thực tế của dòng tổng
  // Định dạng dòng tổng số
  totalRow.font = { bold: true, name: 'Times New Roman', size: 12 };
  totalRow.getCell(2).alignment = { vertical: 'middle' }; // Cell 'B' (Nội dung)
  totalRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
    // Áp dụng định dạng số và căn lề phải cho cột C, D, E
    if (colNumber >= 3 && colNumber <= 5) {
      // Cột budget, estimate, settlement
      cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Copy style từ definition
      cell.alignment = { horizontal: 'right' }; // Ensure right alignment
      cell.font = { bold: true, name: 'Times New Roman', size: 12 }; // Ensure bold
    }
  });
  // Di chuyển đến dòng 12
  currentRowNumber++; // Tăng số dòng hiện tại

  // --- Chèn Dữ liệu Cha/Con và Lưu số dòng cha ---
  const parentRowNumbers: any[] = [];
  sortedData.forEach(group => {
    // Chuẩn bị dữ liệu cho dòng cha (dạng mảng)
    const parentRowDataArray = [
      group.ordinalNumber,
      group.costItemTypeName,
      group.totalApprovedFinalProjectBudget || 0,
      group.totalApprovedFinalEstimate || 0,
      group.totalProposedSettlementValue || 0,
      '',
    ];
    // Chèn dòng cha
    worksheet.insertRow(currentRowNumber, parentRowDataArray);
    const parentRow = worksheet.getRow(currentRowNumber);
    parentRowNumbers.push(currentRowNumber); // Lưu số dòng cha hiện tại

    // Định dạng dòng cha
    parentRow.font = { bold: true, name: 'Times New Roman', size: 12 };
    parentRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      // Áp dụng định dạng số và căn lề phải cho cột C, D, E
      if (colNumber >= 3 && colNumber <= 5) {
        cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) }; // Lấy style từ definition
        cell.alignment = { horizontal: 'right' }; // Canh phải
        cell.font = { bold: true, name: 'Times New Roman', size: 12 }; // Đảm bảo bold
      }
      if (colNumber === 1) {
        // Cột TT
        cell.alignment = { horizontal: 'center' };
      }
    });
    currentRowNumber++; // Tăng số dòng

    // Chèn các dòng con (items) nếu có
    if (group.approvedFinalProjectBudgets && Array.isArray(group.approvedFinalProjectBudgets)) {
      group.approvedFinalProjectBudgets.forEach(item => {
        // Chuẩn bị dữ liệu cho dòng con (dạng mảng)
        const childRowDataArray = [
          item.ordinalNumber,
          item.costItemName,
          item.approvedFinalProjectBudget || 0,
          item.approvedFinalEstimate || 0,
          item.proposedSettlementValue || 0,
          item.increaseDecreaseReason || '',
        ];
        // Chèn dòng con
        worksheet.insertRow(currentRowNumber, childRowDataArray);
        const childRow = worksheet.getRow(currentRowNumber);

        // Định dạng dòng con
        childRow.font = { bold: false, name: 'Times New Roman', size: 12 };
        childRow.getCell(2).alignment = { wrapText: true }; // Cell 'B' (Nội dung)
        childRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          // Áp dụng định dạng số và căn lề phải cho cột C, D, E
          if (colNumber >= 3 && colNumber <= 5) {
            cell.style = { ...cell.style, ...(columnDefinitions[colNumber - 1].style as Style) };
            cell.alignment = { horizontal: 'right' };
            cell.font = { bold: false, name: 'Times New Roman', size: 12 }; // Ensure not bold
          }
          // Cột TT
          if (colNumber === 1) {
            cell.alignment = { horizontal: 'center' };
          }
        });
        currentRowNumber++; // Tăng số dòng
      });
    }
  });

  // --- Tạo và Gán Công thức cho dòng Tổng số (Row startRow + 1) ---
  if (parentRowNumbers.length > 0) {
    const budgetColLetter = worksheet.getColumn(3).letter; // C
    const estimateColLetter = worksheet.getColumn(4).letter; // D
    const settlementColLetter = worksheet.getColumn(5).letter; // E

    const budgetCells = parentRowNumbers.map(r => `${budgetColLetter}${r}`).join(',');
    const estimateCells = parentRowNumbers.map(r => `${estimateColLetter}${r}`).join(',');
    const settlementCells = parentRowNumbers.map(r => `${settlementColLetter}${r}`).join(',');

    // Lấy lại đối tượng dòng tổng bằng số dòng đã lưu
    const finalTotalRow = worksheet.getRow(totalRowActualNumber);

    // Gán công thức
    finalTotalRow.getCell(3).value = { formula: `SUM(${budgetCells})`, result: undefined };
    finalTotalRow.getCell(4).value = { formula: `SUM(${estimateCells})`, result: undefined };
    finalTotalRow.getCell(5).value = { formula: `SUM(${settlementCells})`, result: undefined };

    // Đảm bảo các ô khác đúng giá trị và định dạng lại nếu cần (gán formula có thể ghi đè)
    finalTotalRow.getCell(1).value = ''; // TT
    finalTotalRow.getCell(2).value = 'Tổng số';
    finalTotalRow.getCell(6).value = ''; // Reason
    // Áp dụng lại style và font cho các ô công thức nếu bị mất
    finalTotalRow.getCell(3).style = columnDefinitions[2].style as Style;
    finalTotalRow.getCell(4).style = columnDefinitions[3].style as Style;
    finalTotalRow.getCell(5).style = columnDefinitions[4].style as Style;

    finalTotalRow.getCell(3).font = { bold: true, name: 'Times New Roman', size: 12 };
    finalTotalRow.getCell(4).font = { bold: true, name: 'Times New Roman', size: 12 };
    finalTotalRow.getCell(5).font = { bold: true, name: 'Times New Roman', size: 12 };

    finalTotalRow.getCell(3).alignment = { horizontal: 'right', vertical: 'middle' };
    finalTotalRow.getCell(4).alignment = { horizontal: 'right', vertical: 'middle' };
    finalTotalRow.getCell(5).alignment = { horizontal: 'right', vertical: 'middle' };

    // Set border
    finalTotalRow.getCell(4).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
    finalTotalRow.getCell(5).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
  } else {
    console.log('Không có dòng cha nào để tính tổng.');
  }
}

// Chỉnh sửa sheet 05
function editFiveSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 05
  const worksheet = workbook.getWorksheet('05');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A7', 'B12', 'G14', 'A22', 'E22', 'H22'];

  // Nội dung cần chỉnh sửa
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A7: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    B12: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    G14: {
      year: new Date().getFullYear().toString(),
    },
    A22: {
      formCreator: formValues.formCreator,
    },
    E22: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    H22: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });
}

// Chỉnh sửa sheet 06
function editSixSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 06
  const worksheet = workbook.getWorksheet('06');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A6', 'F13', 'B21', 'D21', 'G21'];

  // Nội dung cần chỉnh sửa
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A6: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    F13: {
      year: new Date().getFullYear().toString(),
    },
    B21: {
      formCreator: formValues.formCreator,
    },
    D21: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    G21: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });
}

// Chỉnh sửa sheet 07
function editSevenSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 07
  const worksheet = workbook.getWorksheet('07');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A7', 'F17', 'A25', 'D25', 'G25'];

  // Nội dung cần chỉnh sửa
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A7: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    F17: {
      year: new Date().getFullYear().toString(),
    },
    A25: {
      formCreator: formValues.formCreator,
    },
    D25: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    G25: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });
}

// chỉnh sửa sheet 08
function editEightSheet(workbook: Workbook, formValues: FinancialSettlementReport) {
  // Get worksheet 08
  const worksheet = workbook.getWorksheet('08');

  if (!worksheet) return;

  // Danh sách các ô cần chỉnh sửa
  const cells = ['A6', 'E13', 'B19', 'C19', 'F19'];

  // Nội dung cần chỉnh sửa
  const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
    A6: {
      projectName: String(formValues.projectName)?.toUpperCase(),
    },
    E13: {
      year: new Date().getFullYear().toString(),
    },
    B19: {
      formCreator: formValues.formCreator,
    },
    C19: {
      headOfFinanceDepartment: formValues.headOfFinanceDepartment,
    },
    F19: {
      pmoDirector: formValues.pmoDirector,
    },
  };

  // Cập nhật dữ liệu của từng ô trong danh sách cần chỉnh sửa
  cells.forEach(cell => {
    if (!cellContents[cell]) return;
    updateCellValue(worksheet, cell, cellContents[cell]);
  });

  // Sao chép và chèn dòng ---
  const totalRowNumber = 12; // index của dòng tổng số
  let targetRowNumber = 13; // Vị trí insert dòng
  const rows: Record<string, any>[] = [
    ...formValues.financialSettlementReportProjectDebtStatusStatistics,
  ];

  rows.forEach((row, index) => {
    const newValues = [
      index + 1, // STT
      row.contractorName, // Tên cá nhân, đơn vị thực hiện
      row.costItemName, // Nội dung công việc, hợp đồng thực hiện
      row.proposedSettlementValue, // Giá trị đề nghị quyết toán
      row.disbursedCapital, // Vốn đã giải ngân
      row.payableAmount, // Phải trả
      row.receivableAmount, // Phải thu
      row.note, // Ghi chú
    ];

    worksheet.insertRow(targetRowNumber, newValues);

    // Get dòng đối tượng
    const targetRow = worksheet.getRow(targetRowNumber);
    // Công thức cho cột [Phải trả] và [Phải thu]
    const formulaFG = {
      // cell [Phải trả]
      6: `IF(D${targetRowNumber}-E${targetRowNumber}<0," ",D${targetRowNumber}-E${targetRowNumber})`,
      // cell [Phải thu]
      7: `IF(E${targetRowNumber}-D${targetRowNumber}<0," ",E${targetRowNumber}-D${targetRowNumber})`,
    };

    // Chỉ thay đổi giá trị của dòng đối tượng
    targetRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      let cellAlignment: Partial<Alignment> = { vertical: 'middle', horizontal: 'center' };
      if (colNumber === 2 || colNumber === 3) {
        cellAlignment = { vertical: 'top', horizontal: 'left' };
        cell.numFmt = '#,##0';
      } else if (colNumber >= 4 && colNumber <= 7) {
        cellAlignment = { vertical: 'middle', horizontal: 'right' };
        cell.numFmt = '#,##0';
      }
      cell.alignment = cellAlignment;
      cell.font = { bold: false, name: 'Times New Roman', size: 12 };
      cell.border = {
        top: { style: 'thin' as BorderStyle },
        left: { style: 'thin' as BorderStyle },
        bottom: { style: 'thin' as BorderStyle },
        right: { style: 'thin' as BorderStyle },
      };
      // Bỏ quả cột [Phải trả] và [Phải thu]
      if (colNumber === 6 || colNumber === 7) {
        cell.value = {
          formula: formulaFG[colNumber],
          result: 0,
        };
        return;
      }
      // *** Chỉ gán lại thuộc tính value ***
      cell.value = newValues[colNumber - 1]; // Tạo giá trị mới dựa trên địa chỉ ô
    });

    // Di chuyển con trỏ đến dòng tiếp theo
    targetRowNumber += 1;
  });

  // Cập nhật công thức của dòng tổng số
  const totalRow = worksheet.getRow(totalRowNumber);

  totalRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
    if (colNumber >= 4 && colNumber <= 7) {
      const columnLetter = getColumnLetterFromString(cell.address);
      cell.value = {
        formula: `SUBTOTAL(9,${columnLetter}13:${columnLetter}${targetRowNumber})`,
        result: 0,
      };
    }
  });
}

/**
 * Thay thế các placeholder dạng {{key}} trong chuỗi mẫu bằng giá trị tương ứng.
 *
 * @param {string} templateString - Chuỗi mẫu chứa các placeholder (ví dụ: "ngày {{day}}").
 * @param {object} params - Đối tượng chứa các cặp key-value để thay thế (ví dụ: { day: 19 }).
 * @returns {string} Chuỗi mới với các placeholder đã được thay thế bằng giá trị.
 */
function replaceTemplateParams(templateString: string, params: any): string {
  // Biểu thức chính quy (Regex) để tìm các placeholder dạng {{ key }}
  // /{{\s*(\w+)\s*}}/g giải thích:
  // - {{ và }}: Khớp với các dấu ngoặc nhọn kép cố định.
  // - \s*: Khớp với 0 hoặc nhiều ký tự khoảng trắng (space, tab, etc.). Điều này cho phép có khoảng trắng như {{ day }} thay vì chỉ {{day}}.
  // - (\w+): Khớp với một hoặc nhiều ký tự "word" (chữ cái, chữ số, dấu gạch dưới). Dấu ngoặc đơn () tạo thành một "capturing group", nghĩa là phần khớp với \w+ (chính là tên key) sẽ được tách ra.
  // - g: "global flag", đảm bảo rằng hàm replace sẽ tìm và thay thế *tất cả* các vý hiện của mẫu, không chỉ cái đầu tiên.
  const regex = /{{\s*(\w+)\s*}}/g;

  // Sử dụng phương thức replace() của chuỗi.
  // Đối số thứ hai của replace() có thể là một hàm callback.
  // Hàm này sẽ được gọi cho mỗi lần tìm thấy sự khớp với regex.
  const resultString = templateString.replace(regex, (match, key) => {
    // Tham số của hàm callback:
    // - match: Toàn bộ chuỗi khớp với regex (ví dụ: "{{ day }}", "{{month}}").
    // - key: Chuỗi được khớp bởi capturing group thứ nhất (\w+) (ví dụ: "day", "month").

    // Kiểm tra xem 'key' có tồn tại như một thuộc tính trong đối tượng 'params' không.
    // Sử dụng Object.prototype.hasOwnProperty.call() là cách an toàn để kiểm tra,
    // tránh các vấn đề tiềm ẩn nếu 'params' có thể không phải là đối tượng thuần túy.
    if (Object.prototype.hasOwnProperty.call(params, key)) {
      // Nếu key tồn tại, trả về giá trị tương ứng từ đối tượng params.
      // Giá trị này sẽ thay thế cho 'match' trong chuỗi gốc.
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return params[key];
    } else {
      // Nếu key không tồn tại trong đối tượng params:
      // Bạn có thể chọn cách xử lý:
      // 1. Trả về chuỗi rỗng: return '';
      // 2. Trả về chính placeholder gốc (như trong ví dụ này): return match;
      // 3. Ném ra lỗi: throw new Error(`Tham số "${key}" không tồn tại.`);
      console.warn(`Cảnh báo: Tham số "{{${key}}}" không được cung cấp giá trị.`); // Tùy chọn: hiển thị cảnh báo
      return match; // Trả về placeholder gốc nếu không tìm thấy giá trị
    }
  });

  // Trả về chuỗi cuối cùng sau khi đã thay thế tất cả các placeholder hợp lệ.
  return resultString;
}

/**
 * Chỉnh sửa giá trị của một ô cụ thể trong worksheet mà không ghi đè
 * lên các thuộc tính định dạng khác (font, fill, border, alignment, numFmt,...).
 *
 * @param {ExcelJS.Worksheet} worksheet - Đối tượng worksheet của exceljs mà bạn muốn thao tác.
 * @param {string} index - Địa chỉ của ô cần chỉnh sửa (ví dụ: 'A1', 'B5', 'C10').
 * @param {*} newValue - Giá trị mới cần ghi vào ô. Giá trị này có thể là:
 * - Chuỗi (string)
 * - Số (number)
 * - Ngày tháng (Date object)
 * - Boolean (true/false)
 * - Null
 * - Đối tượng RichText (ít phổ biến hơn khi chỉ muốn đổi giá trị đơn giản)
 * - Đối tượng Formula (ví dụ: { formula: 'A1+B1', result: undefined })
 * @returns {ExcelJS.Cell} Đối tượng Cell đã được cập nhật. Trả về cell để có thể dùng tiếp nếu cần.
 */
export function updateCellValue(
  worksheet: Worksheet,
  cellAddress: string = 'A1',
  newValue: any
): Cell {
  const cell = worksheet.getCell(cellAddress);
  const oldValue = cell.value;
  cell.value = replaceTemplateParams(String(oldValue), newValue); // Gán giá trị mới

  return cell;
}

/**
 * Sắp xếp một mảng các đối tượng dựa trên một thuộc tính (key) được chỉ định
 * và thêm một thuộc tính 'ordinalNumber' (số thứ tự, bắt đầu từ 1) cho mỗi đối tượng trong mảng đã sắp xếp.
 *
 * @param {Array<Object>} array Mảng các đối tượng cần sắp xếp.
 * @param {string} sortByKey Tên thuộc tính (key) dùng để làm cơ sở sắp xếp.
 * @param {'asc' | 'desc'} [sortOrder='asc'] Thứ tự sắp xếp: 'asc' (tăng dần - mặc định) hoặc 'desc' (giảm dần).
 * @returns {Array<Object>} Một mảng MỚI chứa các đối tượng đã được sắp xếp, mỗi đối tượng có thêm thuộc tính 'ordinalNumber'. Trả về mảng rỗng nếu đầu vào không hợp lệ.
 */
function sortAndAddOrdinal<T extends object>(
  array: T[],
  sortByKey: keyof T,
  sortOrder: 'asc' | 'desc' = 'asc'
): (T & { ordinalNumber: number })[] {
  // --- Kiểm tra đầu vào cơ bản ---
  if (!Array.isArray(array)) {
    console.error('Đầu vào không phải là một mảng.');
    return []; // Trả về mảng rỗng nếu đầu vào không phải mảng
  }
  if (array.length === 0) {
    return []; // Trả về mảng rỗng nếu mảng đầu vào rỗng
  }
  if (!sortByKey || typeof sortByKey !== 'string') {
    console.error('Thuộc tính "sortByKey" phải là một chuỗi không rỗng.');
    // Trả về mảng gốc với số thứ tự nếu không có key sắp xếp hợp lệ
    return array.map((item, index) => ({ ...item, ordinalNumber: index + 1 }));
  }

  // --- Xử lý chính ---

  // 1. Tạo bản sao nông (shallow copy) của mảng để không thay đổi mảng gốc
  const arrayCopy = [...array];

  // 2. Sắp xếp bản sao dựa trên `sortByKey` và `sortOrder`
  arrayCopy.sort((a: object, b: object) => {
    // Lấy giá trị từ thuộc tính cần sắp xếp
    // Kiểm tra xem key có tồn tại trên object không để tránh lỗi
    const valueA = Object.prototype.hasOwnProperty.call(a, sortByKey)
      ? (a as Record<string, any>)[sortByKey]
      : undefined;
    const valueB = Object.prototype.hasOwnProperty.call(b, sortByKey)
      ? (b as Record<string, any>)[sortByKey]
      : undefined;

    let comparison = 0;

    // Xử lý trường hợp giá trị là null hoặc undefined (đẩy về cuối khi tăng dần)
    if ((valueA === undefined || valueA === null) && valueB !== undefined && valueB !== null) {
      comparison = 1; // a lớn hơn b (đẩy a về cuối)
    } else if (
      valueA !== undefined &&
      valueA !== null &&
      (valueB === undefined || valueB === null)
    ) {
      comparison = -1; // a nhỏ hơn b (giữ a ở trước)
    } else if (valueA < valueB) {
      comparison = -1; // a nhỏ hơn b
    } else if (valueA > valueB) {
      comparison = 1; // a lớn hơn b
    }
    // Nếu comparison = 0 nghĩa là bằng nhau hoặc cả hai đều null/undefined

    // Đảo ngược kết quả nếu sắp xếp giảm dần ('desc')
    return sortOrder.toLowerCase() === 'desc' ? comparison * -1 : comparison;
  });

  // 3. Tạo mảng kết quả mới với việc thêm 'ordinalNumber' (bắt đầu từ 1)
  const sortedArrayWithOrdinal = arrayCopy.map((item, index) => ({
    ...item, // Giữ lại tất cả các thuộc tính gốc của đối tượng
    ordinalNumber: index + 1, // Thêm số thứ tự (1-based)
  }));

  return sortedArrayWithOrdinal;
}

/**
 * Hàm định dạng số thứ tự mặc định (trả về chính số đó dưới dạng chuỗi).
 * @param {number} n Số thứ tự.
 * @returns {string} Số thứ tự gốc dưới dạng chuỗi.
 */
const defaultOrdinalFormatter = (n: number): string => n.toString();

/**
 * Nhóm một mảng các đối tượng dựa trên một khóa (key) được chỉ định.
 * @template T Kiểu của các đối tượng trong mảng dữ liệu đầu vào.
 * @template K Kiểu của khóa dùng để nhóm.
 * @param {T[]} data Mảng dữ liệu đầu vào cần nhóm.
 * @param {K} groupKey Tên thuộc tính dùng để nhóm.
 * @param {Array<keyof T>} parentProps Mảng các tên thuộc tính để đưa vào đối tượng cha (lấy từ phần tử đầu tiên của nhóm).
 * @param {Array<keyof T>} childProps Mảng các tên thuộc tính để đưa vào mỗi phần tử con trong nhóm.
 * @param {string} [childrenKey='items'] Tên thuộc tính trong đối tượng cha để chứa mảng các phần tử con (mặc định là 'items').
 * @returns {Array<Record<string, any>>} Mảng các đối tượng đã được nhóm.
 */
function groupData<T, K extends keyof T>(
  data: T[],
  groupKey: K,
  parentProps: Array<keyof T>,
  childProps: Array<keyof T>,
  childrenKey: string = 'items'
): Array<Record<string, any>> {
  // Kiểm tra đầu vào cơ bản
  if (
    !Array.isArray(data) ||
    !groupKey ||
    !Array.isArray(parentProps) ||
    !Array.isArray(childProps)
  ) {
    console.error('Tham số không hợp lệ cho hàm groupData.');
    return []; // Trả về mảng rỗng nếu tham số không hợp lệ
  }

  const groupedObject = data.reduce(
    (accumulator, currentItem) => {
      const key = currentItem[groupKey];

      // Xử lý giá trị key là null/undefined để tránh lỗi khi dùng làm key của object
      // Chúng ta có thể nhóm chúng dưới một key đặc biệt như 'null_group' hoặc 'undefined_group'
      const groupIdentifier =
        key === null ? 'group_null' : key === undefined ? 'group_undefined' : key;

      // Nếu nhóm chưa tồn tại trong accumulator, khởi tạo nó
      if (!accumulator[groupIdentifier as string]) {
        // Tạo đối tượng cha bằng cách trích xuất các thuộc tính parentProps
        accumulator[groupIdentifier as string] = extractProperties(currentItem, parentProps);
        // Khởi tạo mảng để chứa các phần tử con
        accumulator[groupIdentifier as string][childrenKey] = [];
      }

      // Tạo đối tượng con bằng cách trích xuất các thuộc tính childProps
      const childItem = extractProperties(currentItem, childProps);

      // Thêm đối tượng con vào mảng childrenKey của nhóm tương ứng
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      accumulator[groupIdentifier as string][childrenKey].push(childItem);

      // Trả về accumulator cho lần lặp tiếp theo
      return accumulator;
    },
    {} as Record<string, any>
  ); // Giá trị khởi tạo là một object rỗng

  // Chuyển đổi object các nhóm thành một mảng các nhóm
  return Object.values(groupedObject) as Array<Record<string, any>>;
}

/**
 * Sắp xếp dữ liệu đã được nhóm ở cấp độ cha và con, đồng thời thêm số thứ tự.
 * @param {object} options - Các tùy chọn sắp xếp.
 * @param {object[]} options.groupedData - Mảng dữ liệu đã được nhóm cần sắp xếp.
 * @param {string} options.parentSortKey - Tên thuộc tính trong đối tượng cha để sắp xếp các nhóm.
 * @param {string} options.childrenArrayKey - Tên thuộc tính trong đối tượng cha chứa mảng các phần tử con.
 * @param {string} options.childSortKey - Tên thuộc tính trong đối tượng con để sắp xếp các phần tử con trong mỗi nhóm.
 * @param {string} [options.parentOrdinalKey='ordinalNumber'] - Tên thuộc tính để lưu số thứ tự của cha.
 * @param {function} [options.parentOrdinalFormatter=defaultOrdinalFormatter] - Hàm để định dạng số thứ tự của cha.
 * @param {string} [options.childOrdinalKey='ordinalNumber'] - Tên thuộc tính để lưu số thứ tự của con.
 * @param {function} [options.childOrdinalFormatter=defaultOrdinalFormatter] - Hàm để định dạng số thứ tự của con.
 * @returns {object[]} Mảng dữ liệu đã được sắp xếp và đánh số thứ tự.
 */
function sortGroupedData<T extends object>({
  groupedData,
  parentSortKey,
  childrenArrayKey, // Thuộc tính này rất quan trọng để tìm mảng con
  childSortKey,
  parentOrdinalKey = 'ordinalNumber', // Mặc định theo yêu cầu
  parentOrdinalFormatter = defaultOrdinalFormatter,
  childOrdinalKey = 'ordinalNumber', // Mặc định theo yêu cầu
  childOrdinalFormatter = defaultOrdinalFormatter,
}: {
  groupedData: T[];
  parentSortKey: keyof T;
  childrenArrayKey: keyof T;
  childSortKey: keyof T;
  parentOrdinalKey?: string;
  parentOrdinalFormatter?: (index: number) => string;
  childOrdinalKey?: string;
  childOrdinalFormatter?: (index: number) => string;
}): object[] {
  // --- Kiểm tra đầu vào ---
  if (!Array.isArray(groupedData)) {
    console.error('Lỗi: ', groupedData, ' phải là một mảng.');
    return [];
  }
  if (!parentSortKey || !childrenArrayKey || !childSortKey) {
    console.error(
      'Lỗi: ',
      parentSortKey,
      ', ',
      childrenArrayKey,
      ', và ',
      childSortKey,
      ' là bắt buộc.'
    );
    return groupedData; // Trả về dữ liệu gốc nếu thiếu key quan trọng
  }

  // --- 1. Sắp xếp các phần tử con trong mỗi nhóm ---
  groupedData.forEach(group => {
    const children = group[childrenArrayKey];
    // Kiểm tra xem mảng con có tồn tại và là mảng không
    if (Array.isArray(children)) {
      children.sort((a, b) => compareValues(a[childSortKey], b[childSortKey]));
    } else {
      console.warn(
        `Group missing or has invalid children array key '${String(childrenArrayKey)}':`,
        group
      );
    }
  });

  // --- 2. Sắp xếp các nhóm cha ---
  groupedData.sort((a, b) => compareValues(a[parentSortKey], b[parentSortKey]));

  // --- 3. Thêm số thứ tự cho cha và con ---
  groupedData.forEach((group, index) => {
    // Thêm số thứ tự cho cha
    (group as Record<string, any>)[parentOrdinalKey] = parentOrdinalFormatter(index + 1);

    // Thêm số thứ tự cho con
    const children = group[childrenArrayKey];
    if (Array.isArray(children)) {
      children.forEach((child, childIndex) => {
        child[childOrdinalKey] = childOrdinalFormatter(childIndex + 1);
      });
    }
  });

  return groupedData;
}

/**
 * Hàm so sánh cơ bản cho việc sắp xếp, xử lý null/undefined và các kiểu dữ liệu phổ biến.
 * @template T Kiểu dữ liệu của các giá trị cần so sánh.
 * @param {T} a Giá trị thứ nhất.
 * @param {T} b Giá trị thứ hai.
 * @returns {number} -1 nếu a < b, 1 nếu a > b, 0 nếu a == b.
 */
function compareValues<T>(a: T, b: T): number {
  // Xử lý null/undefined: coi chúng nhỏ nhất
  const aIsNull = a === null || a === undefined;
  const bIsNull = b === null || b === undefined;
  if (aIsNull && bIsNull) return 0;
  if (aIsNull) return -1; // a null, b không null -> a < b
  if (bIsNull) return 1; // a không null, b null -> a > b

  // Xử lý Date objects hoặc chuỗi ngày tháng hợp lệ
  const dateA = new Date(a as string);
  const dateB = new Date(b as string);
  if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
    return dateA.getTime() - dateB.getTime();
  }

  // Xử lý số
  if (typeof a === 'number' && typeof b === 'number') {
    return a - b;
  }

  // Xử lý chuỗi (dùng localeCompare để sắp xếp đúng tiếng Việt nếu có)
  if (typeof a === 'string' && typeof b === 'string') {
    return a.localeCompare(b);
  }

  // Trường hợp khác hoặc kiểu dữ liệu hỗn hợp (có thể cần điều chỉnh logic này)
  // Tạm thời chuyển sang string để so sánh
  return String(a).localeCompare(String(b));
}

/**
 * Trích xuất các thuộc tính được chỉ định từ một đối tượng nguồn.
 * @param {object} sourceObject Đối tượng nguồn.
 * @param {string[]} propsArray Mảng các tên thuộc tính cần trích xuất.
 * @returns {object} Một đối tượng mới chỉ chứa các thuộc tính được yêu cầu.
 */
function extractProperties<T, K extends keyof T>(
  sourceObject: T,
  propsArray: K[]
): Partial<Pick<T, K>> {
  const newObj: Partial<Pick<T, K>> = {};
  if (!sourceObject || !Array.isArray(propsArray)) {
    return newObj; // Trả về object rỗng nếu đầu vào không hợp lệ
  }
  for (const prop of propsArray) {
    // Kiểm tra xem thuộc tính có tồn tại trong đối tượng nguồn không
    if (Object.prototype.hasOwnProperty.call(sourceObject, prop)) {
      newObj[prop] = sourceObject[prop];
    }
  }
  return newObj;
}

/**
 * Nhóm một mảng các đối tượng dựa trên một key (thuộc tính) được chỉ định
 * và tính tổng của các thuộc tính số được liệt kê trong mỗi nhóm.
 *
 * @param {T[]} data - Mảng các đối tượng đầu vào.
 * @param {string} groupKey - Tên thuộc tính dùng để nhóm các đối tượng.
 * @param {string[]} sumKeys - Mảng các tên thuộc tính (dạng số) cần tính tổng trong mỗi nhóm.
 * @returns {T[]} Một mảng các đối tượng đã được nhóm và tính tổng.
 * Mỗi đối tượng kết quả sẽ chứa thuộc tính groupKey và các thuộc tính tổng (với tiền tố "total").
 */
function groupAndSum<T>(
  data: Record<string, T>[],
  groupKey: string,
  sumKeys: string[]
): Record<string, T>[] {
  // 1. Kiểm tra tính hợp lệ của đầu vào
  if (!Array.isArray(data)) {
    console.error('Lỗi: Dữ liệu đầu vào phải là một mảng.');
    return [];
  }
  if (typeof groupKey !== 'string' || groupKey.length === 0) {
    console.error('Lỗi: Khóa nhóm (groupKey) phải là một chuỗi không rỗng.');
    return [];
  }
  if (!Array.isArray(sumKeys)) {
    console.error('Lỗi: Danh sách khóa cần tính tổng (sumKeys) phải là một mảng.');
    return [];
  }

  // 2. Sử dụng reduce để nhóm và tính tổng
  const groupedSums = data.reduce(
    (accumulator: Record<string, any>, currentItem: Record<string, any>) => {
      // Lấy giá trị của key dùng để nhóm
      let key = currentItem[groupKey];

      // Xử lý key là chuỗi: loại bỏ khoảng trắng thừa ở đầu/cuối
      if (typeof key === 'string') {
        key = key.trim();
      }

      // Sử dụng giá trị key đã xử lý (có thể là chuỗi, số, null,...) làm định danh nhóm
      const groupIdentifier = key;

      // Nếu nhóm này chưa tồn tại trong accumulator, hãy khởi tạo nó
      if (!accumulator[groupIdentifier]) {
        accumulator[groupIdentifier] = {};
        // Thêm thuộc tính dùng để nhóm vào kết quả
        accumulator[groupIdentifier][groupKey] = key;
        // Khởi tạo tất cả các thuộc tính tổng bằng 0
        sumKeys.forEach(sKey => {
          const totalKey = `total${sKey}`; // Tạo tên thuộc tính tổng, ví dụ: totalPoPlannedCapital
          accumulator[groupIdentifier][totalKey] = 0;
        });
      }

      // Cộng dồn các giá trị từ currentItem vào các thuộc tính tổng của nhóm
      sumKeys.forEach(sKey => {
        const totalKey = `total${sKey}`;
        // Chuyển giá trị sang số, nếu không hợp lệ thì dùng 0
        const value = Number(currentItem[sKey]) || 0;
        accumulator[groupIdentifier][totalKey] += value;
      });

      // Trả về accumulator cho vòng lặp tiếp theo
      return accumulator;
    },
    {}
  ); // Giá trị khởi tạo của accumulator là một object rỗng

  // 3. Chuyển đổi object kết quả (key là groupIdentifier) thành mảng các giá trị (các object nhóm)
  return Object.values(groupedSums) as Record<string, T>[];
}

/**
 * Lấy phần ký tự cột từ một chuỗi địa chỉ ô Excel (ví dụ: 'E29' -> 'E', 'AA100' -> 'AA').
 *
 * @param {string} cellAddress Chuỗi địa chỉ ô (ví dụ: 'E29', 'AA100', 'a1').
 * @returns {string | empty} Phần ký tự cột (luôn viết hoa) hoặc null nếu định dạng không hợp lệ.
 */
function getColumnLetterFromString(cellAddress: string): string {
  if (typeof cellAddress !== 'string' || cellAddress.length === 0) {
    return ''; // Trả về null nếu đầu vào không phải chuỗi hợp lệ
  }

  // Sử dụng Regular Expression để tìm các ký tự chữ cái ở đầu chuỗi
  // ^     : Bắt đầu chuỗi
  // ([A-Z]+) : Tìm và nhóm (capture group 1) một hoặc nhiều ký tự chữ cái từ A đến Z
  // i     : Không phân biệt chữ hoa/thường khi tìm kiếm
  const match = cellAddress.match(/^([A-Z]+)/i);

  // Nếu tìm thấy khớp (match không phải là null)
  // match[0] là toàn bộ chuỗi khớp (ví dụ: 'E', 'AA')
  // match[1] là nội dung của nhóm capture đầu tiên (cũng là 'E', 'AA')
  if (match && match[1]) {
    return match[1].toUpperCase(); // Trả về phần khớp và đảm bảo viết hoa
  } else {
    return ''; // Không tìm thấy ký tự cột ở đầu (định dạng không hợp lệ)
  }
}

/**
 * Nhóm dữ liệu theo cột loại nội dung chi phí
 */
function groupByCostItemTypeId<T>(
  proposedSettlementInvestmentCosts: Record<string, T>[]
): Record<string, T>[] {
  // Sử dụng reduce để nhóm, tính tổng và lưu danh sách các phần tử
  const groupedData: { [key: string]: any } = proposedSettlementInvestmentCosts.reduce(
    (accumulator: { [key: string]: any }, currentItem: Record<string, T>) => {
      const typeId: string = String(currentItem.costItemTypeId);

      // Lấy các giá trị cần tính tổng, đảm bảo là số (hoặc 0 nếu không phải)
      const budget = Number(currentItem.approvedFinalProjectBudget) || 0;
      const estimate = Number(currentItem.approvedFinalEstimate) || 0;
      const settlement = Number(currentItem.proposedSettlementValue) || 0;

      // Nếu chưa có nhóm cho typeId này trong accumulator, khởi tạo nó
      if (!accumulator[typeId]) {
        accumulator[typeId] = {
          costItemTypeId: typeId,
          costItemTypeName: currentItem.costItemTypeName, // Lấy tên từ item đầu tiên của nhóm
          totalApprovedFinalProjectBudget: 0,
          totalApprovedFinalEstimate: 0,
          totalProposedSettlementValue: 0,
          approvedFinalProjectBudgets: [] as Array<any>, // <-- Khởi tạo mảng để lưu các phần tử con
        };
      }

      // Cộng dồn các giá trị vào tổng của nhóm tương ứng
      accumulator[typeId].totalApprovedFinalProjectBudget += budget;
      accumulator[typeId].totalApprovedFinalEstimate += estimate;
      accumulator[typeId].totalProposedSettlementValue += settlement;

      // Thêm thông tin chi tiết của phần tử hiện tại vào mảng approvedFinalProjectBudgets của nhóm
      (accumulator[typeId].approvedFinalProjectBudgets as Array<any>).push({
        costItemTypeId: currentItem.costItemTypeId,
        id: currentItem.id,
        costItemId: currentItem.costItemId,
        costItemName: currentItem.costItemName,
        approvedFinalProjectBudget: currentItem.approvedFinalProjectBudget, // Giữ giá trị gốc
        approvedFinalEstimate: currentItem.approvedFinalEstimate, // Giữ giá trị gốc
        proposedSettlementValue: currentItem.proposedSettlementValue, // Giữ giá trị gốc
        increaseDecreaseReason: currentItem.increaseDecreaseReason,
      });

      // Trả về accumulator cho lần lặp tiếp theo
      return accumulator;
    },
    {}
  ); // Giá trị khởi tạo là một object rỗng

  // Chuyển đổi object kết quả thành một mảng các giá trị (các object nhóm)
  return Object.values(groupedData) as Record<string, T>[];
}

// Sắp xếp dữ liệu theo Tổng mức đầu tư dự án
function orderByApprovedFinalProjectBudget<T>(
  proposedSettlementInvestmentCostGrouped: Record<string, T>[]
): Record<string, T>[] {
  // 1. Sắp xếp phần tử con theo id
  proposedSettlementInvestmentCostGrouped.forEach(group => {
    if (group.approvedFinalProjectBudgets && Array.isArray(group.approvedFinalProjectBudgets)) {
      group.approvedFinalProjectBudgets.sort((a, b) => a.id - b.id);
    }
  });

  // 2. Sắp xếp phần tử cha theo totalApprovedFinalProjectBudget giảm dần và điều kiện đặc biệt
  proposedSettlementInvestmentCostGrouped.sort((a, b) => {
    const isAOtherCost = a.costItemTypeName === 'Chi phí khác';
    const isBOtherCost = b.costItemTypeName === 'Chi phí khác';

    if (isAOtherCost && !isBOtherCost) {
      return 1; // a ('Chi phí khác') xếp sau b
    }
    if (!isAOtherCost && isBOtherCost) {
      return -1; // a xếp trước b ('Chi phí khác')
    }
    // Nếu cả hai cùng là 'Chi phí khác' hoặc không phải, thì sắp xếp theo budget giảm dần
    return (
      Number(b.totalApprovedFinalProjectBudget || 0) -
      Number(a.totalApprovedFinalProjectBudget || 0)
    );
  });

  // 3. Thêm số thứ tự (Ordinal number)
  proposedSettlementInvestmentCostGrouped.forEach(
    (group: Record<string, any>, groupIndex: number): void => {
      // Thêm STT La Mã cho cha
      group['ordinalNumber'] = toRoman(groupIndex + 1); // Đặt tên key có khoảng trắng nếu cần

      // Thêm STT cho con
      if (group.approvedFinalProjectBudgets && Array.isArray(group.approvedFinalProjectBudgets)) {
        group.approvedFinalProjectBudgets.forEach((item, itemIndex) => {
          item['ordinalNumber'] = itemIndex + 1; // Đặt tên key có khoảng trắng nếu cần
        });
      }
    }
  );

  return proposedSettlementInvestmentCostGrouped;
}
