import { PageLayout } from '@/components/page-layout';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import {
  createLabel,
  enterLabel,
  getDataLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  QUERIES,
  selectLabel,
  TABLES,
} from '@/constant';
import { useAuth, useFormHandler, usePermission } from '@/hooks';
import { toLocaleDate } from '@/lib/date';
import notification from '@/lib/notifications';
import { getRandomNumber, realNumberDecimalFormat } from '@/lib/number';
import { displayExpr } from '@/lib/utils';
import {
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
  createQueryReport,
  postRequest,
} from '@/services';
import {
  BudgetSourceCodeCommon,
  defaultValuesTemplateStatisticReportFilter,
  PaginationResponse,
  ReportTemplate,
  ReportTemplateDetail,
  templateStatisticReportFilterSchema,
  TemplateStatisticReportResponse,
  TemplateStatisticsDetailReportDto,
  TemplateStatisticsReportFilter,
} from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Button, DataGrid, DateBox, TextBox } from 'devextreme-react';
import { Button as GridButton } from 'devextreme-react/data-grid';
import {
  Column,
  DataGridRef,
  Editing,
  FilterRow,
  Grouping,
  GroupPanel,
  HeaderFilter,
  Item,
  LoadPanel,
  Lookup,
  Pager,
  Paging,
  Scrolling,
  Search,
  StateStoring,
  Toolbar,
} from 'devextreme-react/data-grid';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { EditorPreparingEvent, RowUpdatingEvent } from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { Parser } from 'expr-eval';
import saveAs from 'file-saver';
import { decode } from 'html-entities';
import { SyntheticEvent, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ColumnProps,
  createTreeConfig,
  getVisibleColumnMap,
  VisibleColumnItem,
} from '../report-template';
import { customizeNumberCell, onCellPrepared } from '@/components/devex-data-grid';
import { useParams } from 'react-router-dom';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { ClickEvent } from 'devextreme/ui/button';

const getExcelColumnLetter = (columnIndex: number) => {
  let letter = '';
  while (columnIndex > 0) {
    const remainder = (columnIndex - 1) % 26;
    letter = String.fromCharCode(65 + remainder) + letter;
    columnIndex = Math.floor((columnIndex - 1) / 26);
  }
  return letter;
};

type VisibleColumnItemWithReportTemplateDetail = VisibleColumnItem & ReportTemplateDetail;

const calculateFormula = (
  formula: string,
  data: Record<string, string | number | Date | null>
): number => {
  try {
    const parser = new Parser();
    const expr = parser.parse(formula);

    const scope = expr.variables().reduce<Record<string, string | number>>((acc, key) => {
      let val = data[key];
      if (val === null || val === undefined) val = 0;
      else if (val instanceof Date) val = val.getTime();
      else if (typeof val === 'string') val = isNaN(Number(val)) ? val : Number(val);
      acc[key] = val;
      return acc;
    }, {});

    return Number(expr.evaluate(scope));
  } catch (error) {
    console.error('Calculate formula error', error);
    return 0;
  }
};

const getRowData = (item: TemplateStatisticReportResponse) => {
  const formulaFieldMap: Map<string, string> = new Map<string, string>();

  const rowItem = item.templateStatisticsDetailReportDto.reduce<
    Record<string, number | string | Date | null>
  >(
    (row, column) => {
      if (column.nameField) {
        let value: string | number | Date | null = column.value || '';

        if (value && column.valueType === 2) {
          const date = new Date(String(value));
          if (!isNaN(date.getTime())) {
            value = date;
          }
        }

        row[column.nameField] = value;

        if (column.formula) {
          formulaFieldMap.set(column.nameField, column.formula);
        }
      }

      return row;
    },
    {
      id: -getRandomNumber(),
      projectId: item.projectId,
      capitalIncreasePlanDetailId: item.capitalIncreasePlanDetailId,
    }
  );

  formulaFieldMap.forEach((formula, key) => {
    rowItem[key] = calculateFormula(formula, rowItem);
  });

  return rowItem;
};

const columnDisplayPropsMap: Record<number, object> = {
  1: {
    alignment: 'right',
    dataType: 'number',
    format: '#,##0.##',
  },
  2: {
    alignment: 'left',
    dataType: 'date',
  },
  3: {
    alignment: 'left',
    dataType: 'number',
  },
};
// const onExporting = createExportingEvent(`${'thong-ke-theo-bieu-mau-bao-cao'}.xlsx`, 'Main');
const onTargetMutationSuccess = createMutationSuccessFn('templateStatisticsReport');
export const TemplateStatisticsReport = () => {
  const { t } = useTranslation('templateStatisticsReport');
  const { projects, user } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;
  const { id: editId } = useParams();
  const role = usePermission(PERMISSIONS.TEMPLATE_STATISTICS_REPORT);
  const defaultValues = useMemo(
    () => ({
      ...defaultValuesTemplateStatisticReportFilter,
      userCreatedId: user?.userId || null,
    }),
    [user?.userId]
  );

  const [dataSource, setDataSource] = useState<TemplateStatisticReportResponse[]>([]);
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(PATHS.TEMPLATE_STATISTICS_REPORT);

  const { handleSubmit, loading, methods } = useFormHandler<TemplateStatisticsReportFilter>({
    queryKey: [MUTATE.TEMPLATE_STATISTICS_REPORT, editId],
    mutateKey: [MUTATE.TEMPLATE_STATISTICS_REPORT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.TEMPLATE_STATISTICS_REPORT],
    readFn: createQueryByIdFn<TemplateStatisticsReportFilter>('template-statistics-report'),
    createFn: createPostMutateFn<TemplateStatisticsReportFilter>('template-statistics-report'),
    updateFn: createPutMutateFn<TemplateStatisticsReportFilter>('template-statistics-report'),
    formatPayloadFn: data => ({
      ...data,
    }),
    formatResponseFn: response => {
      const data = {
        ...response,
      };
      return data;
    },
    onCreateSuccess: data => {
      onTargetMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onTargetMutationSuccess,
    formOptions: {
      resolver: zodResolver(templateStatisticReportFilterSchema),
      defaultValues,
    },
  });

  const gridRef = useRef<DataGridRef>(null);
  const [dataGridState, setDataGridState] = useState<{
    state: Record<string, VisibleColumnItemWithReportTemplateDetail>;
    columnTree: ColumnProps[] | undefined;
  }>();

  const { data: budgetSourceCode } = useQuery({
    queryKey: [QUERIES.COMMON_GET_BUDGET_SOURCE_CODE], // Key để định danh query
    queryFn: () => {
      // Gọi API để lấy danh sách mã nguồn ngân sách
      return postRequest<PaginationResponse<BudgetSourceCodeCommon>>(
        '/common/get-budget-source-code',
        {
          filterColumn: [
            {
              column: 'string',
              keySearch: 'string',
              expression: 'string',
            },
          ],
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'Id',
          sortOrder: 1,
          isPage: false,
        }
      );
    },
    /**
     * `staleTime`: Thời gian (tính bằng mili giây) mà dữ liệu được coi là "cũ".
     * - Trong 30 giây, nếu người dùng thêm dòng mới, query sẽ không gọi lại API.
     * - Giúp giảm số lần gọi API không cần thiết.
     */
    staleTime: 30 * 1000, // 30 giây
  });

  const { mutate, isPending } = useMutation({
    mutationKey: [QUERIES.TEMPLATE_STATISTICS_REPORT],
    mutationFn: ({
      projectIds,
      capitalIncreasePlanDetailId = '0',
    }: {
      projectIds: string | null | undefined;
      capitalIncreasePlanDetailId?: string | null;
    }) => {
      const params = {
        filterColumn: [
          {
            column: 'string',
            keySearch: 'string',
            expression: 'string',
          },
        ],
        pageIndex: 0,
        pageSize: -1,
        sortColumn: 'id',
        sortOrder: 0,
        isPage: true,
        objParam: {
          reportTemplateId: methods.getValues('reportTemplateId').toString(),
          year: toLocaleDate(methods.getValues('year')),
          projectIds: projectIds,
          capitalIncreasePlanDetailId: capitalIncreasePlanDetailId,
          templateStatisticsId: Number(editId) || 0,
        },
      };
      return createQueryReport<TemplateStatisticReportResponse>('template-statistics-report')(
        params
      );
    },
  });

  const setDetailsToTable = useMemo(
    () => (items: TemplateStatisticReportResponse[]) => {
      if (!items.length) return;

      const source = items.map(item => getRowData(item));
      const instance = gridRef.current?.instance();

      const ds = instance?.getDataSource();
      source.forEach(item => {
        ds
          ?.store()
          .insert(item)
          .catch(error => console.error(error));
      });
    },
    []
  );

  const onSubmit = () => {
    setDataSource([]);
    mutate(
      { projectIds },
      {
        onSuccess: response => {
          if (response.items) {
            const { items } = response;
            setDetailsToTable(items);
          }
        },
        onError: error => {
          notification.error(error.message);
        },
      }
    );
  };

  const handleRowUpdate = (e: RowUpdatingEvent<any, any>) => {
    const { newData, key } = e;
    const ids = (newData.projectId ?? projectIds) + '';
    const capitalIncreasePlanDetailId = (newData.capitalIncreasePlanDetailId ?? 0) + '';
    mutate(
      {
        projectIds: ids,
        capitalIncreasePlanDetailId,
      },
      {
        onSuccess: response => {
          if (response.items) {
            const { items } = response;
            if (!items?.length) return;

            const updatedRow = { ...getRowData(items[0]), id: key };
            if (!updatedRow) return;

            gridRef.current
              ?.instance()
              .getDataSource()
              ?.store()
              .update(e.key, updatedRow)
              .then(() => {
                // console.log('response:', response);
              })
              .catch(error => console.error('error:', error));
          }
        },
        onError: error => {
          notification.error(error.message);
        },
      }
    );
  };

  const [reportTemplateId, details] = methods.watch(['reportTemplateId', 'items']);
  const [reportTemplate, setReportTemplate] = useState<ReportTemplate>();
  const handleSelectReportTemplate = useMemo(
    () => (templateId: number | null) => {
      if (!templateId) return;
      createQueryByIdFn<ReportTemplate>('report-template')(templateId)
        .then(response => {
          setReportTemplate(response);
          methods.setValue('numberOfCode', response.numberOfCode);
          methods.setValue('name', response.name);
          methods.setValue('content', response.content);

          //set columns
          const items = response.reportTemplateDetails
            .filter(item => item.departmentId)
            .sort((a, b) => {
              return (a?.columnIndex ?? 0) - (b?.columnIndex ?? 0);
            });

          const visibleColumnMap = getVisibleColumnMap(response.visibleColumns || '', items);

          setDataGridState({
            state: visibleColumnMap,
            columnTree: createTreeConfig(items, visibleColumnMap),
          });

          gridRef.current
            ?.instance()
            .getDataSource()
            .store()
            .insert({
              key: -getRandomNumber(),
              projectId: 0,
              capitalIncreasePlanId: 0,
            })
            .catch(() => {});

          // setDataSource([]);
          setTimeout(() => setDetailsToTable(details), 100);

          //set data for columns
        })
        .catch(error => {
          console.error('error:', error);
        });
    },
    [methods, details, setDetailsToTable]
  );

  useEffect(() => {
    if (reportTemplateId) {
      handleSelectReportTemplate(reportTemplateId);
    }
  }, [reportTemplateId, handleSelectReportTemplate]);

  const handleEditorPreparing = (e: EditorPreparingEvent<any, any>) => {
    const [reportTemplateId, year] = methods.getValues(['reportTemplateId', 'year']);
    if (!reportTemplateId || !year) {
      e.cancel = true;
    }

    if (e.dataField === 'capitalIncreasePlanDetailId') {
      e.editorOptions.dataSource = budgetSourceCode?.items.filter(i => {
        return i.projectId === e.row?.data.projectId;
      });
    }
  };

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current?.instance()?.repaint();
    }

    return () => {
      setDataSource([]);
    };
  }, [dataGridState]);

  const renderColumns = (
    columns: ColumnProps[] | undefined,
    visibleColumnMap: Record<string, VisibleColumnItem>
  ) => {
    if (!columns) return null;

    return columns.map(col => {
      const { dataField, visible, width } = visibleColumnMap?.[col.dataField] || col;
      const props = { ...col, width, visible, dataField };
      const columnDisplayProps = columnDisplayPropsMap[col.valueType || 3];
      return (
        <Column
          {...props}
          {...columnDisplayProps}
          allowHiding
          allowEditing={false}
          key={`${col.id}_${col.dataField}`}
          customizeText={cellInfo => {
            // Kiểm tra nếu giá trị là undefined, null hoặc NaN
            if (
              cellInfo.value === undefined ||
              cellInfo.value === null ||
              (typeof cellInfo.value === 'number' && isNaN(cellInfo.value))
            ) {
              return ''; // Trả về chuỗi rỗng thay vì NaN
            }

            // Kiểm tra nếu giá trị có thể chuyển đổi thành số
            const numValue = Number(cellInfo.value);
            if (!isNaN(numValue) && col.valueType === 1) {
              return customizeNumberCell()(cellInfo);
            }

            // Trả về giá trị gốc cho các trường hợp khác
            return (cellInfo.value as string)?.toString() || '';
          }}
          cssClass="whitespace-pre-wrap"
        >
          {renderColumns(col.columns, visibleColumnMap)}
        </Column>
      );
    });
  };

  const [reportTemplateName, userCreatedName] = methods.getValues([
    'reportTemplateName',
    'userCreatedName',
  ]);

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
  };

  const getAllData = (e: ClickEvent) => {
    const instance = gridRef.current?.instance?.();
    if (!instance) return;

    const dataSource = instance.getDataSource();
    const store = dataSource?.store();
    store
      .load()
      .then(data => {
        const items = data as any[];
        const details: TemplateStatisticReportResponse[] = [];
        items.forEach((item, index) => {
          const dtos: TemplateStatisticsDetailReportDto[] = [];
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          Object.keys(item).forEach(key => {
            const value = item[key];
            const reportTemplateItem = reportTemplate?.reportTemplateDetails.find(
              i => i.nameField === key
            );
            if (reportTemplateItem) {
              dtos.push({
                id: -getRandomNumber(),
                displayCaption: reportTemplateItem?.displayCaption || '',
                nameField: key,
                parentId: reportTemplateItem?.parentId,
                valueType: reportTemplateItem?.valueType || 3,
                formula: reportTemplateItem?.formula || '',
                projectId: item.projectId,
                reportTemplateDetailId: reportTemplateItem?.id,
                capitalIncreasePlanDetailId: item.capitalIncreasePlanDetailId,
                value: String(value),
                templateStatisticsProjectId: 0,
              });
            }
          });
          const detail: TemplateStatisticReportResponse = {
            id: -getRandomNumber(),
            ordinalNumber: index,
            projectId: item.projectId,
            capitalIncreasePlanDetailId: item.capitalIncreasePlanDetailId,
            templateStatisticsId: isNaN(Number(editId)) ? 0 : Number(editId),
            templateStatisticsDetailReportDto: [...dtos],
          };
          details.push(detail);
        });
        methods.setValue('items', details);
        handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
      })
      .catch(error => console.error('error:', error));
  };

  return (
    <Form {...methods}>
      <form>
        <PageLayout
          header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
          onCancel={goBackToList}
          canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
          onSaveChange={e => {
            getAllData(e);
          }}
          isSaving={loading}
          customElementLeft={
            <>
              <Button
                text={createLabel}
                className="mb-2 uppercase md:mb-0"
                stylingMode="outlined"
                type="default"
                icon="plus"
                onClick={onCreateNew}
              />
            </>
          }
        >
          <div className="grid max-w-screen-2xl grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel
                name="reportTemplateId"
                id="reportTemplateId"
                className="hidden w-[81px] md:block"
              >
                {t('fields.reportTemplateId')}
              </FormLabel>
              <FormField
                label={t('fields.reportTemplateId')}
                className="min-w-0 flex-1"
                isRequired
                name="reportTemplateId"
              >
                <FormCombobox<ReportTemplate>
                  model="report-template"
                  queryKey={[QUERIES.REPORT_TEMPLATE]}
                  defaultText={reportTemplateName}
                  placeholder={`${selectLabel} ${t('fields.reportTemplateId')}`}
                />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center lg:col-span-16 2xl:col-span-12">
              <FormLabel name="name" id="name" className="hidden w-[81px] md:block">
                {t('fields.name')}
              </FormLabel>
              <FormField className="min-w-0 flex-1" name="name" label={t('fields.name')}>
                <TextBox placeholder={`${enterLabel} ${t('fields.name')}`} />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center lg:col-span-24 2xl:col-span-18">
              <FormLabel name="content" id="content" className="hidden w-[81px] md:block">
                {t('fields.content')}
              </FormLabel>
              <FormField className="min-w-0 flex-1" name="content" label={t('fields.content')}>
                <TextBox placeholder={`${enterLabel} ${t('fields.content')}`} />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center lg:col-span-24 2xl:col-span-18">
              <FormLabel name="note" id="note" className="hidden w-[81px] md:block">
                {t('fields.note')}
              </FormLabel>
              <FormField className="min-w-0 flex-1" name="note" label={t('fields.note')}>
                <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
              </FormField>
            </div>
            <div className="col-span-1 hidden 2xl:col-span-6 2xl:block"></div>
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel
                name="templateStatisticsTime"
                id="templateStatisticsTime"
                className="hidden w-[81px] md:block"
              >
                {t('fields.templateStatisticsTime')}
              </FormLabel>
              <FormField
                className="min-w-0 flex-1"
                name="templateStatisticsTime"
                type="date"
                label={t('fields.templateStatisticsTime')}
              >
                <DateBox
                  placeholder={`${selectLabel} ${t('fields.templateStatisticsTime')}`}
                  pickerType="calendar"
                  focusStateEnabled={false}
                />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel
                name="userCreatedId"
                id="userCreatedId"
                className="hidden w-[81px] md:block"
              >
                {t('fields.userCreatedId')}
              </FormLabel>
              <FormField
                className="min-w-0 flex-1"
                name="userCreatedId"
                label={t('fields.userCreatedId')}
              >
                <FormCombobox
                  model="user"
                  queryKey={[QUERIES.USERS]}
                  defaultText={userCreatedName}
                  placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel name="year" id="year" className="hidden w-[81px] md:block xl:w-[25px]">
                {t('fields.year')}
              </FormLabel>
              <FormField
                isRequired
                className="min-w-0 flex-1"
                name="year"
                type="date"
                label={t('fields.year')}
              >
                <DateBox
                  calendarOptions={{
                    maxZoomLevel: 'decade',
                    minZoomLevel: 'decade',
                  }}
                  displayFormat={'year'}
                  placeholder={`${selectLabel} ${t('fields.year')}`}
                  pickerType="calendar"
                  focusStateEnabled={false}
                />
              </FormField>
            </div>
            <div className="col-span-1 flex items-center justify-start space-x-4 lg:col-span-24">
              <Button
                text={isPending ? 'Đang lấy dữ liệu...' : getDataLabel}
                className="w-auto"
                stylingMode="contained"
                type="default"
                icon="search"
                onClick={() => {
                  onSubmit();
                }}
              />
              <Button
                text={t('toolbar.export', { ns: 'dataTable' })}
                className="w-auto"
                stylingMode="contained"
                type="success"
                icon="file"
                onClick={() => {
                  // Hàm chuyển đổi số thành chữ cái Excel

                  const dataGrid = gridRef.current?.instance();
                  const columnLength = dataGrid?.getVisibleColumns()?.length ?? 0;
                  const lastColumnLetter = getExcelColumnLetter(columnLength); // Chuyển đổi số thành chữ cái Excel
                  const [numberOfCode, name, content] = methods.getValues([
                    'numberOfCode',
                    'name',
                    'content',
                  ]);

                  const workbook = new Workbook();
                  const worksheet = workbook.addWorksheet(numberOfCode ?? 'A');

                  // Thêm dòng mã vào dòng 1
                  const codeRow = worksheet.addRow([numberOfCode]);
                  codeRow.font = { name: 'Times New Roman', size: 16, bold: true };
                  codeRow.alignment = { horizontal: 'center', vertical: 'middle' };
                  // Merge các ô trong dòng tiêu đề
                  worksheet.mergeCells(`A1:${lastColumnLetter}1`);

                  // Thêm dòng tên biểu mẫu vào dòng 2
                  const nameRow = worksheet.addRow([name]);
                  nameRow.font = { name: 'Times New Roman', size: 16, bold: true };
                  nameRow.alignment = { horizontal: 'center', vertical: 'middle' };
                  // Merge các ô trong dòng tiêu đề
                  worksheet.mergeCells(`A2:${lastColumnLetter}2`);

                  // Thêm dòng nội dung vào dòng 2
                  const contentRow = worksheet.addRow([content]);
                  contentRow.font = { name: 'Times New Roman', size: 12, italic: true };
                  contentRow.alignment = { horizontal: 'center', vertical: 'middle' };
                  // Merge các ô trong dòng mô tả
                  worksheet.mergeCells(`A3:${lastColumnLetter}3`);

                  // Xuất dữ liệu từ DataGrid
                  exportDataGrid({
                    component: dataGrid,
                    worksheet: worksheet,
                    topLeftCell: { row: 4, column: 1 }, // Bắt đầu xuất dữ liệu từ dòng 4
                    customizeCell: function (options) {
                      options.excelCell.font = { name: 'Times New Roman', size: 12 };
                      options.excelCell.alignment = { horizontal: 'left', wrapText: true };
                      const removeHtmlTags = (html: string): string => {
                        const withoutTags = html
                          // Thay thế <br> thành ký tự xuống dòng
                          .replace(/<br\s*\/?>/gi, '\n')
                          // Thay thế thẻ đóng </p> thành xuống dòng
                          .replace(/<\/p>/gi, '\n')
                          // Loại bỏ thẻ mở <p ...>
                          .replace(/<p[^>]*>/gi, '')
                          // Loại bỏ các thẻ HTML còn lại
                          .replace(/<[^>]+>/g, '');
                        return decode(withoutTags).trim();
                      };

                      if (options.gridCell && options.gridCell.column) {
                        if (typeof options.excelCell.value === 'string') {
                          options.excelCell.value = removeHtmlTags(
                            options.excelCell.value as string
                          );
                        }

                        // Định dạng số với dấu phẩy phân cách hàng nghìn
                        if (typeof options.excelCell.value === 'number') {
                          options.excelCell.value = realNumberDecimalFormat(
                            (options.excelCell.value as number).toString(),
                            2
                          );
                          options.excelCell.alignment = { horizontal: 'right' };
                        }
                      }
                    },
                  })
                    .then(function () {
                      workbook.xlsx
                        .writeBuffer()
                        .then(function (buffer) {
                          saveAs(
                            new Blob([buffer], { type: 'application/octet-stream' }),
                            'thong-ke-theo-bieu-mau-bao-cao.xlsx'
                          );
                        })
                        .catch(function (e) {
                          console.error('Error:', e);
                        });
                    })
                    .catch(function (e) {
                      console.error('Error:', e);
                    });
                }}
              />
            </div>
          </div>
          <div className="w-full">
            <DataGrid
              showBorders
              showRowLines
              ref={gridRef}
              keyExpr={'id'}
              showColumnLines
              columnAutoWidth
              allowColumnResizing
              allowColumnReordering
              export={{ enabled: true }}
              dataSource={dataSource}
              onRowUpdating={handleRowUpdate}
              id={`${TABLES.TEMPLATE_STATISTICS_REPORT}`}
              className="column-header-wrap max-h-[calc(100vh-21rem)] w-full overflow-auto"
              onEditorPreparing={handleEditorPreparing}
              columnResizingMode="widget"
              wordWrapEnabled
              columnFixing={{
                enabled: true,
                texts: {
                  fix: 'Cố định',
                  leftPosition: 'Cố định bên trái',
                  rightPosition: 'Cố định bên phải',
                  unfix: 'Bỏ cố định',
                },
              }}
              onCellPrepared={onCellPrepared}
            >
              <LoadPanel enabled={false} />
              <Grouping contextMenuEnabled={true} expandMode="rowClick" />
              <Scrolling mode="standard" rowRenderingMode="standard" showScrollbar="always" />
              <GroupPanel visible />
              <Paging enabled defaultPageSize={10} />
              <Pager
                visible
                showInfo
                showNavigationButtons
                showPageSizeSelector
                displayMode="adaptive"
                allowedPageSizes={[5, 10, 50, 100, 'all']}
              />
              <HeaderFilter visible>
                <Search enabled={true} mode="contains" />
              </HeaderFilter>
              <FilterRow visible showOperationChooser />
              <GroupPanel visible />
              <StateStoring enabled type="custom" customLoad={() => dataGridState?.state} />
              <Editing mode="cell" allowUpdating allowAdding allowDeleting selectTextOnEditStart />
              <Toolbar>
                <Item
                  widget="dxButton"
                  options={{
                    icon: 'plus',
                    onClick: () => {
                      const instance = gridRef.current?.instance();

                      instance
                        ?.getDataSource()
                        .store()
                        .insert({
                          key: -getRandomNumber(),
                          projectId: 0,
                          capitalIncreasePlanDetailId: 0,
                        })
                        .catch(e => console.log(e));

                      instance?.refresh().catch(e => console.error(e));
                    },
                  }}
                />
                <Item name="columnChooserButton" />
                <Item name="exportButton" />
              </Toolbar>

              <Column
                allowEditing
                visibleIndex={0}
                dataField="projectId"
                caption={t('fields.projectId')}
                filterOperations={['contains']}
              >
                <Lookup
                  valueExpr={'id'}
                  dataSource={projects}
                  displayExpr={displayExpr(['name'])}
                />
              </Column>
              <Column
                allowEditing
                visibleIndex={1}
                dataField="capitalIncreasePlanDetailId"
                caption={t('fields.capitalIncreasePlanDetailId')}
              >
                <Lookup
                  allowClearing
                  valueExpr={'id'}
                  dataSource={budgetSourceCode?.items}
                  displayExpr={displayExpr(['budgetSourceCodeCode', 'budgetSourceCodeName'])}
                />
              </Column>
              {dataGridState?.columnTree &&
                renderColumns(dataGridState?.columnTree, dataGridState.state)}
              {/* thao tác */}
              <Column type="buttons" fixedPosition="right" fixed>
                <GridButton
                  name="delete"
                  onClick={e => {
                    // Get the data for the row
                    const rowData = e?.row?.data;

                    // Delete the row without confirmation
                    const dataSource = gridRef.current?.instance().getDataSource();
                    dataSource
                      ?.store()
                      .remove(rowData.id)
                      .catch(e => console.error(e));
                    dataSource?.reload().catch(e => console.error(e));
                  }}
                />
              </Column>
            </DataGrid>
          </div>
        </PageLayout>
      </form>
    </Form>
  );
};
