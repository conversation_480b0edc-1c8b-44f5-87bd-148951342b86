import { z } from 'zod';

export const reportsOfDepartmentDisbursementProgressSchema = z.object({
  departmentId: z.number(),
  departmentName: z.string(),
  totalProject: z.number(),
  totalPlannedCapitalMillionVnd: z.number(),
  cumulativeDisbursedValueMillionVnd: z.number(),
});

export type ReportsOfDepartmentDisbursementProgress = z.infer<
  typeof reportsOfDepartmentDisbursementProgressSchema
>;
