import { z } from 'zod';

export const aiWarningOfContructionProgressSchema = z.object({
  id: z.number(),
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  creatorId: z.number().nullable(),
  createdTime: z.date().nullable(),
  updaterId: z.number().nullable(),
  note: z.string().optional().nullable(),
  isActive: z.boolean().default(true),
  content: z.string().nullable(),
  statusId: z.number().nullable(),
  professionType: z.number().nullable(),
  warningType: z.number().nullable(),
  solution: z.string().nullable(),
  recipientId: z.number().nullable(),
  projectId: z.number().nullable(),
  refId: z.number().nullable(),
  name: z.string().nullable(),
});

export type AIWarningOfContructionProgress = z.infer<typeof aiWarningOfContructionProgressSchema>;
