import { Form, FormField, FormLabel } from '@/components/ui/form';
import { viewLabel } from '@/constant';
import { callbackWithTimeout } from '@/lib/utils';
import { Button, DateBox } from 'devextreme-react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

const queryTypeSchema = z.object({
  year: z.date(),
});

type Props = {
  onSearch: () => void;
  form: UseFormReturn<z.infer<typeof queryTypeSchema>>;
};
export const YearProjectDataFilter = ({ onSearch, form }: Props) => {
  const { t } = useTranslation('savingInBiddingReport');

  return (
    <Form {...form}>
      <form
        onSubmit={e => {
          e.preventDefault();
          const submit = async () => {
            const canSubmit = await form.trigger();
            if (canSubmit) {
              onSearch();
            }
          };
          callbackWithTimeout(submit);
        }}
        aria-description="Bộ lọc báo cáo"
        className="flex w-full flex-col gap-x-4 gap-y-4   md:max-w-screen-lg md:flex-row md:items-center"
      >
        <div className="flex items-center">
          <FormLabel name="year" htmlFor="year" className="hidden md:block">
            {t('page.year')}
          </FormLabel>
          <FormField name="year" className="w-full md:w-[110px]" type="date" label={t('page.year')}>
            <DateBox
              type="date"
              pickerType="calendar"
              calendarOptions={{
                maxZoomLevel: 'decade',
                minZoomLevel: 'decade',
              }}
              displayFormat={'year'}
              focusStateEnabled={false}
            />
          </FormField>
        </div>
        {/* Nút tìm kiếm */}
        <div className="flex items-center">
          <Button
            text={viewLabel} // Nhãn của nút
            className="w-full md:w-fit"
            stylingMode="contained" // Giao diện của nút
            type="default"
            icon="search" // Icon tìm kiếm
            useSubmitBehavior // Cho phép nút tự động submit form
          />
        </div>
      </form>
    </Form>
  );
};
