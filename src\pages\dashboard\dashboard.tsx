import axiosInstance, { request } from '@/axios-instance';
import { PageLayout } from '@/components/page-layout';
import { TickerCard } from '@/components/ticker-card/TickerCard';
import { realNumberDecimalFormat } from '@/lib/number';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { FolderKanban, Wallet, Banknote, LineChart } from 'lucide-react';
import {
  ProjectDeploymentStatusReport,
  Notification,
  ReportsOfDepartmentDisbursementProgress as ReportsOfDepartmentDisbursementProgressType,
  CapitalPlanDisbursementProgressReport,
} from '@/types';
import { DasboardCapitalPlanDisbursementProgressReport } from './dasboard-capital-plan-disbursement-progress-report';
import { DashboardReportsOfDepartmentDisbursementProgress } from './dashboard-reports-of-department-disbursement-progress';
import { DashboardProjectDeploymentStatusReport } from './dashboard-project-deployment-status-report';

import { DashboardNotification } from './dashboard-notification';
import DashboardBoardOfDirectorsWorkScheduleMobile from './dashboard-board-of-directors-work-schedule-mobile';
import { AIWarningCostOverrunDataTable } from './ai_cost_overrun_warning';
import { AIWarningOfConstructionProgressDataTable } from '../ai-warning-of-construction-progress';

type DashBoardRow1 = {
  projectCount: number;
  totalPlannedDisbursementAmount: number;
  totalDisbursement: number;
  disbursementRatePercent: number;
};

export const Dashboard = () => {
  const [year] = useState<Date>(new Date());
  const { data: row1 } = useQuery({
    queryKey: ['/dashboard/get-row-1'],
    queryFn: () => {
      return request<DashBoardRow1>(
        axiosInstance.get(`/dashboard/get-row-1/${year.toISOString().slice(0, 10)}`)
      );
    },
  });

  const { data: row2 } = useQuery({
    queryKey: ['/dashboard/get-row-2'],
    queryFn: () => {
      return request<CapitalPlanDisbursementProgressReport[]>(
        axiosInstance.get(`/dashboard/get-row-2`)
      );
    },
  });
  const row2Items = row2 || [];

  const { data: row3 } = useQuery({
    queryKey: ['/dashboard/get-row-3'],
    queryFn: () => {
      return request<ProjectDeploymentStatusReport[]>(
        axiosInstance.get(`/dashboard/get-row-3/${year.toISOString().slice(0, 10)}`)
      );
    },
  });
  const row3Items = row3 || [];

  const { data: reportsOfDepartmentDisbursementProgress } = useQuery({
    queryKey: ['/dashboard/get-left-row-4'],
    queryFn: () => {
      return request<ReportsOfDepartmentDisbursementProgressType[]>(
        axiosInstance.get(`/dashboard/get-left-row-4`)
      );
    },
  });

  const reportsOfDepartmentDisbursementProgressItems =
    reportsOfDepartmentDisbursementProgress?.sort((a, b) => a.departmentId - b.departmentId);
  const { data: row3Right } = useQuery({
    queryKey: ['/dashboard/get-right-row-4'],
    queryFn: () => {
      return request<Notification[]>(axiosInstance.get(`/dashboard/get-right-row-4`));
    },
  });
  const row3RightItems = row3Right || [];

  return (
    <PageLayout contentClassName="flex flex-col gap-x-8 gap-y-4 ">
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
          <TickerCard
            title={`Tổng số dự án`}
            icon={<FolderKanban className="h-10 w-10" />}
            tone="blue"
            value={row1?.projectCount ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
            link="/summary-report/capital-plan-disbursement-progress-report"
          />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
          <TickerCard
            title="Tổng kế hoạch vốn năm"
            icon={<Wallet className="h-10 w-10" />}
            tone="pink"
            value={row1?.totalPlannedDisbursementAmount ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
            link="/summary-report/capital-plan-disbursement-progress-report"
          />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
          <TickerCard
            title="LŨY KẾ GIÁ TRỊ GIẢI NGÂN ĐẾN"
            icon={<Banknote className="h-10 w-10" />}
            tone="yellow"
            value={row1?.totalDisbursement ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
            link="/summary-report/capital-plan-disbursement-progress-report"
          />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
          <TickerCard
            title="TỶ LỆ GIẢI NGÂN ĐẾN"
            icon={<LineChart className="h-10 w-10" />}
            tone="green"
            value={row1?.disbursementRatePercent || 0}
            formatValue={value => `${realNumberDecimalFormat(value.toString(), 2)}%`}
            link="/summary-report/capital-plan-disbursement-progress-report"
          />
        </div>
      </div>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
          <DasboardCapitalPlanDisbursementProgressReport items={row2Items} />
        </div>
      </div>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-12">
          <DashboardProjectDeploymentStatusReport items={row3Items} />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-12">
          <DashboardReportsOfDepartmentDisbursementProgress
            items={reportsOfDepartmentDisbursementProgressItems || []}
          />
        </div>
      </div>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-10">
          <DashboardNotification items={row3RightItems} />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-14">
          <div className="p-4">
            <p className="mb-2 text-xl font-bold">Lịch làm việc</p>
            <DashboardBoardOfDirectorsWorkScheduleMobile />
          </div>
        </div>
      </div>
      <div className="mt-8 grid max-w-full grid-cols-1 gap-x-8 gap-y-4 ">
        <AIWarningOfConstructionProgressDataTable heightFitContent isForDashBoard />
      </div>
      <div className="mt-8 hidden max-w-full grid-cols-1 gap-x-8 gap-y-4 ">
        <AIWarningCostOverrunDataTable />
      </div>
    </PageLayout>
  );
};
