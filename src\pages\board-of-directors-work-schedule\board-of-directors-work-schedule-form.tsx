import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { enterLabel, MUTATE, PATHS, PERMISSIONS, QUERIES, selectLabel } from '@/constant';
import {
  useAuth,
  useDataTable,
  useEntity,
  useFormHandler,
  usePermission,
  useSendNotification,
} from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { formatDate, toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import {
  BoardOfDirectorsWorkScheduleEditableTable,
  boardOfDirectorsWorkSchedulePrintTemplate,
  groupBoardOfDirectorsSchedule,
  sortDetails,
} from '@/pages/board-of-directors-work-schedule';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import {
  BoardOfDirectorsWorkSchedule,
  BoardOfDirectorsWorkScheduleDetail,
  boardOfDirectorsWorkScheduleSchema,
  defaultValuesBoardOfDirectorsWorkSchedule,
  Department,
  Position,
  User,
} from '@/types';
import dayjs from 'dayjs';
import { DateBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { useReactToPrint } from 'react-to-print';
import { safeRenderString } from '@/lib/print';
import { removeAccents } from '@/lib/text';
import { snakeCase } from 'lodash';

const onBoardOfDirectorsWorkScheduleMutationSuccess = createMutationSuccessFn(
  'boardOfDirectorsWorkSchedule'
);

export const BoardOfDirectorsWorkScheduleForm = () => {
  const { id: editId } = useParams();

  const { t } = useTranslation('boardOfDirectorsWorkSchedule');
  const exportFileName = snakeCase(removeAccents(t('model')));

  const role = usePermission(PERMISSIONS.BOARD_OF_DIRECTORS_WORK_SCHEDULE);
  const { user } = useAuth();
  const printRef = useRef(null);

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(
    PATHS.BOARD_OF_DIRECTORS_WORK_SCHEDULE
  );

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesBoardOfDirectorsWorkSchedule,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { sendNotify } = useSendNotification();

  const sendNotificationsCreate = (refId: number) => {
    const receivers = getReceivers();
    sendNotifications(refId, receivers);
  };

  const sendNotificationsUpdate = (refId: number) => {
    const receivers = getReceivers(detail => detail.id <= 0); //chỉ gửi dòng mới ở details
    sendNotifications(refId, receivers);
  };

  const getReceivers = (filter?: (item: BoardOfDirectorsWorkScheduleDetail) => boolean) => {
    const receivers: number[] = [];
    methods
      .getValues()
      ?.boardOfDirectorsWorkScheduleDetails.filter(item => (filter ? filter(item) : true))
      .forEach(item => {
        if (item.chairpersonId) receivers.push(item.chairpersonId);
        if (item.contentPreparerId) receivers.push(item.contentPreparerId);
        if (item.memberIdArray) receivers.push(...item.memberIdArray);
      });
    return [...new Set(receivers)];
  };

  const sendNotifications = (refId: number, receivers: number[]) => {
    if (receivers.length <= 0) return;
    const data = methods.getValues();
    const sendTemplate = {
      title: t('boardOfDirectorsWorkSchedule.newSchedule', {
        ns: 'sendNotification',
      }),
      content: `${t('fields.weekNumber')}: ${data.weekNumber}, ${t('fields.fromDate').toLocaleLowerCase()} ${dayjs(data.fromDate).format('DD/MM/YYYY')} ${t('fields.toDate').toLocaleLowerCase()} ${dayjs(data.toDate).format('DD/MM/YYYY')}`,
      typeNotification: PATHS.BOARD_OF_DIRECTORS_WORK_SCHEDULE,
      refId: refId,
      userIds: receivers,
    };
    sendNotify(sendTemplate);
  };

  const { handleSubmit, loading, methods } = useFormHandler<BoardOfDirectorsWorkSchedule>({
    queryKey: [MUTATE.BOARD_OF_DIRECTORS_WORK_SCHEDULE, editId],
    mutateKey: [MUTATE.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
    readFn: createQueryByIdFn<BoardOfDirectorsWorkSchedule>('board-of-directors-work-schedule'),
    createFn: createPostMutateFn<BoardOfDirectorsWorkSchedule>('board-of-directors-work-schedule'),
    updateFn: createPutMutateFn<BoardOfDirectorsWorkSchedule>('board-of-directors-work-schedule'),
    formatPayloadFn: data => ({
      ...data,
      boardOfDirectorsWorkScheduleTime: toLocaleDate(data.boardOfDirectorsWorkScheduleTime)!,
      fromDate: toLocaleDate(data.fromDate)!,
      toDate: toLocaleDate(data.toDate)!,
      boardOfDirectorsWorkScheduleDetails: data.boardOfDirectorsWorkScheduleDetails.reduce<
        BoardOfDirectorsWorkScheduleDetail[]
      >((acc, { departmentIdArray, memberIdArray, ...item }) => {
        if (item.workDate) {
          acc.push({
            ...item,
            workDate: toLocaleDate(item.workDate),
            workTime: toLocaleDate(item.workTime!),
            departmentIds: departmentIdArray?.toString(),
            memberIds: memberIdArray?.toString(),
          });
        }
        return acc;
      }, []),
    }),
    formatResponseFn: data => {
      const details = data.boardOfDirectorsWorkScheduleDetails.map(item => ({
        ...item,
        workTime: toDateType(item.workTime!),
        workDate: toDateType(item.workDate!),
        departmentIdArray: item.departmentIds?.split(',').map(Number),
        memberIdArray: item.memberIds?.split(',').map(Number),
      }));
      const shortedDetails = sortDetails(details);

      return {
        ...data,
        boardOfDirectorsWorkScheduleTime: toDateType(data.boardOfDirectorsWorkScheduleTime)!,
        fromDate: toDateType(data.fromDate)!,
        toDate: toDateType(data.toDate)!,
        boardOfDirectorsWorkScheduleDetails: shortedDetails,
      };
    },
    onCreateSuccess: responseData => {
      onBoardOfDirectorsWorkScheduleMutationSuccess(responseData);
      goToUpdate(responseData);

      //send notification
      sendNotificationsCreate(responseData);
    },
    onUpdateSuccess: data => {
      onBoardOfDirectorsWorkScheduleMutationSuccess(data);
      sendNotificationsUpdate(Number(editId));
    },
    formOptions: {
      resolver: zodResolver(boardOfDirectorsWorkScheduleSchema),
      defaultValues,
    },
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<BoardOfDirectorsWorkSchedule, PeriodFilter>({
    deleteFn: createDeleteMutateFn('board-of-directors-work-schedule'),
    deleteKey: [MUTATE.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    // reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };

  const { list: departments } = useEntity<Department>({
    queryKey: [QUERIES.DEPARTMENT],
    model: 'department',
  });

  const { list: users } = useEntity<User>({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: positions } = useEntity<Position>({
    queryKey: [QUERIES.POSITION],
    model: 'position',
  });

  const preprocessPrint = (id: string): { html: string; error: any } => {
    if (id === 'new') {
      return safeRenderString(boardOfDirectorsWorkSchedulePrintTemplate, {
        context: {
          ...defaultValuesBoardOfDirectorsWorkSchedule,
          boardOfDirectorsWorkScheduleDetails: [],
        },
      });
    }

    // shallow copy form data
    const formData = {
      ...methods.getValues(),
    };

    const tableTemplate = safeRenderString(boardOfDirectorsWorkSchedulePrintTemplate, {
      context: {
        ...formData,
        fromDate: formatDate(toLocaleDate(formData.fromDate), 'dd/MM/yyyy'),
        toDate: formatDate(toLocaleDate(formData.toDate), 'dd/MM/yyyy'),
        boardOfDirectorsWorkScheduleDetails: groupBoardOfDirectorsSchedule(
          formData.boardOfDirectorsWorkScheduleDetails,
          departments,
          users,
          positions
        ),
        sessionName: {
          1: t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.morning') ?? '',
          2: t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.afternoon') ?? '',
        },
        notes: (formData?.note ?? '').split(/\r?\n/),
      },
    });

    if (tableTemplate.error) {
      console.error('Error rendering string:', tableTemplate.error);
      return { html: '', error: tableTemplate.error };
    }

    if (!tableTemplate.html) {
      console.error;
    }

    return tableTemplate;
  };

  // Khởi tạo mẫu in
  const [printoutElement, setPrintoutElement] = useState(
    <div ref={printRef} dangerouslySetInnerHTML={{ __html: '' }} />
  );

  const printFn = useReactToPrint({
    content: () => printRef.current,
    pageStyle: `
            @media print {
              body { -webkit-print-color-adjust: exact; }
              .dx-datagrid-headers { background-color: #f5f5f5 !important; }
              .dx-datagrid-rowsview { font-size: 12px !important; }
            }
          `,
    // removeAfterPrint: true,
    onBeforeGetContent: () => {
      const table = preprocessPrint(editId as string);
      // cập nhật lại mẫu in
      setPrintoutElement(<div ref={printRef} dangerouslySetInnerHTML={{ __html: table.html }} />);
      return Promise.resolve();
    },
  });
  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <Button
                  type="default"
                  stylingMode="contained"
                  icon="print"
                  text={t('content.printA4', { ns: 'common' })}
                  disabled={editId === 'new'}
                  onClick={() => {
                    if (printRef.current) {
                      printFn();
                    }
                  }}
                />
              </>
            }
          >
            <div className="grid max-w-full grid-cols-1 gap-x-8  gap-y-4 lg:grid-cols-24 xl:max-w-screen-2xl">
              <div className="col-span-1 flex w-full flex-col gap-x-8 gap-y-4 lg:col-span-12">
                <div className="flex items-center">
                  <FormLabel
                    name="weekNumber"
                    htmlFor="weekNumber"
                    className="hidden w-[62px] md:block lg:w-[60px]"
                  >
                    {t('fields.weekNumber')}
                  </FormLabel>
                  <FormField
                    id="weekNumber"
                    name={'weekNumber'}
                    className="min-w-0 flex-1"
                    label={t('fields.weekNumber')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.weekNumber')}`} />
                  </FormField>
                </div>
                <div className="flex w-full items-center lg:w-1/2">
                  <FormLabel
                    name="fromDate"
                    htmlFor="fromDate"
                    className="hidden w-[62px] md:block lg:w-[60px]"
                  >
                    {t('fields.fromDate')}
                  </FormLabel>
                  <FormField
                    id="fromDate"
                    name={'fromDate'}
                    className="min-w-0 flex-1"
                    type="date"
                    label={t('fields.fromDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.fromDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex items-center lg:w-1/2">
                  <FormLabel
                    name="toDate"
                    htmlFor="toDate"
                    className="hidden w-[62px] md:block lg:w-[60px]"
                  >
                    {t('fields.toDate')}
                  </FormLabel>
                  <FormField
                    id="toDate"
                    name={'toDate'}
                    className="min-w-0 flex-1"
                    type="date"
                    label={t('fields.toDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.toDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
                <div className="hidden lg:block lg:h-[34.67px]" />
                <div className="flex items-center">
                  <FormLabel
                    name="boardOfDirectorsWorkScheduleTime"
                    htmlFor="boardOfDirectorsWorkScheduleTime"
                    className="hidden w-[62px] md:block"
                  >
                    {t('fields.boardOfDirectorsWorkScheduleTime')}
                  </FormLabel>
                  <FormField
                    id="boardOfDirectorsWorkScheduleTime"
                    name={'boardOfDirectorsWorkScheduleTime'}
                    className="min-w-0 flex-1"
                    type="date"
                    label={t('fields.boardOfDirectorsWorkScheduleTime')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex items-center">
                  <FormLabel
                    name="userCreatedId"
                    htmlFor="userCreatedId"
                    className="hidden w-[62px] md:block"
                  >
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name={'userCreatedId'}
                    className="min-w-0 flex-1"
                    label={t('fields.userCreatedId')}
                  >
                    <FormCombobox
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      defaultText={methods.getValues('userCreatedName')}
                    />
                  </FormField>
                </div>
              </div>
              <div className="flex items-center lg:col-span-18">
                <FormLabel name="note" htmlFor="note" className="hidden w-[60px] md:block">
                  {t('fields.note')}
                </FormLabel>
                <FormField
                  id="note"
                  name={'note'}
                  className="min-w-0 flex-1"
                  label={t('fields.note')}
                >
                  <TextArea
                    autoResizeEnabled={true}
                    placeholder={`${enterLabel} ${t('fields.note')}`}
                  />
                </FormField>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.form.tabs.detail')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <BoardOfDirectorsWorkScheduleEditableTable
                    role={role}
                    params={{
                      fromDate: formatDate(
                        toLocaleDate(methods.getValues('fromDate')),
                        'dd/MM/yyyy'
                      ),
                      toDate: formatDate(toLocaleDate(methods.getValues('toDate')), 'dd/MM/yyyy'),
                      weekNumber: methods.getValues('weekNumber'),
                      outputFileName: `${exportFileName}`,
                      note: methods.getValues('note') || '',
                    }}
                  />
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: 'none' }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="board-of-directors-work-schedule"
        name={methods.getValues('note')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
      <div id="print-region" className="hidden">
        {printoutElement}
      </div>
    </>
  );
};
