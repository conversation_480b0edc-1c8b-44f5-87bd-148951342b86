import { FormField, FormLabel } from '@/components/ui/form';
import { enterLabel, QUERIES } from '@/constant';
import {
  Contract,
  ContractTaskManagement,
  defaultValuesContractTaskManagement,
  Department,
} from '@/types';
import { TextArea } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { SyntheticEvent, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { useAuth, useEntity, useSendNotification } from '@/hooks';
import { createQueryByIdFn, Model } from '@/services';
import { modelToCamelCase } from '@/lib/utils';
import { getRandomNumber } from '@/lib/number';
import { useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ContractTaskManagementResponse, getNotifyContent } from './utils';

type ContractTaskManagementFormDetailProps = {
  model: Model;
  getValues: UseFormGetValues<ContractTaskManagement>;
  setValue: UseFormSetValue<ContractTaskManagement>;
  handleSubmitPromise: (e?: SyntheticEvent<HTMLElement>) => Promise<ContractTaskManagement>;
  isDisableExecutionResult: boolean;
  isPmoDirector: boolean;
};
export const ContractTaskManagementDirectorApproveForm = ({
  model,
  getValues,
  setValue,
  handleSubmitPromise,
  isDisableExecutionResult,
  isPmoDirector,
}: ContractTaskManagementFormDetailProps) => {
  const { ns } = useMemo(() => {
    const ns = modelToCamelCase(model);
    return {
      ns,
    };
  }, [model]);

  const { t } = useTranslation([ns]);
  const { user } = useAuth();
  const { sendNotify } = useSendNotification();

  const [
    isApprovePmoDirector,
    contractTaskManagementProjectDepartments,
    id,
    projectName,
    contractId,
  ] = getValues([
    'isApprovePmoDirector',
    'contractTaskManagementProjectDepartments',
    'id',
    'projectName',
    'contractId',
  ]);

  const { data: contract } = useQuery({
    queryKey: [QUERIES.CONTRACT, contractId],
    queryFn: () => createQueryByIdFn<Contract>('contract')(contractId || 0),
    enabled: !!contractId,
  });
  const path = useLocation().pathname.replace(/\/(\d+|new)\/?$/, '');
  const getContent = useCallback(
    (opinion: string) => {
      return getNotifyContent({
        projectName: projectName,
        contractNumber: contract?.contractNumber,
        contractName: contract?.contractName,
        opinion: opinion,
      });
    },
    [projectName, contract?.contractNumber, contract?.contractName]
  );
  const canApproveOrReject = !isApprovePmoDirector && !isDisableExecutionResult && isPmoDirector;
  const { list: departments } = useEntity<Department>({
    queryKey: [QUERIES.DEPARTMENT],
    model: 'department',
  });

  return (
    <>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4">
        {/* Ý kiến TP.TC-TH*/}
        <div className="col-span-1 items-center lg:flex lg:w-1/2">
          <FormLabel htmlFor="pmoDirectorOpinion" className="hidden w-[100px] md:block">
            {t(`fields.pmoDirectorOpinion`)}
          </FormLabel>
          <FormField
            id={`pmoDirectorOpinion`}
            name={`pmoDirectorOpinion`}
            className="min-w-0 flex-1 md:w-[500px]"
            label={`${t(`fields.pmoDirectorOpinion`)}`}
          >
            <TextArea
              autoResizeEnabled={true}
              placeholder={`${enterLabel} ${t(`fields.pmoDirectorOpinion`)}`}
              readOnly={!canApproveOrReject}
            />
          </FormField>
        </div>
        {/* đồng ý và từ chối*/}
        <div className="col-span-1 flex items-center justify-end space-x-4 md:w-[500px] lg:w-1/2">
          {canApproveOrReject && (
            <>
              <Button
                id={`isApprove`}
                text="Đồng ý"
                className="uppercase"
                type="success"
                icon="check"
                onClick={e => {
                  setValue('pmoDirectorOpinionTime', new Date());
                  setValue('userApprovePmoDirectorId', user?.userId);
                  setValue('isApprovePmoDirector', true);

                  void handleSubmitPromise(
                    e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                  ).then(data => {
                    const newData = data as ContractTaskManagementResponse;

                    const receivers: number[] = [];
                    if (newData.contractType === 1) {
                      receivers.push(
                        ...(departments
                          ?.filter(d => d.id === 4 && d.departmentDeputyId)
                          .map(d => d.departmentDeputyId!) || [])
                      );
                    } else {
                      receivers.push(newData.departmentPlanningHeadId);
                    }

                    sendNotify({
                      title: t(ns + '.approved', {
                        ns: 'sendNotification',
                      }),
                      content: getContent(newData?.pmoDirectorOpinion || ''),
                      typeNotification: path,
                      refId: Number(id) || null,
                      userIds: receivers,
                    });
                  });
                }}
              />
              <Button
                id={`isNotApprove`}
                text="Từ chối"
                className="uppercase"
                type="danger"
                icon="close"
                onClick={e => {
                  setValue('pmoDirectorOpinionTime', new Date());
                  setValue('userApprovePmoDirectorId', user?.userId);
                  setValue('isApprovePmoDirector', false);
                  setValue('isSendPmoDirector', false);

                  //quay lại bước đầu
                  const newDepartment = {
                    ...defaultValuesContractTaskManagement
                      .contractTaskManagementProjectDepartments[0],
                    id: -getRandomNumber(),
                  };

                  const lastOne = contractTaskManagementProjectDepartments.at(-1);
                  if (lastOne) {
                    newDepartment.submissionContent = lastOne.submissionContent;
                    newDepartment.directorOpinion = lastOne.directorOpinion;
                  }

                  setValue('contractTaskManagementProjectDepartments', [
                    ...contractTaskManagementProjectDepartments,
                    newDepartment,
                  ]);

                  handleSubmitPromise(
                    e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                  )
                    .then(data => {
                      const newData = data as ContractTaskManagementResponse;
                      sendNotify({
                        title: t(ns + '.rechecked', {
                          ns: 'sendNotification',
                        }),
                        content: getContent(newData?.pmoDirectorOpinion || ''),
                        typeNotification: path,
                        refId: Number(id) || null,
                        userIds: [
                          newData.projectManagementDirectorId,
                          newData.departmentProjectHeadId,
                        ].filter(receiver => !!receiver),
                      });
                    })
                    .catch(error => console.error('error:', error));
                }}
              />
            </>
          )}
        </div>
      </div>
    </>
  );
};
