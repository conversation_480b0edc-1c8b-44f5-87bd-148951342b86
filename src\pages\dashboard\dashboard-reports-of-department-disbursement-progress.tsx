import { ReportsOfDepartmentDisbursementProgress as ReportsOfDepartmentDisbursementProgressType } from '@/types';
import Chart, {
  CommonSeriesSettings,
  Series,
  Legend,
  Title,
  ValueAxis,
} from 'devextreme-react/chart';
import { RowLabel } from './row-label';
import { Link } from 'react-router-dom';
import { PATHS } from '@/constant';

export const DashboardReportsOfDepartmentDisbursementProgress = ({
  items,
}: {
  items: ReportsOfDepartmentDisbursementProgressType[];
}) => {
  return (
    <div className="bg-white p-4">
      <div className="mb-2 text-xl font-bold">
        <Link to={PATHS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT} className="!text-black">
          Tiến độ giải ngân theo phòng QLDA
        </Link>
      </div>
      <Chart dataSource={items} className="bg-white">
        <CommonSeriesSettings argumentField="departmentName" type="bar" />

        <Series
          color={'#2C8FF3'}
          valueField="totalPlannedCapitalMillionVnd"
          name="Tổng kế hoạch vốn"
        >
          <RowLabel />
        </Series>

        <Series
          color={'#E56C00'}
          valueField="cumulativeDisbursedValueMillionVnd"
          name="Lũy kế giá trị giải ngân"
        >
          <RowLabel />
        </Series>

        <ValueAxis
          title={{ text: 'Số tiền (triệu VNĐ)', font: { weight: 'bold' } }}
          label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
        />
        <Legend verticalAlignment="bottom" horizontalAlignment="center" font={{ weight: 800 }} />
        <Title text="" />
      </Chart>
    </div>
  );
};
