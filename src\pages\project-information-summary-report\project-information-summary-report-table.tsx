import { DevexDataGrid } from '@/components/devex-data-grid';
import { QUERIES, TABLES } from '@/constant';
import { useAuth, useEntity } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { Contractor, ContractorProjectStaffReport } from '@/types';
import { Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { snakeCase } from 'lodash';

const t = translationWithNamespace('contractorProjectStaffReport');
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

type Props = {
  items: ContractorProjectStaffReport[];
  refetch: () => Promise<unknown>;
};
export const ProjectInformationSummaryReportTable = ({ items, refetch }: Props) => {
  const { projects } = useAuth();
  const { list: contractors } = useEntity<Contractor>({
    queryKey: [QUERIES.CONTRACTOR],
    model: 'contractor',
  });
  return (
    <DevexDataGrid
      id={TABLES.CONTRACTOR_PROJECT_STAFF_REPORT}
      dataSource={items}
      onRefresh={() => {
        callbackWithTimeout(refetch);
      }}
      onExporting={onExporting}
      keyExpr={'ordinalNumber'}
    >
      <Export enabled={true} />
      <Editing allowUpdating={false} allowDeleting={false} useIcons />
      <Column dataField="projectId" caption={t('fields.projectId')}>
        <Lookup dataSource={projects} valueExpr={'id'} displayExpr={displayExpr(['name'])} />
      </Column>
      <Column dataField="contractorId" caption={t('fields.contractorId')}>
        <Lookup dataSource={contractors} valueExpr={'id'} displayExpr={displayExpr(['name'])} />
      </Column>
      <Column dataField="tenderPackageName" caption={t('fields.tenderPackageName')} />
      <Column dataField="statusName" caption={t('fields.statusName')} />
      <Column dataField="employeeCode" caption={t('fields.employeeCode')} />
      <Column dataField="employeeName" caption={t('fields.employeeName')} />
      <Column dataField="employeePhone" caption={t('fields.employeePhone')} />
      <Column dataField="careerTrainingName" caption={t('fields.careerTrainingName')} />
      <Column dataField="note" caption={t('fields.note')} />
    </DevexDataGrid>
  );
};
