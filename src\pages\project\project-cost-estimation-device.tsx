import { BasicDialog } from '@/components/basic-dialog';
import { DevexDataGridEditable } from '@/components/devex-data-grid';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { PageLayout } from '@/components/page-layout';
import { FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import { downloadTemplateLabel, enterLabel, PROFESSIONS, selectLabel, TABLES } from '@/constant';
import { useBoolean } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import {
  defaultValuesProjectCostEstimationDevice,
  Project,
  ProjectCostEstimationDevice,
  ProjectTabChildrenProps,
} from '@/types';
import { Button, DateBox, TextBox, Button as DxButton } from 'devextreme-react';
import { SyntheticEvent, useCallback } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Column, Item, IColumnProps } from 'devextreme-react/data-grid';
import { optionsMoney, optionsPercent, optionsQuantity } from './options';

const defaultRow = defaultValuesProjectCostEstimationDevice;

export const ProjectCostEstimationDeviceForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
  isPmDirector,
}: ProjectTabChildrenProps) => {
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue, getValues } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectCostEstimationDeviceDetails', 'id'],
  });

  const updateSummary = useCallback(() => {
    const newCostEstimationDeviceTotalPreTax = getValues(
      'projectCostEstimationDeviceDetails'
    )?.reduce((sum, item) => sum + (item.preTaxValue || 0), 0);
    const newCostEstimationDeviceTotalVat = getValues('projectCostEstimationDeviceDetails')?.reduce(
      (sum, item) => sum + (item.vatTax || 0),
      0
    );
    const newCostEstimationDeviceTotalPostTax = getValues(
      'projectCostEstimationDeviceDetails'
    )?.reduce((sum, item) => sum + (item.postTaxValue || 0), 0);

    setValue('costEstimationDeviceTotalPreTax', newCostEstimationDeviceTotalPreTax);
    setValue('costEstimationDeviceTotalVat', newCostEstimationDeviceTotalVat);
    setValue('costEstimationDeviceTotalPostTax', newCostEstimationDeviceTotalPostTax);
  }, [setValue, getValues]);

  const columns: IColumnProps[] = [
    {
      dataField: 'deviceName',
      caption: t('fields.projectCostEstimationDeviceDetails.deviceName'),
      allowEditing: isPmDirector,
    },
    {
      dataField: 'note',
      caption: t('fields.projectCostEstimationDeviceDetails.note'),
      allowEditing: isPmDirector,
    },
    {
      dataField: 'unitName',
      caption: t('fields.projectCostEstimationDeviceDetails.unitName'),
      allowEditing: isPmDirector,
    },
    {
      dataField: 'quantity',
      caption: t('fields.projectCostEstimationDeviceDetails.quantity'),
      allowEditing: isPmDirector,
      ...optionsQuantity,
    },
    {
      dataField: 'price',
      caption: t('fields.projectCostEstimationDeviceDetails.price'),
      allowEditing: isPmDirector,
      ...optionsMoney,
    },
    {
      dataField: 'preTaxValue',
      caption: t('fields.projectCostEstimationDeviceDetails.preTaxValue'),
      allowEditing: false,
      ...optionsMoney,
    },
    {
      dataField: 'vat',
      caption: t('fields.projectCostEstimationDeviceDetails.vat'),
      allowEditing: isPmDirector,
      ...optionsPercent,
    },
    {
      dataField: 'vatTax',
      caption: t('fields.projectCostEstimationDeviceDetails.vatTax'),
      allowEditing: false,
      ...optionsMoney,
    },
    {
      dataField: 'postTaxValue',
      caption: t('fields.projectCostEstimationDeviceDetails.postTaxValue'),
      allowEditing: false,
      ...optionsMoney,
    },
  ];

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.dataField,
      header: column.caption,
    };
  });

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceDocumentCode"
              htmlFor="costEstimationDeviceDocumentCode"
            >
              {t('fields.documentCode')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceDocumentCode"
              label={t('fields.documentCode')}
            >
              <TextBox
                readOnly={!isPmDirector}
                placeholder={`${enterLabel} ${t('fields.documentCode')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center xl:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceSigningDate"
              htmlFor="costEstimationDeviceSigningDate"
            >
              {t('fields.signingDate')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceSigningDate"
              type="date"
              label={t('fields.signingDate')}
            >
              <DateBox
                placeholder={`${selectLabel} ${t('fields.signingDate')}`}
                readOnly={!isPmDirector}
                pickerType="calendar"
                focusStateEnabled={false}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center xl:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceSigner"
              htmlFor="costEstimationDeviceSigner"
            >
              {t('fields.signerId')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceSigner"
              label={t('fields.signerId')}
            >
              <TextBox
                readOnly={!isPmDirector}
                placeholder={`${enterLabel} ${t('fields.signerId')}`}
              />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalPreTax"
              htmlFor="costEstimationDeviceTotalPreTax"
            >
              {t('fields.costEstimationDeviceTotalPreTax')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalPreTax"
              label={t('fields.costEstimationDeviceTotalPreTax')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalPreTax')}`}
              />
            </FormField>
          </div>
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalVat"
              htmlFor="costEstimationDeviceTotalVat"
            >
              {t('fields.costEstimationDeviceTotalVat')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalVat"
              label={t('fields.costEstimationDeviceTotalVat')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalVat')}`}
              />
            </FormField>
          </div>
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalPostTax"
              htmlFor="costEstimationDeviceTotalPostTax"
            >
              {t('fields.costEstimationDeviceTotalPostTax')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalPostTax"
              label={t('fields.costEstimationDeviceTotalPostTax')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalPostTax')}`}
              />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 xl:col-span-24">
          <DevexDataGridEditable
            id={TABLES.COST_ESTIMATION_DEVICE_DETAIL}
            dataSource={editableData}
            editing={{
              mode: 'cell',
              allowUpdating: isPmDirector,
              allowDeleting: isPmDirector,
              allowAdding: isPmDirector,
              useIcons: true,
              confirmDelete: false,
              newRowPosition: 'last',
            }}
            grouping={{ contextMenuEnabled: false }}
            groupPanel={{ visible: false }}
            repaintChangesOnly
            onEditorPreparing={e => {
              if (e.parentType === 'dataRow' && e.dataField === 'note') {
                e.editorName = 'dxTextArea';
              }
            }}
            onEditorPrepared={e => {
              if (e.parentType === 'dataRow') {
                const element = e.editorElement.querySelector(
                  '.dx-texteditor-input'
                ) as HTMLTextAreaElement;
                if (element) {
                  if (e.dataField === 'note') {
                    element.style.lineHeight = '1';
                    element.style.minHeight = '200px';

                    // // Auto resize function
                    // const autoResize = () => {
                    //   element.style.height = 'auto';
                    //   element.style.height = element.scrollHeight + 'px';
                    //   const overlay = document.querySelector(
                    //     '.dx-datagrid-focus-overlay'
                    //   ) as HTMLDivElement;
                    //   if (overlay) {
                    //     overlay.style.height = element.scrollHeight + 4 + 'px';
                    //   }
                    // };

                    // // Gọi khi load lần đầu
                    // autoResize();

                    // // Gọi khi người dùng gõ
                    // element.addEventListener('input', autoResize);
                  }
                }
              }
            }}
            onRowInserting={e => {
              e.data = {
                ...(e.data as ProjectCostEstimationDevice),
                id: -getRandomNumber(),
              };
            }}
            // onRowInserted={() => {
            //   setValue('projectCostEstimationDeviceDetails', editableData);
            //   updateSummary();
            // }}
            // onRowUpdated={() => {
            //   setValue('projectCostEstimationDeviceDetails', editableData);
            //   updateSummary();
            // }}
            // onRowRemoved={() => {
            //   setValue('projectCostEstimationDeviceDetails', editableData);
            //   updateSummary();
            // }}
            onSavedCustom={dataSource => {
              setValue(
                'projectCostEstimationDeviceDetails',
                dataSource as ProjectCostEstimationDevice[]
              );
              updateSummary();
            }}
            onInitNewRow={e => {
              e.data = { ...defaultRow, projectId };
            }}
            customToolbar={
              <>
                <Item name="addRowButton" />
                <Item location="before">
                  <DxButton
                    stylingMode="text"
                    icon="download"
                    text={downloadTemplateLabel}
                    type="default"
                    onClick={() => {
                      window.open(`/templates/project/mau_import_du_toan_thiet_bi.xlsx`);
                    }}
                  />
                </Item>
                <Item location="before" locateInMenu="auto">
                  <DxButton
                    stylingMode="text"
                    icon="upload"
                    text="Import Excel"
                    type="default"
                    onClick={toggleImportForm}
                  />
                </Item>
              </>
            }
          >
            <Column
              dataField="deviceName"
              caption={t('fields.projectCostEstimationDeviceDetails.deviceName')}
              allowEditing={isPmDirector}
            />
            <Column
              dataField="note"
              caption={t('fields.projectCostEstimationDeviceDetails.note')}
              allowEditing={isPmDirector}
              cssClass="whitespace-pre-wrap break-words"
              // showEditorAlways
              // cellRender={CellRendererTextArea}
              // editCellRender={CellRendererTextArea}
            />
            <Column
              dataField="unitName"
              caption={t('fields.projectCostEstimationDeviceDetails.unitName')}
              allowEditing={isPmDirector}
            />
            <Column
              dataField="quantity"
              caption={t('fields.projectCostEstimationDeviceDetails.quantity')}
              allowEditing={isPmDirector}
              {...optionsQuantity}
              setCellValue={(newRow, value, currentRowData) => {
                const row = { ...currentRowData, quantity: Number(value) };
                const preTaxValue = (row.price || 0) * (row.quantity || 0);
                const vatTax = (preTaxValue * (row.vat || 0)) / 100;
                const postTaxValue = preTaxValue + vatTax;
                newRow.quantity = row.quantity;
                newRow.preTaxValue = preTaxValue;
                newRow.vatTax = vatTax;
                newRow.postTaxValue = postTaxValue;
              }}
            />
            <Column
              dataField="price"
              caption={t('fields.projectCostEstimationDeviceDetails.price')}
              allowEditing={isPmDirector}
              {...optionsMoney}
              setCellValue={(newRow, value, currentRowData) => {
                const row = { ...currentRowData, price: Number(value) };

                const preTaxValue = (row.price || 0) * (row.quantity || 0);
                const vatTax = (preTaxValue * (row.vat || 0)) / 100;
                const postTaxValue = preTaxValue + vatTax;

                newRow.price = row.price;
                newRow.preTaxValue = preTaxValue;
                newRow.vatTax = vatTax;
                newRow.postTaxValue = postTaxValue;
              }}
            />
            <Column
              dataField="preTaxValue"
              caption={t('fields.projectCostEstimationDeviceDetails.preTaxValue')}
              allowEditing={false}
              {...optionsMoney}
            />
            <Column
              dataField="vat"
              caption={t('fields.projectCostEstimationDeviceDetails.vat')}
              allowEditing={isPmDirector}
              {...optionsPercent}
              setCellValue={(newRow, value, currentRowData) => {
                const row = { ...currentRowData, vat: Number(value) };

                const preTaxValue = (row.price || 0) * (row.quantity || 0);
                const vatTax = (preTaxValue * (row.vat || 0)) / 100;
                const postTaxValue = preTaxValue + vatTax;

                newRow.vat = row.vat;
                newRow.preTaxValue = preTaxValue;
                newRow.vatTax = vatTax;
                newRow.postTaxValue = postTaxValue;
              }}
            />
            <Column
              dataField="vatTax"
              caption={t('fields.projectCostEstimationDeviceDetails.vatTax')}
              allowEditing={false}
              {...optionsMoney}
            />
            <Column
              dataField="postTaxValue"
              caption={t('fields.projectCostEstimationDeviceDetails.postTaxValue')}
              allowEditing={false}
              {...optionsMoney}
            />
          </DevexDataGridEditable>
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ProjectCostEstimationDevice>
          onApply={data => {
            const existingData = getValues('projectCostEstimationDeviceDetails');
            const newData = [
              ...data.map(item => ({ ...item, projectId: projectId })),
              ...(existingData || []),
            ];
            setValue('projectCostEstimationDeviceDetails', newData);
            updateSummary();
            toggleImportForm();
          }}
          importModel="project"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT_COST_ESTIMATION_DEVICE}
          professionColumns={columnsForImportConfig}
          onImported={() => {}}
          additionalFormValues={[{ key: 'refId', value: projectId?.toString() || '' }]}
          path="import-list-cost-estimation-device-detail"
        />
      </BasicDialog>
    </PageLayout>
  );
};
