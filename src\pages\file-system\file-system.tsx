import { PageLayout } from '@/components/page-layout';
import File<PERSON>ana<PERSON>, {
  type FileManagerTypes,
  Permissions,
  Details,
  Column,
  ItemView,
  Item,
  FileManagerRef,
  Toolbar,
  FileSelectionItem,
} from 'devextreme-react/file-manager';

import { ReactNode, useCallback, useContext, useMemo, useRef, useState } from 'react';
import { createCustomFileProvider, DataItem, isADirectory } from './file-system-actions';
import { CreateDirectoryForm } from './create-directory-form';
import {
  defaultValuesRecordAttachment,
  DocumentGroup,
  Project,
  RecordAttachment,
  recordAttachmentSchema,
} from '@/types';
import { useAuth, useBoolean, useDataTable } from '@/hooks';
import { PeriodFilter } from '@/components/period-filter-form';
import { createDeleteMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import { MUTATE, PROFESSIONS, QUERIES } from '@/constant';
import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { getRandomNumber } from '@/lib/number';
import { zodResolver } from '@hookform/resolvers/zod';
import { UploadFileForm } from './upload-file-form';
import { Form } from '@/components/ui/form';
import { useForm, UseFormReturn } from 'react-hook-form';
import FileSystemItem from 'devextreme/file_management/file_system_item';
import './file-system.css';
import { loadMessages } from 'devextreme/localization';
import viMessages from '@/locales/vi.json';
import { useQueryClient } from '@tanstack/react-query';
import { SelectedProjectContext } from '@/provider/selected-project-context';

const customMessages = {
  ...viMessages,
};
loadMessages({ vi: customMessages });

const defaultDocumentGroupValues: DocumentGroup = {
  id: 0,
  code: '',
  name: '',
  note: '',
  projectId: 0,
  isActive: true,
  storeId: null,
  branchId: null,
};
const rootDirectoryContextMenuItems = {
  items: [{ name: 'createItem', text: 'Thêm thư mục', icon: 'plus' }],
};
const directoryContextMenuItems = {
  items: [
    { name: 'createItem', text: 'Thêm thư mục', icon: 'plus' },
    { name: 'move', text: 'Di chuyển', icon: 'movetofolder' },
    { name: 'renameItem', text: 'Đổi tên', icon: 'edit' },
    { name: 'delete', text: 'Xóa', icon: 'trash' },
  ],
};
const emptyFileContextMenuItems = {
  items: [{ name: 'uploadItem', text: 'Thêm file', icon: 'upload' }],
};
const fileContextMenuItems = {
  items: [
    { name: 'uploadItem', text: 'Thêm file', icon: 'upload' },
    { name: 'editItem', text: 'Sửa file', icon: 'edit' },
    { name: 'delete', text: 'Xóa file', icon: 'trash' },
    { name: 'separator', visible: true },
    { name: 'download', text: 'Tải xuống', icon: 'download', location: 'after' },
  ],
};
type FileSystemPageProps = {
  projectId?: number;
  projectMethods?: UseFormReturn<Project, any, undefined>;
  onBackToList?: () => void;
  description?: ReactNode | ReactNode[];
};
export const FileSystemPage = ({
  projectId,
  projectMethods,
  onBackToList,
  description,
}: FileSystemPageProps) => {
  const [currentPath, setCurrentPath] = useState('Widescreen');
  const projectRef = useRef<Project | null>(null);

  const onCurrentDirectoryChanged = useCallback(
    (e: FileManagerTypes.CurrentDirectoryChangedEvent) => {
      setCurrentPath(e.component.option('currentPath') || '');
    },
    [setCurrentPath]
  );
  const fileManagerRef = useRef<FileManagerRef>(null);

  const [directory, setDirectory] = useState<DocumentGroup>(defaultDocumentGroupValues);
  const [isCreateDirectoryOpen, setIsEditDialogOpen] = useState(false);
  const toggleCreateDirectoryDialog = useCallback(() => {
    setIsEditDialogOpen(!isCreateDirectoryOpen);
  }, [isCreateDirectoryOpen, setIsEditDialogOpen]);
  const queryClient = useQueryClient();

  const {
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,
    isConfirmDeleteDialogOpen,
    selectedTarget,
  } = useDataTable<DocumentGroup, PeriodFilter>({
    getTargetAlias: row => row?.name || '',
    deleteFn: createDeleteMutateFn<DocumentGroup>('document-group'),
    deleteKey: [MUTATE.DELETE_DOCUMENT_GROUP],
    invalidateKey: [QUERIES.DOCUMENT_GROUP],
  });

  const { state: isRecordFormOpen, toggle: toggleRecordForm } = useBoolean(false);
  const methods = useForm<RecordAttachment>({
    resolver: zodResolver(recordAttachmentSchema),
    defaultValues: defaultValuesRecordAttachment,
  });

  const onAddButtonClick = useCallback(
    (dataItem: DataItem) => {
      const newRow: RecordAttachment = {
        ...defaultValuesRecordAttachment,
        projectId: dataItem?.documentGroup?.projectId || projectId,
        id: -getRandomNumber(),
        typeUpload: PROFESSIONS.PROJECT,
        groupDocId: dataItem?.documentGroup?.id || 0,
        // content: dataItem?.documentGroup?.name,
      };
      methods.reset(newRow);
      toggleRecordForm();
    },
    [methods, toggleRecordForm, projectId]
  );
  const onEditButtonClick = useCallback(
    async (dataItem: DataItem) => {
      projectRef.current = await createQueryByIdFn<Project>('project')(
        dataItem.documentGroup?.projectId || 0
      );
      const item = projectRef.current?.itemsRecordManagement.find(
        item => item.id === dataItem.directoryContent?.id
      );
      if (!item) return;

      methods.reset(item);
      toggleRecordForm();
    },
    [methods, toggleRecordForm]
  );

  const handleClose = () => {
    toggleRecordForm();
  };

  const handleOk = async (updatedItem: RecordAttachment) => {
    const isValid = await methods.trigger();
    if (!isValid) return;
    let project = projectRef.current;
    if (projectMethods) {
      project = projectMethods.getValues();
    }
    if (!projectRef.current || projectRef.current.id !== updatedItem.projectId) {
      project = await createQueryByIdFn<Project>('project')(updatedItem.projectId || 0);
    }
    if (project) {
      project.itemsRecordManagement = project.itemsRecordManagement.filter(
        item => item.id !== updatedItem.id
      );
      project.itemsRecordManagement.push(updatedItem);
      await createPutMutateFn<Project>('project')(project);
      await queryClient.invalidateQueries({ queryKey: [QUERIES.PROJECT] });
      if (projectMethods) {
        projectMethods.setValue('itemsRecordManagement', project.itemsRecordManagement);
      }
      toggleRecordForm();
      refresh();
    }
  };

  // const deleteFile = async (dataItem: DataItem) => {
  //   const project = await createQueryByIdFn<Project>('project')(
  //     dataItem.documentGroup?.projectId || 0
  //   );
  //   const item = project.itemsRecordManagement.find(
  //     item => item.id === dataItem.directoryContent?.id
  //   );
  //   if (item) {
  //     project.itemsRecordManagement = project.itemsRecordManagement.filter(
  //       item => item.id !== dataItem.directoryContent?.id
  //     );
  //     await createPutMutateFn<Project>('project')(project);
  //     refresh();
  //   }
  // };

  const onItemClick = useCallback(
    (dataItem: DataItem, itemName: string) => {
      if (itemName === 'createItem') {
        if (dataItem) {
          setDirectory({
            ...defaultDocumentGroupValues,
            parentId: dataItem.documentGroup?.id,
            projectId: dataItem.documentGroup?.projectId || projectId || 0,
            code: dataItem.documentGroup?.code || '',
          });
        } else {
          setDirectory({
            ...defaultDocumentGroupValues,
            projectId: projectId || 0,
          });
        }
        toggleCreateDirectoryDialog();
      }

      if (itemName === 'renameItem') {
        if (!dataItem.documentGroup) return;
        setDirectory({
          ...dataItem.documentGroup,
        });
        toggleCreateDirectoryDialog();
      }

      if (itemName === 'deleteItem') {
        if (!dataItem.documentGroup) return;
        //delete directory
        if (isADirectory(dataItem)) {
          selectTargetToDelete(dataItem.documentGroup);
        }
        // //delete file
        // else if (dataItem.documentGroup?.projectId) {
        //   void deleteFile(dataItem);
        // }
      }

      if (itemName == 'uploadItem') {
        if (!dataItem?.documentGroup?.projectId && !projectId) return;
        onAddButtonClick(dataItem);
      }
      if (itemName == 'editItem') {
        if (!dataItem?.documentGroup?.projectId) return;
        void onEditButtonClick(dataItem);
      }
    },
    [
      toggleCreateDirectoryDialog,
      selectTargetToDelete,
      onAddButtonClick,
      onEditButtonClick,
      projectId,
    ]
  );

  const { selectedProject } = useContext(SelectedProjectContext);
  const { projects } = useAuth();
  const refresh = () => void fileManagerRef.current?.instance()?.refresh();
  const fileSystemProvider = useMemo(
    () =>
      createCustomFileProvider({
        projectIds: projectId
          ? [projectId]
          : selectedProject?.id
            ? [selectedProject.id]
            : projects.map(i => i.id),
        methods: projectMethods,
      }),
    [projects, projectId, projectMethods, selectedProject?.id]
  );

  return (
    <PageLayout onCancel={onBackToList ? onBackToList : undefined}>
      {description}
      <FileManager
        ref={fileManagerRef}
        currentPath={currentPath}
        fileSystemProvider={fileSystemProvider}
        onCurrentDirectoryChanged={onCurrentDirectoryChanged}
        onContextMenuItemClick={e => {
          const itemName = e.itemData.name as string;
          let dataItem: DataItem | null = null;
          if (e.viewArea === 'navPane') {
            dataItem = e.fileSystemItem?.dataItem?.dataItem as DataItem;
          } else if (e.viewArea === 'itemView') {
            if (!e.fileSystemItem?.dataItem?.dataItem) {
              //không chọn gì => thêm file => lấy thư mục đang chọn bên trái
              dataItem = fileManagerRef.current?.instance().getCurrentDirectory().dataItem
                ?.dataItem as DataItem;
            } else {
              dataItem = e.fileSystemItem?.dataItem?.dataItem as DataItem;
            }
          }
          void onItemClick(dataItem!, itemName);
        }}
        onToolbarItemClick={e => {
          let selectedItem = fileManagerRef.current?.instance().getSelectedItems()[0];
          if (!selectedItem?.dataItem) {
            selectedItem = fileManagerRef.current
              ?.instance()
              .getCurrentDirectory() as FileSystemItem;
          }
          if (!selectedItem && e.itemData.name !== 'createItem') return;
          const dataItem = selectedItem?.dataItem?.dataItem as DataItem;
          const itemName = e.itemData.name as string;
          void onItemClick(dataItem, itemName);
        }}
        notifications={{ showPanel: false }}
        onContextMenuShowing={e => {
          let item = null;
          if (e.viewArea === 'navPane') {
            if (!e.fileSystemItem?.dataItem) {
              item = { ...rootDirectoryContextMenuItems };
            } else {
              item = { ...directoryContextMenuItems };
            }
          } else {
            if (e.fileSystemItem?.isDirectory) {
              item = { ...directoryContextMenuItems };
            } else if (!e.fileSystemItem?.dataItem) {
              item = { ...emptyFileContextMenuItems };
            } else {
              item = { ...fileContextMenuItems };
            }
          }
          // setContextMenuItems(item);
          fileManagerRef.current?.instance().option('contextMenu', item);
        }}
      >
        <Permissions
          create={false}
          copy={true}
          move={true}
          delete={true}
          rename={true}
          upload={true}
          download={true}
        ></Permissions>
        <ItemView>
          <Details>
            <Column dataField="thumbnail" />
            <Column dataField="name" caption="Tên file" />
            <Column dataField="dataItem.directoryContent.content" caption="Nội dung" />
          </Details>
        </ItemView>
        <Toolbar>
          <Item name="showNavPane" visible={true} locateInMenu="never" />
          <Item name="createItem" options={{ text: 'Thêm thư mục', icon: 'plus' }} />
          {/* <Item name="move" options={{ text: 'Di chuyển', icon: 'movetofolder' }} visible={true} />
          <Item name="renameItem" options={{ text: 'Đổi tên', icon: 'edit' }} />
          <Item name="separator" />
          <Item name="delete" options={{ text: 'Xóa', icon: 'trash' }} visible={true} /> */}
          <Item name="switchView" locateInMenu="never" location="after" />
          <Item name="separator" locateInMenu="never" location="after" />
          <Item name="refresh" locateInMenu="never" location="after" />

          {/* <Item name="uploadItem" options={{ text: 'Tải lên', icon: 'upload' }} />
          <Item name="refresh" locateInMenu="never" location="after" /> */}

          {/* <FileSelectionItem name="showNavPane" visible={true} locateInMenu="never" />
          <FileSelectionItem name="renameItem" options={{ text: 'Đổi tên', icon: 'edit' }} />
          <FileSelectionItem name="move" options={{ text: 'Di chuyển', icon: 'movetofolder' }} />
          <FileSelectionItem
            name="download"
            options={{ text: 'Tải xuống', icon: 'download' }}
            location="after"
            locateInMenu="never"
          />
          <FileSelectionItem name="separator" />
          <FileSelectionItem name="delete" options={{ text: 'Xóa', icon: 'trash' }} /> */}
          <FileSelectionItem name="showNavPane" visible={true} locateInMenu="never" />
          <FileSelectionItem name="createItem" options={{ text: 'Thêm thư mục', icon: 'plus' }} />
          <FileSelectionItem name="move" options={{ text: 'Di chuyển', icon: 'movetofolder' }} />
          <FileSelectionItem name="renameItem" options={{ text: 'Đổi tên', icon: 'edit' }} />
          <FileSelectionItem name="separator" />
          <FileSelectionItem name="delete" options={{ text: 'Xóa', icon: 'trash' }} />
          <FileSelectionItem name="switchView" locateInMenu="never" location="after" />
          <FileSelectionItem name="separator" locateInMenu="never" location="after" />
          <FileSelectionItem name="refresh" locateInMenu="never" location="after" />
          <FileSelectionItem
            name="clearSelection"
            options={{ text: 'Bỏ chọn', icon: 'remove' }}
            location="after"
          />
        </Toolbar>

        {/* <ContextMenu key={contextMenuType} items={contextMenuItems.items} /> */}
      </FileManager>

      {/* create/update directory */}
      <CreateDirectoryForm
        editId={directory.id}
        toggle={toggleCreateDirectoryDialog}
        defaultValues={directory}
        isEditDialogOpen={isCreateDirectoryOpen}
        onCreated={() => refresh()}
      />
      {/* delete directory */}
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          refresh();
        }}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        model="document-group"
        name={selectedTarget?.name || ''}
      />
      {/* upload file */}
      <Form {...methods}>
        <UploadFileForm
          isRecordFormOpen={isRecordFormOpen}
          handleClose={handleClose}
          handleOk={e => void handleOk(e)}
          folder={'project'}
        />
      </Form>
    </PageLayout>
  );
};
