import { PROFESSIONS } from '@/constant';
import { s3DowndloadFileHandler } from '@/lib/file';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdAsParamFn,
  createQueryByIdFn,
  createQueryPaginationFn,
  getDownloadOnetimeToken,
  s3DowndloadFile,
  uploadMultipartPresigned,
} from '@/services';
import {
  defaultValuesRecordAttachment,
  DocumentGroup,
  FieldColumn,
  Project,
  QueryPaginationParams,
  RecordAttachment,
} from '@/types';
import CustomFileSystemProvider from 'devextreme/file_management/custom_provider';
import FileSystemItem from 'devextreme/file_management/file_system_item';
import UploadInfo from 'devextreme/file_management/upload_info';
import { UseFormReturn } from 'react-hook-form';

let allItems: DocumentGroup[] = [];
const directoryType = 1;
const fileType = 2;

type DirectoryContent = {
  id: number;
  content?: string;
  type: number;
  host?: string;
  name?: string;
  folder?: string;
  fileName?: string;
  typeUpload?: number;
};
type DirectoryContents = {
  id: number;
  items?: DirectoryContent[];
};

export type DataItem = {
  type: 'DocumentGroup' | 'DirectoryContent';
  documentGroup: DocumentGroup | null;
  directoryContent: DirectoryContent | null;
};

function createFileSystemItem(
  item: DocumentGroup,
  parentDirectory?: FileSystemItem
): FileSystemItem {
  const path = parentDirectory?.path ? `${parentDirectory.path}/${item.name}` : `/${item.name}`;
  const pathKeys = [...(parentDirectory?.pathKeys || []), item.id.toString()];
  const fileSystemItem = new FileSystemItem(path, true, pathKeys);

  fileSystemItem.dataItem = {
    type: 'DocumentGroup',
    documentGroup: item,
    directoryContent: null,
  } as DataItem;
  fileSystemItem.key = `${item.id.toString()}-${item.name}`;
  fileSystemItem.hasSubDirectories = allItems.some(i => i.parentId === item.id);

  return fileSystemItem;
}

function createFileSystemItemFromDirectoryContent(
  item: DirectoryContent,
  parentDirectory?: FileSystemItem
): FileSystemItem {
  const isDirectory = item.type === directoryType;
  const isFile = item.type === fileType;

  const path = parentDirectory?.path ? `${parentDirectory.path}/${item.name}` : `/${item.name}`;
  const pathKeys = [...(parentDirectory?.pathKeys || []), item.id.toString()];
  const fileSystemItem = new FileSystemItem(path, isDirectory, pathKeys);

  fileSystemItem.dataItem = {
    type: 'DirectoryContent',
    directoryContent: item,
    documentGroup:
      allItems.find(i => i.id === item.id) || parentDirectory?.dataItem?.dataItem?.documentGroup,
  } as DataItem;

  fileSystemItem.key = `${item.id.toString()}-${item.name}`;

  if (isFile) {
    fileSystemItem.name = item.fileName || '';
    fileSystemItem.hasSubDirectories = false;
  } else {
    fileSystemItem.name = item.name || '';
    fileSystemItem.hasSubDirectories = allItems.some(i => i.parentId === item.id);
  }

  return fileSystemItem;
}
export function isADirectory(dataItem: DataItem): boolean {
  return (
    dataItem.type === 'DocumentGroup' ||
    (dataItem.type === 'DirectoryContent' && dataItem.directoryContent?.type === directoryType)
  );
}

export const getItems = async (parentDirectory: FileSystemItem): Promise<FileSystemItem[]> => {
  try {
    const query = createQueryPaginationFn<DocumentGroup>('document-group');
    const params: QueryPaginationParams = {
      pageIndex: 1,
      pageSize: -1,
      sortColumn: 'Id',
      sortOrder: 1,
      isPage: false,
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch:
            '(392,294,303,164,355,218,230,201,367,286,285,344,333,342,297,247,277,182,300,237,336,288,291,332,220,359,221,177,307,304,275,269,250,321,346,391,181,327,375,196,184,323,252,302,210,331,284,205,352,224,271,349,214,249,236,211,281,376,387,215,379,209,222,384,267,185,283,372,229,334,257,287,337,268,246,248,308,315,328,190,200,186,241,217,316,362,235,213,206,345,341,380,298,242,226,270,189,306,343,256,198,314,279,295,338,212,293,393,231,356,324,276,191,311,260,219,204,223,363,255,265,373,278,232,197,216,183,389,312,194,317,259,254,208,258,335,296,351,233,370,262,301,374,390,207,290,261,282,353,243,299,369,310,192,325,225,228,368,187,358,238,227,360,329,263,365,313,253,381,348,320,366,339,266,244,292,330,264,199,340,274,326,357,354,193,280,364,188,305,273,195,272,203,350,361,319,347,251,289,309,382,318,322,245,240)',
        },
      ],
    };

    const isRoot = !parentDirectory.dataItem;

    if (!isRoot) {
      params.filterColumn?.push({
        column: 'ParentId',
        expression: '=',
        keySearch: String(parentDirectory.dataItem.dataItem.id),
      });
    }

    const response = await query(params);
    const items = response.items;
    if (isRoot) {
      allItems = items;
    }

    const filteredItems = isRoot ? items.filter(item => !item.parentId) : items;

    const fileSystemItems = filteredItems.map(item => createFileSystemItem(item, parentDirectory));

    return fileSystemItems;
  } catch (error) {
    console.error('Error fetching document groups:', error);
    return [];
  }
};

export const getItemsAndFiles = async (
  parentDirectory: FileSystemItem
): Promise<FileSystemItem[]> => {
  try {
    const isRoot = !parentDirectory.dataItem;
    console.log('isRoot: ', isRoot);
    if (isRoot) {
      const query = createQueryPaginationFn<DocumentGroup>('document-group');
      const params: QueryPaginationParams = {
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [
          {
            column: 'ProjectId',
            expression: 'IN',
            keySearch:
              '(392,294,303,164,355,218,230,201,367,286,285,344,333,342,297,247,277,182,300,237,336,288,291,332,220,359,221,177,307,304,275,269,250,321,346,391,181,327,375,196,184,323,252,302,210,331,284,205,352,224,271,349,214,249,236,211,281,376,387,215,379,209,222,384,267,185,283,372,229,334,257,287,337,268,246,248,308,315,328,190,200,186,241,217,316,362,235,213,206,345,341,380,298,242,226,270,189,306,343,256,198,314,279,295,338,212,293,393,231,356,324,276,191,311,260,219,204,223,363,255,265,373,278,232,197,216,183,389,312,194,317,259,254,208,258,335,296,351,233,370,262,301,374,390,207,290,261,282,353,243,299,369,310,192,325,225,228,368,187,358,238,227,360,329,263,365,313,253,381,348,320,366,339,266,244,292,330,264,199,340,274,326,357,354,193,280,364,188,305,273,195,272,203,350,361,319,347,251,289,309,382,318,322,245,240)',
          },
        ],
      };
      const response = await query(params);
      const items = response.items;
      if (isRoot) {
        allItems = items;
      }

      const filteredItems = items.filter(
        item => !item.parentId || items.find(i => i.id === item.parentId) === null
      );
      console.log('filteredItems', filteredItems);

      const fileSystemItems = filteredItems.map(item =>
        createFileSystemItem(item, parentDirectory)
      );

      return fileSystemItems;
    }

    const parentItem = parentDirectory.dataItem.dataItem as DataItem;
    const queryFile = createQueryByIdAsParamFn<DirectoryContents>(
      'document-group/get-directory-file-by-id'
    );
    if (isADirectory(parentItem)) {
      const data = await queryFile(
        parentItem.documentGroup?.id || parentItem.directoryContent?.id || 0
      );
      const fileSystemItems =
        data?.items?.map(item => createFileSystemItemFromDirectoryContent(item, parentDirectory)) ||
        [];
      return fileSystemItems;
    }

    return [];
  } catch (error) {
    console.error('Error fetching document groups:', error);
    return [];
  }
};
export const getItemsAndFilesByProjectIds = async (
  parentDirectory: FileSystemItem,
  projectIds: number[]
): Promise<FileSystemItem[]> => {
  try {
    const isRoot = !parentDirectory.dataItem;
    const ids = projectIds.map(i => String(i)).join(',');
    let param: FieldColumn[] = [];
    if (ids) {
      param = [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${ids})`,
        },
      ];
    } else {
      param = [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: '(0)',
        },
      ];
    }
    if (isRoot) {
      const query = createQueryPaginationFn<DocumentGroup>('document-group');
      const params: QueryPaginationParams = {
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: param,
      };
      const response = await query(params);
      const items = response.items;
      if (isRoot) {
        allItems = items;
      }

      const filteredItems = items.filter(
        item => !item.parentId || !items.find(i => i.id === item.parentId)
      );

      const fileSystemItems = filteredItems.map(item =>
        createFileSystemItem(item, parentDirectory)
      );

      return fileSystemItems;
    }

    const parentItem = parentDirectory.dataItem.dataItem as DataItem;
    const queryFile = createQueryByIdAsParamFn<DirectoryContents>(
      'document-group/get-directory-file-by-id'
    );
    if (isADirectory(parentItem)) {
      const data = await queryFile(
        parentItem.documentGroup?.id || parentItem.directoryContent?.id || 0
      );
      const fileSystemItems =
        data?.items?.map(item => createFileSystemItemFromDirectoryContent(item, parentDirectory)) ||
        [];
      return fileSystemItems;
    }

    return [];
  } catch (error) {
    console.error('Error fetching document groups:', error);
    return [];
  }
};

export const createDirectory = async (
  parentDirectory: FileSystemItem,
  name: string
): Promise<any> => {
  try {
    const dataItem = parentDirectory.dataItem.dataItem as DataItem;
    if (isADirectory(dataItem)) {
      const newItem: DocumentGroup = {
        id: 0,
        projectId: dataItem.documentGroup?.projectId || 0,
        parentId: dataItem.documentGroup?.id || dataItem.directoryContent?.id,
        code: name,
        name,
        isActive: true,
        storeId: null,
        branchId: null,
      };

      return await createPostMutateFn<DocumentGroup>('document-group')(newItem);
    }
  } catch (error) {
    console.error('Error creating directory:', error);
    return null;
  }
};

export const renameItem = async (item: FileSystemItem, newName: string): Promise<any> => {
  try {
    const dataItem = item.dataItem.dataItem as DataItem;
    if (!dataItem) return null;
    if (isADirectory(dataItem)) {
      const newItem: DocumentGroup = {
        ...dataItem.documentGroup!,
        name: newName,
      };

      return await createPutMutateFn<DocumentGroup>('document-group')(newItem);
    }
  } catch (error) {
    console.error('Error renaming item:', error);
    return null;
  }
};

export const deleteItem = async (
  item: FileSystemItem,
  methods?: UseFormReturn<Project>
): Promise<any> => {
  try {
    const dataItem = item.dataItem.dataItem as DataItem;
    console.log('dataItem: ', dataItem);
    if (!dataItem) return null;
    if (isADirectory(dataItem)) {
      const id = dataItem.documentGroup?.id || dataItem.directoryContent?.id;
      if (!id) return null;
      if (allItems.some(i => i.parentId === id)) return null;
      return await createDeleteMutateFn<DocumentGroup>('document-group')(id);
    } else {
      let project = methods?.getValues();
      if (!project?.id) {
        project = await createQueryByIdFn<Project>('project')(
          dataItem.documentGroup?.projectId || 0
        );
      }
      const item = project.itemsRecordManagement.find(
        item => item.id === dataItem.directoryContent?.id
      );
      if (item) {
        project.itemsRecordManagement = project.itemsRecordManagement.filter(
          item => item.id !== dataItem.directoryContent?.id
        );
        await createPutMutateFn<Project>('project')(project);
        methods?.setValue('itemsRecordManagement', project.itemsRecordManagement);
      }
    }
  } catch (error) {
    console.error('Error renaming item:', error);
    return null;
  }
};

export const moveItem = async (
  item: FileSystemItem,
  destinationDirectory: FileSystemItem
): Promise<any> => {
  try {
    const dataItem = item.dataItem.dataItem as DataItem;
    const parentDataItem = destinationDirectory.dataItem.dataItem as DataItem;
    if (!dataItem || !parentDataItem) return null;
    if (!isADirectory(dataItem) || !isADirectory(parentDataItem)) return null;
    if (dataItem.documentGroup?.projectId !== parentDataItem.documentGroup?.projectId) return null;
    console.log(dataItem.documentGroup?.projectId, parentDataItem.documentGroup?.projectId);
    const newItem: DocumentGroup = {
      ...dataItem.documentGroup!,
      parentId: parentDataItem.documentGroup?.id,
    };

    return await createPutMutateFn<DocumentGroup>('document-group')(newItem);
  } catch (error) {
    console.error('Error renaming item:', error);
    return null;
  }
};

export const copyItem = async (
  item: FileSystemItem,
  destinationDirectory: FileSystemItem
): Promise<any> => {
  try {
    const dataItem = item.dataItem.dataItem as DataItem;
    const parentDataItem = destinationDirectory.dataItem.dataItem as DataItem;
    if (!dataItem || !parentDataItem) return null;
    if (!isADirectory(dataItem) || !isADirectory(parentDataItem)) return null;

    const parentId = parentDataItem.documentGroup?.id || parentDataItem.directoryContent?.id;
    if (!parentId) return null;

    const newItem: DocumentGroup = {
      ...dataItem.documentGroup!,
      code: `${dataItem.documentGroup!.code}-copy-${new Date().getTime()}`,
      parentId: parentId,
    };

    return await createPostMutateFn<DocumentGroup>('document-group')(newItem);
  } catch (error) {
    console.error('Error renaming item:', error);
    return null;
  }
};

const handleDownload = (item: FileSystemItem) => {
  const original = item.dataItem.dataItem as DataItem;
  if (
    original.type === 'DirectoryContent' &&
    original.directoryContent &&
    original.directoryContent.type === fileType
  ) {
    const rowData = {
      ...original.directoryContent,
      host: original.directoryContent.host ?? '',
      folder: original.directoryContent.folder ?? '',
      fileName: original.directoryContent.fileName ?? '',
    };
    console.log('rowData: ', rowData);
    s3DowndloadFileHandler(getDownloadOnetimeToken, s3DowndloadFile, rowData);
  }
};
export const downloadItems = (items: Array<FileSystemItem>) => {
  items.forEach(item => {
    handleDownload(item);
  });
};
export const uploadFileChunk = async (
  file: File,
  _: UploadInfo,
  destinationDirectory: FileSystemItem
): Promise<any> => {
  if (file) {
    const dataItem = destinationDirectory.dataItem.dataItem as DataItem;
    console.log('dataItem: ', dataItem);
    if (
      isADirectory(dataItem) &&
      dataItem.documentGroup?.projectId &&
      dataItem.directoryContent?.typeUpload === PROFESSIONS.PROJECT
    ) {
      const formData = new FormData();
      formData.append('File', file);
      // formData.append('Type', 'xlsx');
      if (dataItem.directoryContent?.folder) {
        formData.append('Folder', dataItem.directoryContent?.folder);
      }

      const objFile = await uploadMultipartPresigned(
        file,
        dataItem.directoryContent?.folder ?? 'upload'
      );
      if (objFile !== undefined) {
        const fileItem = {
          ...defaultValuesRecordAttachment,
          groupDocId: dataItem.documentGroup?.id,
          itemFile: [
            {
              fileName: objFile.fileName,
              folder: objFile.src,
              host: objFile.host,
              id: 0,
              name: objFile.name,
              note: '',
              recordManagementId: -1,
              refId: dataItem.documentGroup?.projectId,
              typeFileId: null,
              storeId: null,
              typeUpload: dataItem.directoryContent?.typeUpload,
            },
          ],
          projectId: dataItem.documentGroup?.projectId,
          refId: dataItem.documentGroup?.projectId,
          typeUploadId: dataItem.directoryContent?.typeUpload,
        } as RecordAttachment;
        const project = await createQueryByIdFn<Project>('project')(
          dataItem.documentGroup?.projectId || 0
        );
        if (project) {
          project.itemsRecordManagement.push(fileItem);
          return await createPutMutateFn<Project>('project')(project);
        }
      }
    }
  }
};

export const customFileProvider = new CustomFileSystemProvider({
  getItems: getItemsAndFiles,
  createDirectory,
  renameItem,
  moveItem,
  copyItem,
  downloadItems,
  uploadFileChunk,
});
type CustomFileProviderOptions = {
  projectIds: number[];
  methods?: UseFormReturn<Project, any, undefined>;
};

export const createCustomFileProvider = (options: CustomFileProviderOptions) => {
  return new CustomFileSystemProvider({
    getItems: async (parentDirectory: FileSystemItem) =>
      getItemsAndFilesByProjectIds(parentDirectory, options.projectIds),
    createDirectory,
    renameItem,
    moveItem,
    copyItem,
    downloadItems,
    uploadFileChunk,
    deleteItem: (item: FileSystemItem) => deleteItem(item, options.methods),
  });
};
