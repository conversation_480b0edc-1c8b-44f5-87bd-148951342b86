/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGridEditable } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PERMISSIONS, PROFESSIONS, QUERIES, TABLES } from '@/constant';
import { useAuth, useDataTable, useEntity, usePermission, useStatus } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { AIWarningCostOverrun } from '@/types/ai-warning-cost-overrun';
import axiosInstance, { request } from '@/axios-instance';
import queryClient from '@/query-client';

const t = translationWithNamespace('aiWarningCostOverrun');
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const AIWarningCostOverrunDataTable = () => {
  const { user } = useAuth();
  const { t } = useTranslation('aiWarningCostOverrun');

  const role = usePermission(PERMISSIONS.AI_WARNING);

  const { list: projects } = useEntity({ queryKey: [QUERIES.PROJECT], model: 'project' });
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const status = useStatus(PROFESSIONS.AI_WARNING);
  const listWarningType = [{ id: 3, name: 'Cảnh báo nghiêm trọng' }];
  const getTargetAlias = (target: AIWarningCostOverrun | undefined) => {
    if (!target) {
      return '';
    }
    return target.projectId?.toString() || '';
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    // selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<AIWarningCostOverrun, PeriodFilter>({
    queryRangeName: 'createdTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<AIWarningCostOverrun>('warning'),
    deleteKey: [MUTATE.AI_WARNING_COST_OVERRUN],
    invalidateKey: [QUERIES.AI_WARNING_COST_OVERRUN],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.AI_WARNING_COST_OVERRUN],
    queryFn: () => {
      return createQueryPaginationFn<AIWarningCostOverrun>('warning')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'CreatedTime',
        sortOrder: 1,
        isPage: false,
        ...queryListParams,
        filterColumn: [
          ...(queryListParams.filterColumn || []),
          {
            column: 'RecipientId',
            expression: '=',
            keySearch: `${user?.userId}`,
          },
          {
            column: 'ProfessionType',
            expression: '=',
            keySearch: `${PROFESSIONS.PROJECT_DISBURSEMENT}`,
          },
        ],
      });
    },
  });

  const { items } = data || { items: [] };

  const { isUpdate, isDelete } = role || {};

  const { mutate: updateStatusWarning } = useMutation({
    mutationKey: [MUTATE.UPDATE_TASK_STATUS_AI_WARNING_COST_OVERRUN],
    mutationFn: (data: AIWarningCostOverrun) => {
      return request<string>(axiosInstance.put(`/warning/${data.id}`, data));
    },
    onSuccess: async () =>
      queryClient.invalidateQueries({ queryKey: [QUERIES.AI_WARNING_COST_OVERRUN] }),
  });

  // Xử lý khi cập nhật dòng dữ liệu
  const handleRowUpdated = (e: any) => {
    // Kiểm tra xem có thay đổi statusId không
    if (e.data && e.key) {
      const updatedData: AIWarningCostOverrun = {
        ...e.data,
      };
      updateStatusWarning(updatedData);
    }
  };

  return (
    <div>
      <p className="mb-2 text-xl font-bold">{t('page.header')}</p>

      <PageLayout>
        <PeriodFilterForm
          defaultSearchValues={{
            range: [queryListParams.fromDate!, queryListParams.toDate!],
          }}
          onSearch={values => {
            const { range } = values;

            if (range) {
              const [from, to] = values.range;
              queryListMethods.addOrReplaceFilterDateColumn('createdTime', from!, to!);
            }

            callbackWithTimeout(refetch);
          }}
        />
        <div className="w-full">
          <DevexDataGridEditable
            id={TABLES.AI_WARNING_COST_OVERRUN}
            dataSource={items}
            onRefresh={() => {
              callbackWithTimeout(refetch);
            }}
            onExporting={onExporting}
            onRowUpdated={handleRowUpdated}
            editing={{
              mode: 'cell',
              allowDeleting: false,
              allowUpdating: true,
              useIcons: true,
              confirmDelete: false,
              newRowPosition: 'last',
            }}
          >
            <Export enabled={true} />
            <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
            {/* <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column> */}
            {/* Dự án */}
            <Column
              dataField="projectId"
              caption={t('fields.projectId')}
              alignment="left"
              allowEditing={false}
            >
              <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
            </Column>

            {/* Hạng mục công việc */}
            <Column dataField="name" caption={t('fields.name')} dataType="date" alignment="left" />
            {/* Loại cảnh báo */}
            <Column
              dataField="warningType"
              caption={t('fields.warningType')}
              alignment="left"
              allowEditing={false}
            >
              <Lookup
                dataSource={listWarningType}
                displayExpr={displayExpr(['name'])}
                valueExpr={'id'}
              />
            </Column>
            {/* Nội dung cảnh báo */}
            <Column dataField="content" caption={t('fields.content')} alignment="left" />
            {/* Giải pháp */}
            <Column dataField="solution" caption={t('fields.solution')}></Column>
            {/* Trạng thái */}
            <Column dataField="statusId" caption={t('fields.statusId')} width={150}>
              <Lookup dataSource={status} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
            </Column>
            {/* Người nhận */}
            <Column dataField="recipientId" caption={t('fields.recipientId')} allowEditing={false}>
              <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
            </Column>
          </DevexDataGridEditable>
        </div>

        <DeleteConfirmDialog
          isDeleting={isDeleting}
          open={isConfirmDeleteDialogOpen}
          toggle={toggleConfirmDeleteDialog}
          onConfirm={() => {
            deleteTarget();
          }}
          name={getTargetAlias(selectedTarget)}
          model="aiWarningOfConstructionProgress"
        />
      </PageLayout>
    </div>
  );
};
