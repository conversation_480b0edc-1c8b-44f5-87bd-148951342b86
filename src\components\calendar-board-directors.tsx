import { useState, memo, useCallback } from 'react';
import { startOfWeek, addDays, format, addWeeks, subWeeks, isSameDay } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CalendarBoardDirectorsProps {
  onSelectedDateChange?: (date: Date) => void;
}

const CalendarBoardDirectors = memo(({ onSelectedDateChange }: CalendarBoardDirectorsProps) => {
  // Lấy ngày hiện tại
  const today = new Date();
  const currentMonth = today.getMonth() + 1; // getMonth() trả về 0-11, nên cần +1
  const currentYear = today.getFullYear();

  const [currentDate, setCurrentDate] = useState(today);
  const [month, setMonth] = useState(currentMonth);
  const [year, setYear] = useState(currentYear);
  const [selectedDate, setSelectedDate] = useState(today);

  const startOfCurrentWeek = startOfWeek(currentDate, { weekStartsOn: 1 }); // Bắt đầu từ thứ 2

  // Hàm xử lý khi nhấn nút trước
  const handlePrevious = useCallback(() => {
    const newDate = subWeeks(currentDate, 1);
    setCurrentDate(newDate);
    setMonth(newDate.getMonth() + 1);
    setYear(newDate.getFullYear());
  }, [currentDate]);

  // Hàm xử lý khi nhấn nút tiếp theo
  const handleNext = useCallback(() => {
    const newDate = addWeeks(currentDate, 1);
    setCurrentDate(newDate);
    setMonth(newDate.getMonth() + 1);
    setYear(newDate.getFullYear());
  }, [currentDate]);

  // Hàm xử lý khi chọn ngày
  const handleDateSelect = useCallback(
    (date: Date) => {
      setSelectedDate(date);
      setCurrentDate(date);
      setMonth(date.getMonth() + 1);
      setYear(date.getFullYear());
      onSelectedDateChange?.(date);
    },
    [onSelectedDateChange]
  );

  // Mapping tên ngày theo format Việt Nam
  const dayNames = ['Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7', 'CN'] as const;

  type DayName = (typeof dayNames)[number];

  const weekDays: {
    date: Date;
    dayName: DayName;
    dayNumber: string;
  }[] = Array.from({ length: 7 }, (_, index) => {
    const day = addDays(startOfCurrentWeek, index);
    return {
      date: day,
      dayName: dayNames[index],
      dayNumber: format(day, 'd'),
    };
  });

  return (
    <div className="flex flex-col justify-center pb-2">
      <div className="flex items-center justify-center pt-2">
        <button onClick={handlePrevious}>
          <ChevronLeft />
        </button>
        <div className="flex flex-row items-center justify-center px-2" onClick={() => {}}>
          <p className="font-semibold">
            Tháng {month} Năm {year}
          </p>
        </div>
        <button onClick={handleNext}>
          <ChevronRight />
        </button>
      </div>

      <div className="mt-2 flex flex-row justify-center">
        {weekDays.map((dayItem, index) => (
          <div
            className="flex cursor-pointer flex-col items-center justify-center px-2 text-center"
            onClick={() => handleDateSelect(dayItem.date)}
          >
            <p
              key={index}
              className={`${dayItem.dayName === 'Th 7' || dayItem.dayName === 'CN' ? 'text-red-600' : ''} font-semibold `}
            >
              {dayItem.dayName}
            </p>
            <div
              className={`${isSameDay(selectedDate, dayItem.date) ? 'bg-blue-600' : isSameDay(today, dayItem.date) ? 'bg-blue-200' : ''}  h-6 w-6 rounded-full p-1 font-semibold`}
            >
              <p
                className={`${isSameDay(selectedDate, dayItem.date) ? 'text-white' : ''} ${dayItem.dayName === 'Th 7' || dayItem.dayName === 'CN' ? 'text-red-600' : ''} font-semibold `}
              >
                {dayItem.dayNumber}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

CalendarBoardDirectors.displayName = 'CalendarBoardDirectors';

export default CalendarBoardDirectors;
