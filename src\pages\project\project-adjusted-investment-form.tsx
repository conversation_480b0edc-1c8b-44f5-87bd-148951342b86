import { PageLayout } from '@/components/page-layout';
import { FormField, FormLabel } from '@/components/ui/form';
import { enterLabel, QUERIES, selectLabel, TABLES } from '@/constant';
import { useEntity } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import { displayExpr } from '@/lib/utils';
import { Project, ProjectDetailSummaries, ProjectTabChildrenProps } from '@/types';
import { Button, DataGrid, DateBox, TextBox } from 'devextreme-react';
import { Column, Item, Lookup, Summary, TotalItem } from 'devextreme-react/data-grid';
import { SyntheticEvent, useEffect } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { customizeNumberCell, DevexDataGrid, onCellPrepared } from '@/components/devex-data-grid';
import { optionsMoney, optionsPercent } from './options';
import { SourceInfo } from './components';

export const ProjectAdjustedInvestmentForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { control, setValue } = useFormContext<Project>();
  const [editableData, editableSummaries, projectId] = useWatch({
    control,
    name: ['projectAdjustedInvestmentDetails', 'projectAdjustedInvestmentSummaries', 'id'],
  });

  const { list: costItems } = useEntity({
    queryKey: [QUERIES.COST_ITEM],
    model: 'cost-item',
  });
  const { list: costItemTypes } = useEntity<ProjectDetailSummaries>({
    queryKey: [QUERIES.COST_ITEM_TYPE],
    model: 'cost-item-type',
  });

  useEffect(() => {
    if (editableSummaries.length === 0 || editableSummaries.length !== costItemTypes.length) {
      const summary: ProjectDetailSummaries[] = costItemTypes
        .map(costItemType => {
          return {
            id: -getRandomNumber(),
            projectId,
            costItemValue:
              editableSummaries.find(item => item.costItemTypeId === costItemType.id)
                ?.costItemValue || 0,
            costItemValueBefore:
              editableSummaries.find(item => item.costItemTypeId === costItemType.id)
                ?.costItemValueBefore || 0,
            costItemTypeId: costItemType.id,
            sort: costItemType.sort,
          };
        })
        .sort((a, b) => (a?.sort ?? 0) - (b?.sort ?? 0));
      setValue('projectAdjustedInvestmentSummaries', summary);
    }
  }, [costItemTypes, editableSummaries, projectId, setValue]);

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedInvestmentDocumentCode"
              htmlFor="adjustedInvestmentDocumentCode"
            >
              {t('fields.documentCode')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedInvestmentDocumentCode"
              label={t('fields.documentCode')}
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.documentCode')}`} readOnly />
            </FormField>
          </div>
          <div className="flex w-full items-center sm:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedInvestmentSigningDate"
              htmlFor="adjustedInvestmentSigningDate"
            >
              {t('fields.signingDate')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedInvestmentSigningDate"
              type="date"
              label={t('fields.signingDate')}
            >
              <DateBox
                placeholder={`${selectLabel} ${t('fields.signingDate')}`}
                pickerType="calendar"
                focusStateEnabled={false}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center sm:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedInvestmentSignerId"
              htmlFor="adjustedInvestmentSignerId"
            >
              {t('fields.signerId')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedInvestmentSignerId"
              label={t('fields.signerId')}
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.signerId')}`} />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 xl:col-span-16">
          <DataGrid
            dataSource={editableSummaries}
            columnAutoWidth
            allowColumnResizing
            allowColumnReordering
            showBorders
            showColumnLines
            showRowLines
            width={'auto'}
            id={TABLES.ADJUSTED_INVESTMENT_DETAIL_SUMMARY}
            className="column-header-wrap"
            onCellPrepared={e => {
              onCellPrepared(e);
            }}
          >
            <Column
              dataType="number"
              caption="STT"
              width={50}
              cellRender={({ row }) => Number(row.rowIndex) + 1}
            />
            <Column
              dataField="costItemTypeId"
              caption={t('fields.projectDetailSummaries.costItemTypeId')}
            >
              <Lookup
                dataSource={costItemTypes}
                valueExpr={'id'}
                displayExpr={displayExpr(['name'])}
              />
            </Column>
            <Column
              dataField="costItemValue"
              caption={t('fields.projectDetailSummaries.costItemValue')}
              dataType="number"
              alignment="right"
              customizeText={customizeNumberCell(0)}
            />
            <Column
              dataField="costItemValueBefore"
              caption={t('fields.projectDetailSummaries.costItemValueBefore')}
              dataType="number"
              alignment="right"
              customizeText={customizeNumberCell(0)}
            />
            <Summary>
              <TotalItem
                column="costItemTypeId"
                summaryType="count"
                customizeText={() => t('fields.projectDetailSummaries.totalAmount')}
              />
              <TotalItem
                column="costItemValue"
                summaryType="sum"
                customizeText={customizeNumberCell(0)}
                displayFormat="{0}"
              />
              <TotalItem
                column="costItemValueBefore"
                summaryType="sum"
                customizeText={customizeNumberCell(0)}
                displayFormat="{0}"
              />
            </Summary>
          </DataGrid>
        </div>
        <div className="col-span-1 xl:col-span-24">
          <DevexDataGrid
            id={TABLES.ADJUSTED_INVESTMENT_DETAIL}
            dataSource={editableData}
            editing={{
              mode: 'cell',
              allowUpdating: false,
              allowDeleting: false,
              allowAdding: false,
              useIcons: true,
            }}
            grouping={{ contextMenuEnabled: false }}
            groupPanel={{ visible: false }}
            repaintChangesOnly
            customToolbar={
              <>
                <Item location="before">
                  <SourceInfo>{t('sourceInfoNotes.adjustedInvestment')}</SourceInfo>
                </Item>
              </>
            }
          >
            <Column dataField="costItemId" caption={t('fields.projectDetail.costItemId')}>
              <Lookup dataSource={costItems} valueExpr={'id'} displayExpr={displayExpr(['name'])} />
            </Column>
            <Column dataField="symbol" caption={t('fields.projectDetail.symbol')} />
            <Column
              dataField="percentageRate"
              caption={t('fields.projectDetail.percentageRate')}
              {...optionsPercent}
            />
            <Column
              dataField="calculationMethod"
              caption={t('fields.projectDetail.calculationMethod')}
            />
            <Column
              dataField="preTaxValue"
              caption={t('fields.projectDetail.preTaxValue')}
              {...optionsMoney}
            />
            <Column dataField="vat" caption={t('fields.projectDetail.vat')} {...optionsPercent} />
            <Column
              dataField="vatTax"
              caption={t('fields.projectDetail.vatTax')}
              {...optionsMoney}
            />
            <Column
              dataField="postTaxValue"
              caption={t('fields.projectDetail.postTaxValue')}
              {...optionsMoney}
            />
            <Column
              dataField="postTaxValueBefore"
              caption={t('fields.projectDetail.postTaxValueBefore')}
              {...optionsMoney}
            />
          </DevexDataGrid>
        </div>
      </div>
    </PageLayout>
  );
};
