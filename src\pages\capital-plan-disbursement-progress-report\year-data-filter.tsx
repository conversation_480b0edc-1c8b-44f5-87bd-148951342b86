import { Form, FormField, FormLabel } from '@/components/ui/form';
import { viewLabel } from '@/constant';
import { callbackWithTimeout } from '@/lib/utils';
import { Button, DateBox } from 'devextreme-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

// Định nghĩa schema cho dữ liệu form sử dụng zod
const year = z.object({
  time: z.date(),
});

// Định nghĩa kiểu dữ liệu cho Props của component
type Props = {
  onSearch: (values: z.infer<typeof year>) => void; // Hàm callback khi submit form
  defaultValues: z.infer<typeof year>; // Giá trị mặc định của form
};

export const YearDataFilter = ({ onSearch, defaultValues }: Props) => {
  const { t } = useTranslation('capitalPlanDisbursementProgressReport'); // Hook dịch thuật i18n để hỗ trợ đa ngôn ngữ
  const form = useForm<Props['defaultValues']>({ defaultValues: defaultValues }); // Khởi tạo form với giá trị mặc định
  return (
    <Form {...form}>
      <form
        onSubmit={e => {
          e.preventDefault(); // Ngăn chặn hành vi submit mặc định của form
          const submit = async () => {
            const canSubmit = await form.trigger(); // Kiểm tra tính hợp lệ của form
            if (canSubmit) {
              onSearch(form.getValues()); // Nếu hợp lệ, lấy dữ liệu từ form và gọi `onSearch`
            }
          };
          callbackWithTimeout(submit); // Gọi hàm submit với timeout để tránh bị chặn
        }}
        aria-description="Bộ lọc báo cáo"
        className="flex w-full max-w-screen-md flex-col gap-x-4 gap-y-4 md:flex-row md:items-center"
      >
        <div className="flex  items-center">
          <FormLabel name="time" htmlFor="time" className="hidden md:block">
            {t('page.year')}
          </FormLabel>
          <FormField name="time" className="w-full md:w-[110px]" type="date" label={t('page.year')}>
            <DateBox
              type="date"
              pickerType="calendar"
              calendarOptions={{
                maxZoomLevel: 'decade',
                minZoomLevel: 'decade',
              }}
              displayFormat={'year'}
              focusStateEnabled={false}
            />
          </FormField>
        </div>

        {/* Nút tìm kiếm */}
        <Button
          text={viewLabel} // Nhãn của nút
          className="w-full md:w-fit"
          stylingMode="contained" // Giao diện của nút
          type="default"
          icon="search" // Icon tìm kiếm
          useSubmitBehavior // Cho phép nút tự động submit form
        />
      </form>
    </Form>
  );
};
