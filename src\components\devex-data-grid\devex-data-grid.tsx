import { onCellPrepared, saveDataGridStateToIndexedDB } from '@/components/devex-data-grid';
import { DEV_EXTREME_STORAGE_KEY } from '@/constant';
import { useScreenSize } from '@/hooks';
import { useGridState } from '@/hooks/useGridState';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { cn } from '@/lib/utils';
import { Button } from 'devextreme-react';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  ColumnFixing,
  DataGridRef,
  Editing,
  FilterRow,
  Grouping,
  GroupPanel,
  HeaderFilter,
  Item,
  LoadPanel,
  Pager,
  Paging,
  Scrolling,
  Search,
  SearchPanel,
  StateStoring,
  Toolbar,
} from 'devextreme-react/data-grid';
import dxDataGrid, {
  dxDataGridColumn,
  dxDataGridRowObject,
  RowDblClickEvent,
} from 'devextreme/ui/data_grid';
import React, { useCallback, useEffect, useRef, useState } from 'react';

type ColumnBand = { name: string; columns?: ColumnBand[] };

type Props = {
  onAddNewClick?: () => void;
  onEditDoubleClick?: (e: RowDblClickEvent) => void;
  onRefresh?: () => void;
  customToolbar?: React.ReactNode | React.ReactNode[];
  hideSerialNumber?: boolean;
  columnsBands?: ColumnBand[];
  setRef?: (current: DataGridRef | null) => void;
};

const t = translationWithNamespace('common');

export function DevexDataGrid({
  children,
  // ref,
  onRefresh,
  onAddNewClick,
  onEditDoubleClick,
  customToolbar,
  hideSerialNumber: hideSerialNumber,
  setRef,
  selectedRowKeys,
  ...props
}: React.PropsWithChildren & React.ComponentProps<typeof DataGrid> & Props) {
  const [expanded, setExpanded] = useState(true);
  const storageKey = `${DEV_EXTREME_STORAGE_KEY}_${props.id}`;
  const treeListRef = useRef<DataGridRef>(null);
  useEffect(() => {
    if (setRef) {
      setRef(treeListRef.current);
    }
  }, [setRef]);

  const { height } = useScreenSize();

  const { loadState, isLoadingState } = useGridState(storageKey, {
    selectedRowKeys,
  });
  const customSave = useCallback(
    (state: any) => {
      if (state) {
        delete state.selectedRowKeys;
      }
      void saveDataGridStateToIndexedDB(storageKey, state, treeListRef?.current?.instance());
    },
    [storageKey]
  );

  const columnWidthMapRef = useRef<Map<string, number>>(new Map());
  const isAutoFitting = useRef(false);
  useEffect(() => {
    const instance = treeListRef.current?.instance();
    if (!instance) return;
    instance.beginUpdate();
    instance.option('columnAutoWidth', true);
    instance.option('wordWrapEnabled', true);
    instance.endUpdate();
    instance.updateDimensions();
  }, [treeListRef]);

  const handleClearFilterClick = () => {
    treeListRef.current?.instance().clearFilter();
  };

  return (
    <DataGrid
      keyExpr={'id'}
      ref={treeListRef}
      id="dataGrid"
      // columnAutoWidth
      allowColumnResizing
      columnResizingMode="widget"
      allowColumnReordering
      showBorders
      showColumnLines
      showRowLines
      hoverStateEnabled
      // rowAlternationEnabled
      // wordWrapEnabled={wordWrapEnabled}
      columnFixing={{
        enabled: true,
        texts: {
          fix: 'Cố định',
          leftPosition: 'Cố định bên trái',
          rightPosition: 'Cố định bên phải',
          unfix: 'Bỏ cố định',
        },
      }}
      className={cn(
        'column-header-wrap',
        'max-h-[calc(100vh-9.8rem-32.8px)]',
        height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
      )}
      onRowDblClick={onEditDoubleClick}
      {...props}
      onCellPrepared={e => {
        onCellPrepared(e);
        props.onCellPrepared?.(e);
      }}
      onContextMenuPreparing={e => {
        props.onContextMenuPreparing?.(e);
        if (e.target !== 'header') {
          return;
        }
        if (!e.items) e.items = [];

        // Add a custom menu item
        e.items.push({
          text: t('action.autoFit'),
          onItemClick: () => {
            autofitDataGrid();
          },
        });
      }}
      onOptionChanged={e => {
        if (e.name === 'columns' && e.fullName.includes('.width')) {
          if (!isAutoFitting.current) {
            const instance = treeListRef.current?.instance();
            if (instance) {
              instance.option('wordWrapEnabled', true);
            }
          }
        }
      }}
    >
      <LoadPanel enabled={isLoadingState} />
      <ColumnChooser enabled mode="select" height="45rem">
        <ColumnChooserSearch enabled />
        <ColumnChooserSelection allowSelectAll selectByClick recursive />
      </ColumnChooser>
      <FilterRow visible showOperationChooser />
      <Scrolling mode="standard" rowRenderingMode="standard" />
      <Paging enabled defaultPageSize={10} />
      <Pager
        visible
        showInfo
        showNavigationButtons
        showPageSizeSelector
        displayMode="adaptive"
        allowedPageSizes={[5, 10, 50, 100, 'all']}
      />
      <Grouping autoExpandAll={expanded} contextMenuEnabled={true} expandMode="rowClick" />
      <StateStoring
        enabled
        type="custom"
        storageKey={storageKey}
        // customSave={state => {
        //   console.log('state:', state);
        // }}
        customLoad={loadState}
        customSave={customSave}
      />
      <HeaderFilter visible>
        <Search enabled mode="contains" />
      </HeaderFilter>
      <SearchPanel visible />
      <GroupPanel visible />
      {/* <FilterPanel visible /> */}
      <Editing confirmDelete allowUpdating allowDeleting allowAdding useIcons />
      <Toolbar>
        <Item name="groupPanel" />
        <Item location="after">
          <Button
            icon={expanded ? 'chevrondown' : 'chevronleft'}
            onClick={() => setExpanded(prevExpanded => !prevExpanded)}
            hint={expanded ? 'Đóng nhóm' : 'Mở nhóm'}
          />
        </Item>
        {customToolbar}
        {onAddNewClick && (
          <Item>
            <Button icon="plus" onClick={onAddNewClick} hint="Thêm mới" />
          </Item>
        )}
        {onRefresh && (
          <Item location="after">
            <Button icon="refresh" onClick={onRefresh} hint="Lấy lại dữ liệu mới" />
          </Item>
        )}
        <Item>
          <Button icon="clearformat" onClick={handleClearFilterClick} hint="Reset bộ lọc" />
        </Item>
        <Item name="columnChooserButton" />
        <Item name="exportButton" />
        {/* <Item name="searchPanel" /> */}
      </Toolbar>
      {/* <Column dataField="branchName" /> */}
      <ColumnFixing enabled />
      {!hideSerialNumber && (
        <Column
          dataField="serialNumber"
          caption="STT"
          dataType="number"
          format={',##0,##'}
          alignment="center"
          width={60}
          allowEditing={false}
          allowFiltering={false}
          allowSorting={false}
          fixed={true}
          fixedPosition="left"
          cellRender={(cellInfo: {
            column: dxDataGridColumn;
            columnIndex: number;
            component: dxDataGrid;
            data: Record<string, any>;
            displayValue: any;
            oldValue: any;
            row: dxDataGridRowObject;
            rowIndex: number;
            rowType: string;
            text: string;
            value: any;
            watch: () => void;
          }) => {
            if (cellInfo.rowType === 'data') {
              const pageIndex = cellInfo.component.pageIndex();
              const pageSize = cellInfo.component.pageSize();
              const visibleRowIndex = cellInfo.component
                .getVisibleRows()
                .filter(item => item.rowType === 'data')
                .indexOf(cellInfo.row);
              return pageIndex * pageSize + visibleRowIndex + 1;
            }
          }}
        />
      )}
      {children}
    </DataGrid>
  );

  function autofitDataGrid() {
    const instance = treeListRef.current?.instance();
    if (instance) {
      isAutoFitting.current = true;
      // const widthBefore = instance.element().scrollWidth;
      try {
        const table = instance.element().querySelector(".dx-datagrid-content[role='grid']");
        if (table) {
          if (table.classList.contains('whitespace-pre-line')) {
            table.classList.remove('whitespace-pre-line');
          }
          if (!table.classList.contains('whitespace-pre')) {
            table.classList.add('whitespace-pre');
          }
        }
        instance.beginUpdate();
        instance.getVisibleColumns().forEach((column: any) => {
          if (column.dataField) {
            if (column.dataField !== 'serialNumber') {
              instance.columnOption(column.dataField as string, 'width', 'auto');
            } else {
              instance.columnOption(column.dataField as string, 'width', 60);
            }
          }
        });
        instance.option('wordWrapEnabled', false);
        instance.endUpdate();
        instance.updateDimensions();
        const widthAuto = instance.element().scrollWidth;

        instance.beginUpdate();
        instance.getVisibleColumns().forEach((column: any) => {
          if (column.dataField) {
            if (column.dataField !== 'serialNumber') {
              instance.columnOption(column.dataField as string, 'width', undefined);
            } else {
              instance.columnOption(column.dataField as string, 'width', 60);
            }
          }
        });
        instance.endUpdate();
        instance.updateDimensions();
        const widthUndefined = instance.element().scrollWidth;

        let attribute = undefined;
        if (widthAuto >= widthUndefined) {
          attribute = 'auto';
        } else {
          attribute = undefined;
        }

        instance.getVisibleColumns().forEach((column: any) => {
          if (column.dataField) {
            if (column.dataField !== 'serialNumber') {
              instance.columnOption(column.dataField as string, 'width', attribute);
            } else {
              instance.columnOption(column.dataField as string, 'width', 60);
            }
          }
        });

        instance.endUpdate();
        instance.updateDimensions();

        instance.getVisibleColumns().forEach((column: any) => {
          if (column.dataField) {
            columnWidthMapRef.current.set(
              column.dataField as string,
              column.visibleWidth as number
            );
          }
        });

        setTimeout(() => {
          const state = instance.state();
          if (state) {
            instance.state({
              ...state,
              columns: (state.columns as Record<string, any>[])?.map(column => {
                return {
                  ...column,
                  width: columnWidthMapRef.current.get(column.dataField as string) || column.width,
                };
              }),
            });
          }
        }, 100);
      } catch (error) {
        console.error('Error autofitting columns:', error);
      } finally {
        setTimeout(() => {
          isAutoFitting.current = false;
          const table = instance.element().querySelector(".dx-datagrid-content[role='grid']");
          if (table) {
            if (table.classList.contains('whitespace-pre')) {
              table.classList.remove('whitespace-pre');
            }
            if (!table.classList.contains('whitespace-pre-line')) {
              table.classList.add('whitespace-pre-line');
            }
          }
        }, 200);
      }
    }
  }
}
