/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { TrainingTracking } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useNavigate } from 'react-router-dom';

const path = PATHS.TRAINING_TRACKING;

const t = translationWithNamespace('trainingTracking');
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');
export const TrainingTrackingDataTable = () => {
  const { list: staffStore } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  console.log('🚀 ~ TrainingTrackingDataTable ~ staffStore:', staffStore);
  const navigate = useNavigate();
  const role = usePermission(PERMISSIONS.TRAINING_TRACKING);
  const { data, refetch } = useQuery({
    queryKey: [QUERIES.TRAINING_TRACKING],
    queryFn: () => {
      return createQueryPaginationFn<TrainingTracking>('training-tracking')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });
  const { items } = data || { items: [] };

  //hiện thông tin khi nhấn vào nút xóa
  const getTargetAlias = (target: TrainingTracking | undefined) => {
    if (!target) {
      return '';
    }
    return ``;
  };

  const {
    selectedTarget,
    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,
    queryListParams,
    queryListMethods,
  } = useDataTable<TrainingTracking, PeriodFilter>({
    queryRangeName: 'trainingTrackingTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<TrainingTracking>('training-tracking'),
    deleteKey: [MUTATE.DELETE_TRAINING_TRACKING],
    invalidateKey: [QUERIES.TRAINING_TRACKING],
  });

  const onEditClick = (e: ColumnButtonClickEvent<TrainingTracking>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<TrainingTracking>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };
  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };
  const { isUpdate, isDelete } = role || {};
  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          if (values) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn('trainingTrackingTime', from!, to!);
          }
          callbackWithTimeout(refetch);
        }}
      ></PeriodFilterForm>

      <DevexDataGrid
        id={TABLES.TRAINING_TRACKING}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>

        {/* Ngày lập*/}
        <Column
          dataField="trainingTrackingTime"
          dataType="date"
          caption={t('fields.trainingTrackingTime')}
        />
        {/* Người lập*/}
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={staffStore} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="year"
          caption={t('fields.year')}
          dataType="date"
          format={'yyyy'}
        ></Column>

        {/* Ghi chú */}
        <Column dataField="note" caption={t('fields.note')} />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="trainingTracking"
      />
    </PageLayout>
  );
};
