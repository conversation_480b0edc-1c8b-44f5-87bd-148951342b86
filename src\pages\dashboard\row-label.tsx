import { Label } from 'devextreme-react/chart';
import { formatter, formatterMillion } from './methods';

export { Dashboard } from './dashboard';
export { DashboardProjectDeploymentStatusReport as ProjectDeploymentStatusReportTable } from './dashboard-project-deployment-status-report';

export const RowLabel = ({ formatMillion = false }: { formatMillion?: boolean }) => {
  return (
    <Label
      font={{ weight: 800, size: 15 }}
      visible={true}
      customizeText={formatMillion ? formatterMillion : formatter}
      rotationAngle={90}
      overlappingBehavior="none"
    />
  );
};
