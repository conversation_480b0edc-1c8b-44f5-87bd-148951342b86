/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission, useSyncProjectFilter } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { ComprehensiveInvestmentReport } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('comprehensiveInvestmentReport');

const path = PATHS.COMPREHENSIVE_INVESTMENT_REPORT;
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const ComprehensiveInvestmentReportDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('comprehensiveInvestmentReport');

  const role = usePermission(PERMISSIONS.COMPREHENSIVE_INVESTMENT_REPORT);

  // const { list: projects } = useEntity({ queryKey: [QUERIES.PROJECT], model: 'project' });
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const getTargetAlias = (target: ComprehensiveInvestmentReport | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };
  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<ComprehensiveInvestmentReport, PeriodFilter>({
    queryRangeName: 'comprehensiveInvestmentReportTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<ComprehensiveInvestmentReport>(
      'comprehensive-investment-report'
    ),
    deleteKey: [MUTATE.DELETE_COMPREHENSIVE_INVESTMENT_REPORT],
    invalidateKey: [QUERIES.COMPREHENSIVE_INVESTMENT_REPORT],
    initialQuery: {},
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.COMPREHENSIVE_INVESTMENT_REPORT],
    queryFn: () => {
      return createQueryPaginationFn<ComprehensiveInvestmentReport>(
        'comprehensive-investment-report'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Code',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<ComprehensiveInvestmentReport>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<ComprehensiveInvestmentReport>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  useSyncProjectFilter({
    queryListParams,
    queryListMethods,
    onSyncedParams: () => {
      callbackWithTimeout(refetch);
    },
  });

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'comprehensiveInvestmentReportTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.COMPREHENSIVE_INVESTMENT_REPORT}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="comprehensiveInvestmentReportTime"
          caption={t('fields.comprehensiveInvestmentReportTime')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="year" caption={t('fields.year')} alignment="left" />
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="comprehensiveInvestmentReport"
      />
    </PageLayout>
  );
};
