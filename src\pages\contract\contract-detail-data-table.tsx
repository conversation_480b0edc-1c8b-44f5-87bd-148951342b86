import { ErrorMessage } from '@/components/ui/error-message';
import {
  DEFAULT_DECIMAL,
  DEFAULT_DECIMAL_SCALE,
  downloadTemplateLabel,
  PROFESSIONS,
  QUERIES,
  TABLES,
} from '@/constant';
import {
  BiddingMethod,
  BiddingSector,
  ConstructionTask,
  Contract,
  ContractDetail,
  Contractor,
  CostItem,
  defaultValuesContract,
  IUserPermission,
  ResponseSuccess,
  TenderType,
  Unit,
} from '@/types';
import { useFormContext } from 'react-hook-form';

import { BasicDialog } from '@/components/basic-dialog';

import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { useBoolean, useEntity, useScreenSize } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { Button as DxButton } from 'devextreme-react';
import { convertMoneny, getRandomNumber } from '@/lib/number';
import { useMutation } from '@tanstack/react-query';
import axiosInstance from '@/axios-instance';
import notification from '@/lib/notifications';
import { customizeNumberCell, onCellPrepared } from '@/components/devex-data-grid';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  Editing,
  Lookup,
  Toolbar,
  Item,
  FilterRow,
  Scrolling,
  Paging,
  Pager,
  HeaderFilter,
  Search,
  SearchPanel,
  GroupPanel,
  type DataGridTypes,
  DataGridRef,
  IColumnProps as ColumnProps,
} from 'devextreme-react/data-grid';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';
import { displayExpr } from '@/lib/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { getUnitsByConstructionTask } from '@/components/business-construction-task-editable-data-table';
import { InputNumber } from '@/components/ui/input';

const [defaultRow] = defaultValuesContract.contractDetails;

type ContractDetailTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  tenderPackageId: number | null;
};

const t = translationWithNamespace('contract');

const getTotal = (items: ContractDetail[]) => {
  if (!items) return 0;
  return items.reduce((sum, row) => sum + (row.totalAmount || 0), 0);
};

// Tự động chọn (bôi đen) toàn bộ văn bản khi người dùng nhấp vào ô
const selectTextOnFocus = (args: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  args.element.querySelector('input.dx-texteditor-input')?.select();
};

export const ContractDetailDataTable = ({
  role,
  // calculateForm,
  tenderPackageId,
}: ContractDetailTableProps) => {
  const {
    setValue,
    watch,
    formState: { errors },
    getValues,
  } = useFormContext<Contract>();
  const tableRef = useRef<DataGridRef>(null);

  const [unexpectedCostPct, contractDetails] = watch(['unexpectedCostPct', 'contractDetails']);

  // Tự động tính toán lại tổng tiền mỗi khi chi tiết hợp đồng hoặc % chi phí dự phòng thay đổi
  useEffect(() => {
    const totalAmount = getTotal(contractDetails || []);
    const unexpectedCost = ((unexpectedCostPct || 0) / 100) * totalAmount;
    const contractAmount = unexpectedCost + totalAmount;

    setValue('contractAmount', contractAmount, { shouldValidate: true });
    setValue('totalAmount', totalAmount, { shouldValidate: true });
    setValue('unexpectedCost', unexpectedCost, { shouldValidate: true });
  }, [contractDetails, unexpectedCostPct, setValue]);

  const [currentConstructionTaskId, setCurrentConstructionTaskId] = useState<number | null>(null);

  const onRowInserting = useCallback((e: DataGridTypes.RowInsertingEvent) => {
    e.data.rowKeyId = -getRandomNumber();
  }, []);

  const onRowInserted = useCallback(
    (e: DataGridTypes.RowInsertedEvent) => {
      const currentDetails = getValues('contractDetails') || [];
      const isAlreadyExists = currentDetails.some(detail => detail.rowKeyId === e.data.rowKeyId);

      if (!isAlreadyExists) {
        const newDetails: ContractDetail[] = [...currentDetails, e.data];
        setValue('contractDetails', newDetails, { shouldValidate: true, shouldDirty: true });
      }

      const totalAmount = getTotal(currentDetails);
      const unexpectedCost = (unexpectedCostPct / 100) * totalAmount;
      const contractAmount = unexpectedCost + totalAmount;
      setValue('contractAmount', contractAmount);
      setValue('totalAmount', totalAmount);
      setValue('unexpectedCost', unexpectedCost);
    },
    [getValues, setValue, unexpectedCostPct]
  );

  const onRowRemoved = useCallback(
    (e: DataGridTypes.RowRemovedEvent) => {
      const currentDetails = getValues('contractDetails') || [];
      const newDetails = currentDetails.filter(detail => detail.rowKeyId !== e.key);
      setValue('contractDetails', newDetails, { shouldValidate: true, shouldDirty: true });
    },
    [getValues, setValue]
  );

  /**
   * Hàm tiện ích để cập nhật một dòng chi tiết hợp đồng trong state của react-hook-form.
   * Được bọc trong useCallback để tối ưu hiệu năng, tránh việc tạo lại hàm sau mỗi lần render.
   * @param rowKeyId - ID định danh của dòng cần cập nhật.
   * @param newValues - Một object chứa các giá trị mới cần cập nhật cho dòng đó.
   */
  const updateContractDetail = useCallback(
    (rowKeyId: number, newValues: Partial<ContractDetail>) => {
      // Lấy danh sách chi tiết hợp đồng hiện tại từ form state.
      // Sử dụng `getValues` để không gây ra re-render không cần thiết.
      const currentDetails = getValues('contractDetails') || [];

      // Tạo một mảng mới bằng cách duyệt qua mảng hiện tại.
      // Đây là cách tiếp cận bất biến (immutable), rất quan trọng cho React.
      const updatedDetails = currentDetails.map(detail =>
        // Nếu tìm thấy dòng có rowKeyId khớp,
        // tạo một object mới bằng cách kết hợp (merge) dữ liệu cũ và dữ liệu mới.
        // Dữ liệu mới (`newValues`) sẽ ghi đè lên dữ liệu cũ (`detail`) nếu có thuộc tính trùng lặp.
        detail.rowKeyId === rowKeyId ? { ...detail, ...newValues } : detail
      );

      // Cập nhật lại giá trị của trường 'contractDetails' trong form.
      setValue('contractDetails', updatedDetails, { shouldValidate: true, shouldDirty: true });
    },
    [getValues, setValue] // Dependencies của useCallback.
  );

  const onSetQuantityCellValue = useCallback(
    (newData: Partial<ContractDetail>, value: number, currentRowData: ContractDetail) => {
      const quantity = value ?? 0;
      const price = currentRowData.price ?? 0;
      const totalAmount = quantity * price;

      newData.quantity = quantity;
      newData.totalAmount = totalAmount;

      updateContractDetail(currentRowData.rowKeyId as number, { quantity, totalAmount });
    },
    [updateContractDetail]
  );

  const onSetPriceCellValue = useCallback(
    (newData: Partial<ContractDetail>, value: number, currentRowData: ContractDetail) => {
      const price = value ?? 0;
      const quantity = currentRowData.quantity ?? 0;
      const totalAmount = quantity * price;

      newData.price = price;
      newData.totalAmount = totalAmount;

      updateContractDetail(currentRowData.rowKeyId as number, { price, totalAmount });
    },
    [updateContractDetail]
  );

  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { fetch: fetchConstructionTaskItems, list: constructionTasks } =
    useEntity<ConstructionTask>({
      model: 'construction-task',
      queryKey: [QUERIES.CONSTRUCTION_TASK],
    });
  const { fetch: fetchCostItems } = useEntity<CostItem>({
    model: 'cost-item',
    queryKey: [QUERIES.COST_ITEM],
  });
  const { fetch: fetchTenderTypes } = useEntity<TenderType>({
    model: 'tender-type',
    queryKey: [QUERIES.TENDER_TYPE],
  });
  const { fetch: fetchBiddingMethods } = useEntity<BiddingMethod>({
    model: 'bidding-method',
    queryKey: [QUERIES.BIDDING_METHOD],
  });
  const { fetch: fetchBiddingSectors } = useEntity<BiddingSector>({
    model: 'bidding-sector',
    queryKey: [QUERIES.BIDDING_SECTOR],
  });
  const { fetch: fetchContractors, list: contractors } = useEntity<Contractor>({
    model: 'contractor',
    queryKey: [QUERIES.CONTRACTOR],
  });
  const { fetch: fetchUnits, list: units } = useEntity<Unit>({
    queryKey: [QUERIES.UNIT],
    model: 'unit',
  });

  // Không khai báo columnsForImportConfig ở đây vì component render lại nó khai báo lại
  const columnsForImportConfig: ColumnProps[] = [
    {
      dataField: 'constructionTaskId',
      caption: t('fields.constructionTaskId'),
      lookup: { dataSource: constructionTasks, displayExpr: 'name', valueExpr: 'id' },
      allowEditing: false,
    },
    {
      dataField: 'contractorId',
      caption: t('fields.contractorId'),
      lookup: { dataSource: contractors, displayExpr: 'name', valueExpr: 'id' },
      allowEditing: false,
    },
    {
      dataField: 'unitId',
      caption: t('fields.unitId'),
      lookup: { dataSource: units, displayExpr: 'name', valueExpr: 'id' },
      allowEditing: false,
    },
    {
      dataField: 'price',
      caption: t('fields.price'),
      customizeText: customizeNumberCell(0),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'quantity',
      caption: t('fields.quantity'),
      customizeText: customizeNumberCell(DEFAULT_DECIMAL_SCALE),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'totalAmount',
      caption: t('fields.totalAmount'),
      customizeText: customizeNumberCell(0),
      allowEditing: false,
      width: 120,
    },
    {
      dataField: 'note',
      caption: t('fields.note'),
      allowEditing: false,
      width: 120,
    },
  ];

  const getCalculatedRowValues = (row: ContractDetail) => ({
    totalAmount: convertMoneny((row.quantity ?? 0) * (row.price ?? 0)),
  });

  const { mutate: mutateContractDetail } = useMutation({
    mutationKey: [tenderPackageId],
    mutationFn: (tenderPackageId: number) =>
      axiosInstance.get<ResponseSuccess<ContractDetail>>(
        `/${'contract/get-data-contract-detail'}/${tenderPackageId}`
      ),
    onSuccess: dataContractDetail => {
      const data = dataContractDetail?.data?.data;
      if (!data) return;

      setValue('contractDetails', [
        { ...data, quantity: data.price || 0, price: data.quantity, rowKeyId: -getRandomNumber() },
      ]);

      // Kiểm tra data.constructionTaskId tồn tại trong constructionTasks
      const constructionTask = constructionTasks.find(item => item.id === data.constructionTaskId);
      console.log('constructionTask', constructionTask);
      if (!constructionTask) {
        fetchConstructionTaskItems({});
        fetchContractors({});
        fetchUnits({});
        void tableRef.current?.instance().refresh();
        tableRef.current?.instance().repaint();
      }
    },
    onError: error => {
      console.error('Error fetching contract details:', error);
      notification.error(error.message || 'Có lỗi vui lòng thử lại');
    },
  });

  const { height } = useScreenSize();

  const filteredUnits = useMemo(() => {
    const constructionTask = constructionTasks.find(item => item.id === currentConstructionTaskId);
    if (!constructionTask) return [];

    return getUnitsByConstructionTask(constructionTask) || [];
  }, [constructionTasks, currentConstructionTaskId]);

  return (
    <div>
      <DataGrid
        ref={tableRef}
        keyExpr={'rowKeyId'}
        id={TABLES.CONTRACT_DETAIL}
        dataSource={contractDetails}
        onRowInserting={onRowInserting}
        onRowInserted={onRowInserted}
        onRowRemoved={onRowRemoved}
        columnAutoWidth
        allowColumnResizing
        columnResizingMode="widget"
        allowColumnReordering
        showBorders
        showColumnLines
        showRowLines
        wordWrapEnabled
        hoverStateEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        remoteOperations={false}
        className={cn(
          'column-header-wrap',
          'max-h-[calc(100vh-9.8rem-32.8px)]',
          height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
        )}
        onInitNewRow={e => {
          e.data = { ...defaultRow };
        }}
        onEditorPreparing={e => {
          if (e.parentType !== 'dataRow') {
            return;
          }

          switch (e.dataField) {
            case 'unitId': {
              const rowData = e.row?.data as ContractDetail;
              setCurrentConstructionTaskId(rowData.constructionTaskId || null);
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
            }
            case 'constructionTaskId':
            case 'contractorId':
            case 'price':
            case 'quantity':
            case 'note':
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
          }
        }}
        onCellPrepared={e => {
          onCellPrepared(e);
        }}
      >
        <Editing
          mode="cell"
          allowUpdating={role?.isCreate || role?.isUpdate}
          allowDeleting={role?.isCreate || role?.isUpdate}
          allowAdding={role?.isCreate || role?.isUpdate}
          confirmDelete={false}
          useIcons
          newRowPosition="last"
        />
        <ColumnChooser enabled mode="select" height="45rem">
          <ColumnChooserSearch enabled />
          <ColumnChooserSelection allowSelectAll selectByClick recursive />
        </ColumnChooser>
        <FilterRow visible showOperationChooser />
        <Scrolling mode="standard" rowRenderingMode="standard" />
        <Paging enabled defaultPageSize={10} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={[5, 10, 50, 100]}
        />
        <HeaderFilter visible>
          <Search enabled mode="contains" />
        </HeaderFilter>
        <SearchPanel visible />
        <GroupPanel visible />
        <Column
          dataField="serialNumber"
          caption="STT"
          dataType="number"
          format={',##0,##'}
          alignment="center"
          width={60}
          allowEditing={false}
          allowFiltering={false}
          allowSorting={false}
          fixedPosition="left"
          cellRender={(cellInfo: {
            column: dxDataGridColumn;
            columnIndex: number;
            component: dxDataGrid;
            data: Record<string, any>;
            displayValue: any;
            oldValue: any;
            row: dxDataGridRowObject;
            rowIndex: number;
            rowType: string;
            text: string;
            value: any;
            watch: () => void;
          }) => {
            if (cellInfo.rowType === 'data') {
              const pageIndex = cellInfo.component.pageIndex();
              const pageSize = cellInfo.component.pageSize();
              const visibleRowIndex = cellInfo.component
                .getVisibleRows()
                .filter(item => item.rowType === 'data')
                .indexOf(cellInfo.row);
              return pageIndex * pageSize + visibleRowIndex + 1;
            }
          }}
        />
        {/* Tên công việc */}
        <Column
          dataField="constructionTaskId"
          caption={t('fields.constructionTaskId')}
          setCellValue={(newData, value) => {
            // Gán giá trị ID mới của công việc
            if (value) {
              newData.constructionTaskId = value;
              // Tìm công việc trong danh sách để lấy tên và cập nhật vào dữ liệu của hàng
              const task = constructionTasks.find(t => t.id === value);
              newData.name = task?.name;
            } else {
              newData.constructionTaskId = null;
              newData.name = '';
            }
            // Khi "Tên công việc" thay đổi, chúng ta cần reset "Đơn vị tính"
            // vì danh sách đơn vị tính hợp lệ phụ thuộc vào công việc đã chọn.
            newData.unitId = null;
            newData.unitName = '';
          }}
        >
          <Lookup
            dataSource={constructionTasks}
            displayExpr={displayExpr(['name'])}
            valueExpr={'id'}
          />
        </Column>
        {/* Đơn vị thực hiện */}
        <Column dataField="contractorId" caption={t('fields.contractorId')}>
          <Lookup dataSource={contractors} displayExpr={'name'} valueExpr={'id'} />
        </Column>
        {/* Đơn vị tính */}
        <Column
          dataField="unitId"
          caption={t('fields.unitId')}
          calculateDisplayValue={'unitName'}
          cellRender={(cellInfo: DataGridTypes.ColumnCellTemplateData<ContractDetail>) => {
            const { value, data } = cellInfo;
            // Get tên từ biến units bằng value
            if (!data?.unitName) {
              const unit = units.find(item => item.id === value);
              return unit?.name;
            }

            return data?.unitName;
          }}
        >
          <Lookup dataSource={filteredUnits} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        {/* Khối lượng */}
        <Column
          dataField="quantity"
          caption={t('fields.quantity')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL_SCALE)}
          setCellValue={onSetQuantityCellValue}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                decimal={DEFAULT_DECIMAL_SCALE}
                onChange={(value: number | undefined) => {
                  if (value === undefined) return;
                  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                  cell.setValue(value);
                }}
              />
            );
          }}
        />
        {/* Đơn giá */}
        <Column
          dataField="price"
          caption={t('fields.price')}
          customizeText={customizeNumberCell(0)}
          setCellValue={onSetPriceCellValue}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                isMoney
                decimal={DEFAULT_DECIMAL}
                onChange={(value: number | undefined) => {
                  if (value === undefined) return;
                  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                  cell.setValue(value);
                }}
              />
            );
          }}
        />
        {/* Thành tiền */}
        <Column
          dataField="totalAmount"
          allowEditing={false}
          caption={t('fields.totalAmountDetail')}
          customizeText={customizeNumberCell(0)}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                isMoney
                decimal={DEFAULT_DECIMAL}
                readOnly
              />
            );
          }}
        />
        {/* Ghi chú */}
        <Column dataField="note" caption={t('fields.note')} />
        <Toolbar>
          {errors.contractDetails?.message && (
            <ErrorMessage message={errors.contractDetails?.message} />
          )}
          <Item location="before">
            <DxButton
              stylingMode="text"
              icon="download"
              text={downloadTemplateLabel}
              type="default"
              onClick={() => {
                window.open(`/templates/contractor-selection-plan/mau_import_hop_dong.xlsx`);
              }}
            />
          </Item>
          <Item location="before" locateInMenu="auto">
            <DxButton
              stylingMode="text"
              icon="upload"
              text="Import Excel"
              type="default"
              onClick={toggleImportForm}
            />
          </Item>
          <Item location="before" locateInMenu="auto">
            <DxButton
              icon="pulldown"
              text={t('page.buttonGetValueContract')}
              onClick={() => {
                if (!tenderPackageId) {
                  return;
                }
                mutateContractDetail(tenderPackageId);
                void tableRef.current?.instance().refresh();
              }}
              type="default"
              stylingMode="contained"
            />
          </Item>
          <Item location="after" name="addRowButton" />
          {(role?.isCreate || role?.isUpdate) && (
            <Item location="after">
              <DxButton
                icon="trash"
                hint={'Xóa tất cả'}
                onClick={() => {
                  setValue('contractDetails', []);
                  setValue('totalAmount', 0);
                  setValue('contractAmount', 0);
                }}
              />
            </Item>
          )}
          <Item location="after" name="columnChooserButton" />
        </Toolbar>
      </DataGrid>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ContractDetail>
          importModel="contract"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.CONTRACT}
          devDataGridColumns={columnsForImportConfig}
          useDevDataGrid={true}
          onApply={data => {
            const contractId = getValues('id');
            const newDetails = data.map(item => {
              return {
                ...defaultRow,
                ...item,
                ...getCalculatedRowValues(item),
                rowKeyId: -getRandomNumber(),
                contractId,
              };
            });
            setValue('contractDetails', [...newDetails]);
            toggleImportForm();
          }}
          onImported={() => {
            fetchConstructionTaskItems({});
            fetchCostItems({});
            fetchTenderTypes({});
            fetchBiddingMethods({});
            fetchBiddingSectors({});
            fetchContractors({});
          }}
        />
      </BasicDialog>
    </div>
  );
};
