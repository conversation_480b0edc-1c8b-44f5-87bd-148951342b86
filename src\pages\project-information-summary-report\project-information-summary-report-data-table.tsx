/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { PageLayout } from '@/components/page-layout';
import { PERCENT_FORMAT_WITH_SUFFIX, QUERIES } from '@/constant';
import { createQueryReport } from '@/services';
import { ProjectInformationSummaryReport } from '@/types';
import { useMutation } from '@tanstack/react-query';

import { YearProjectDataFilter } from './year-project-data-filter';
import { useForm } from 'react-hook-form';
import { PeriodFilter } from '@/components/period-filter-form';
import { useDataTable } from '@/hooks';
import { QueryType } from './query-type';
import { Form, FormField, FormLabel } from '@/components/ui/form';
import { TextBox } from 'devextreme-react';
import { Textarea } from '@/components/ui/textarea';
import { InputNumber } from '@/components/ui/input';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { cn } from '@/lib/utils';

const labelWidthClass = 'w-[170px]';
export const ProjectInformationSummaryReportDataTable = () => {
  const { t } = useTranslation('projectInformationSummaryReport');
  const queryForm = useForm<QueryType>({
    defaultValues: { year: new Date(), projectId: null },
  });

  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<ProjectInformationSummaryReport, PeriodFilter>({
    initialQuery: {
      objParam: {
        year: new Date(),
        projectId: null,
      },
    },
  });

  const methods = useForm<ProjectInformationSummaryReport>({});

  const { mutate: refetchDetails } = useMutation({
    mutationKey: [QUERIES.PROJECT_INFORMATION_SUMMARY_REPORT, queryListParams],
    mutationFn: ({ year, projectId }: QueryType) => {
      return createQueryReport<ProjectInformationSummaryReport>(
        'project-information-summary-report'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        objParam: {
          year: year,
          projectId: projectId,
        },
      });
    },
    onSuccess: data => {
      const dataProject = data?.items?.at(0);
      methods.reset({
        ...dataProject,
      });
    },
  });
  const [year, setYear] = useState<Date | null | undefined>(new Date());

  return (
    <PageLayout header={t('page.header')}>
      <div className="items-top flex gap-x-4">
        <YearProjectDataFilter
          form={queryForm}
          onSearch={() => {
            const values = queryForm.getValues();
            setYear(values.year);

            queryListMethods.setQueryListParams(prev => ({
              ...prev,
              objParam: {
                year: values.year,
                projectId: values.projectId,
              },
            }));

            refetchDetails({ year: values.year || new Date(), projectId: values.projectId });
          }}
        />
      </div>
      <div className="mt-8">
        <Form {...methods}>
          <form autoComplete="off">
            <PageLayout>
              <div className="grid grid-cols-1  gap-x-8 gap-y-4 lg:grid-cols-24">
                <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-12 2xl:col-span-12">
                  <div className="flex flex-row items-center ">
                    <FormLabel
                      name="projectName"
                      htmlFor="projectName"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.projectName')}
                    </FormLabel>
                    <FormField
                      label={t('fields.projectName')}
                      id="projectName"
                      name="projectName"
                      className="w-full flex-1"
                    >
                      <TextBox />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center ">
                    <FormLabel
                      htmlFor="projectLocation"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.projectLocation')}
                    </FormLabel>
                    <FormField
                      id="projectLocation"
                      name="projectLocation"
                      className="w-full flex-1"
                      label={t('fields.projectLocation')}
                    >
                      <TextBox />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center lg:w-2/3">
                    <FormLabel
                      name="totalInvestment"
                      htmlFor="totalInvestment"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.totalInvestment')}
                    </FormLabel>
                    <FormField
                      label={t('fields.totalInvestment')}
                      id="totalInvestment"
                      name="totalInvestment"
                      className="w-full flex-1"
                    >
                      <InputNumber format={`#,##0.000 ${t('fields.billion')}`} />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center ">
                    <FormLabel
                      htmlFor="constructionDate"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.constructionDate')}
                    </FormLabel>
                    <FormField
                      id="constructionDate"
                      name="constructionDate"
                      className="w-full flex-1"
                      label={t('fields.constructionDate')}
                    >
                      <TextBox />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center lg:w-2/3">
                    <FormLabel
                      name="yearlyPlan"
                      htmlFor="yearlyPlan"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.yearlyPlan', {
                        year: year?.getFullYear() || '',
                      })}
                    </FormLabel>
                    <FormField
                      label={t('fields.yearlyPlan', {
                        year: year?.getFullYear() || '',
                      })}
                      id="yearlyPlan"
                      name="yearlyPlan"
                      className="w-full flex-1"
                      aria-readonly
                    >
                      <InputNumber format={`#,##0.000 ${t('fields.billion')}`} />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center ">
                    <FormLabel
                      htmlFor="progress"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.progress')}
                    </FormLabel>
                    <FormField
                      id="progress"
                      name="progress"
                      className="w-full flex-1"
                      label={t('fields.progress')}
                    >
                      <Textarea />
                    </FormField>
                  </div>

                  <div className="flex flex-row items-center lg:w-2/3">
                    <FormLabel
                      name="cumulativeDisbursementYearToDate"
                      htmlFor="cumulativeDisbursementYearToDate"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.cumulativeDisbursementYearToDate')}
                    </FormLabel>
                    <FormField
                      label={t('fields.cumulativeDisbursementYearToDate')}
                      id="cumulativeDisbursementYearToDate"
                      name="cumulativeDisbursementYearToDate"
                      className="w-full flex-1"
                    >
                      <InputNumber format={`#,##0.000 ${t('fields.billion')}`} />
                    </FormField>
                  </div>
                  <div className="flex flex-row items-center lg:w-2/3">
                    <FormLabel
                      name="disbursementRateYearToDate"
                      htmlFor="disbursementRateYearToDate"
                      className={cn(['hidden md:block', labelWidthClass])}
                    >
                      {t('fields.disbursementRateYearToDate')}
                    </FormLabel>
                    <FormField
                      label={t('fields.disbursementRateYearToDate')}
                      id="disbursementRateYearToDate"
                      name="disbursementRateYearToDate"
                      className="w-full flex-1"
                    >
                      <InputNumber format={PERCENT_FORMAT_WITH_SUFFIX} />
                    </FormField>
                  </div>
                </div>
                <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-12 2xl:col-span-12"></div>
                {/* <div className="col-span-1  grid grid-cols-1 flex-col gap-x-8 gap-y-4 lg:col-span-24 lg:grid-cols-24">
                  <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-22  2xl:col-span-16">
                    <div className="flex flex-row items-center ">
                      <FormLabel htmlFor="note" className="hidden w-[120px] md:block ">
                        {t('fields.note')}
                      </FormLabel>
                      <FormField
                        id="note"
                        name="note"
                        className="w-full flex-1"
                        label={t('fields.note')}
                      >
                        <Textarea />
                      </FormField>
                    </div>
                  </div>
                </div> */}
              </div>
            </PageLayout>
          </form>
        </Form>
      </div>
    </PageLayout>
  );
};
