import { Form, Form<PERSON>ombobox, <PERSON><PERSON><PERSON>, FormLabel, FormRadioGroup } from '@/components/ui/form';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  CONTRACT_TYPES,
  enterLabel,
  MUTATE,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
  TABLES,
} from '@/constant';
import {
  useAuth,
  useEntity,
  useFormHandlerPromise,
  useFormOperation,
  usePermission,
  useSendNotification,
} from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { formatDate, toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn, Model } from '@/services';
import {
  Contract,
  ContractTaskManagement,
  ContractTaskManagementDocuments,
  contractTaskManagementSchema,
  defaultValuesContractTaskManagement,
  Department,
  Project,
} from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { CheckBox, DateBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { SyntheticEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';

import { DataTable, EditableInputCell } from '@/components/data-table';
import { DocumentExportPreparation } from '@/components/document-export-preparation';
import { RecordEditableTable } from '@/components/records-attachment';
import { InputNumber } from '@/components/ui/input';
import { getRandomNumber, realNumberDecimalFormat, roundScaleNumber } from '@/lib/number';
import {
  decimalToVietnameseWords,
  getValidId,
  modelToCamelCase,
  modelToUpperSnakeCase,
} from '@/lib/utils';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { PageLayout } from './page-layout';
import { ContractTaskManagementSelectFromContractWindow } from './contract-task-mangement-from-contract-table';
import { ContractTaskManagementFormDetail } from './contract-task-management-form-detail';
import {
  ContractTaskManagementResponse,
  getApproveDate,
  getApproveTimeText,
  getNotifyContent,
  getOpinionDate,
  getOpinionTimeText,
} from './utils';
import { useQuery } from '@tanstack/react-query';
import { PrintDataSource } from '@/lib/print';
import { ContractTaskManagementDirectorApproveForm } from '.';

type ContractTaskManagementFormTemplateProps = {
  model: Model;
  defaltValues?: Partial<ContractTaskManagement>;
};
export const ContractTaskManagementFormTemplate = ({
  model,
  defaltValues,
}: ContractTaskManagementFormTemplateProps) => {
  const path = useLocation().pathname.replace(/\/(\d+|new)\/?$/, '');

  const {
    ns,
    permission,
    profession,
    queryKey,
    queryKeyCode,
    mutateKey,
    tableKey,
    notifyNew,
    notifyUpdated,
    notifyRechecked,
    notifyNeedApprove,
    onContractTaskManagementMutationSuccess,
  } = useMemo(() => {
    const ns = modelToCamelCase(model);
    const key = modelToUpperSnakeCase(model);
    const keyCode = key + '_CODE';
    const keyDocument = key + '_DOCUMENTS';

    const permission = PERMISSIONS[key as keyof typeof PERMISSIONS];
    const profession = PROFESSIONS[key as keyof typeof PROFESSIONS];

    const queryKey = QUERIES[key as keyof typeof QUERIES];
    const queryKeyCode = QUERIES[keyCode as keyof typeof QUERIES];
    const mutateKey = MUTATE[key as keyof typeof MUTATE];

    const tableKey = TABLES[keyDocument as keyof typeof TABLES];

    const notifyNew = ns + '.new';
    const notifyUpdated = ns + '.updated';
    const notifyRechecked = ns + '.rechecked';
    const notifyNeedApprove = ns + '.needApprove';
    const notifyAddNew = ns + '.addNew';
    const notifyInfo = ns + '.info';
    const notifyContent = ns + '.content';

    const onContractTaskManagementMutationSuccess = createMutationSuccessFn(ns);

    return {
      ns,
      permission,
      profession,
      queryKey,
      queryKeyCode,
      mutateKey,
      tableKey,
      notifyNew,
      notifyUpdated,
      notifyRechecked,
      notifyNeedApprove,
      notifyAddNew,
      notifyInfo,
      notifyContent,
      onContractTaskManagementMutationSuccess,
    };
  }, [model]);
  const { t } = useTranslation([ns]);
  const { id: editId } = useParams();
  const role = usePermission(permission);
  const { sendNotify } = useSendNotification();

  const { user, projects } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isEligibleForSigning, setEligibleForSigning] = useState<boolean>(true);
  const [isNotEligibleForSigning, setNotEligibleForSigning] = useState<boolean>(false);

  const { list: departments } = useEntity<Department>({
    queryKey: [QUERIES.DEPARTMENT],
    model: 'department',
  });

  const defaultValues = useMemo(() => {
    return {
      ...defaultValuesContractTaskManagement,
      ...defaltValues,
      branchId: user?.defaultBrachId,
      storeId: 0,
    };
  }, [user?.defaultBrachId, defaltValues]);

  const { handleSubmitPromise, loading, methods, resetQueryId } =
    useFormHandlerPromise<ContractTaskManagement>({
      queryKey: [mutateKey, editId],
      mutateKey: [mutateKey],
      queryId: Number(editId) || 0,
      invalidateKey: [queryKey],
      readFn: createQueryByIdFn<ContractTaskManagement>(model),
      createFn: createPostMutateFn<ContractTaskManagement>(model),
      updateFn: createPutMutateFn<ContractTaskManagement>(model),

      formatPayloadFn: data => {
        const formattedData = {
          ...data,
          contractTaskManagementTime: toLocaleDate(data.contractTaskManagementTime)!,
          completionTime: toLocaleDate(data.completionTime ?? null)!,
          contractTaskManagementProjectDepartments:
            data.contractTaskManagementProjectDepartments.map(item => ({
              ...item,
              approveTime: getApproveDate(item),
              opinionTime: getOpinionDate(item),
              id: getValidId(item.id),
            })),
          contractTaskManagementFinanceDepartments:
            data.contractTaskManagementFinanceDepartments.map(item => ({
              ...item,
              receivedTime: toLocaleDate(item.receivedTime ?? null)!,
              completionTime: toLocaleDate(item.completionTime ?? null)!,
              id: getValidId(item.id),
              approveTime: getApproveDate(item),
              opinionTime: getOpinionDate(item),
            })),
          contractTaskManagementPlanningDepartments:
            data.contractTaskManagementPlanningDepartments.map(item => ({
              ...item,
              receivedTime: toLocaleDate(item.receivedTime ?? null)!,
              completionTime: toLocaleDate(item.completionTime ?? null)!,
              id: getValidId(item.id),
              approveTime: getApproveDate(item),
              opinionTime: getOpinionDate(item),
            })),
          contractTaskManagementDocuments: data.contractTaskManagementDocuments.map(item => ({
            ...item,
            id: getValidId(item.id),
          })),
          itemsRecordManagement: data.itemsRecordManagement
            .filter(item => item.content)
            .map(itemRecord => ({
              ...itemRecord,
              id: getValidId(itemRecord.id),
              dateCreate: toLocaleDate(itemRecord.dateCreate!),
              itemFile: itemRecord.itemFile
                .filter(file => file.fileName)
                .map(file => ({ ...file, id: getValidId(file.id) })),
            })),
          isNotEligibleForSigning: isNotEligibleForSigning ?? false,
          isEligibleForSigning: isEligibleForSigning ?? false,
        };
        return formattedData;
      },

      formatResponseFn: data => {
        setEligibleForSigning(data.isEligibleForSigning ?? false);
        setNotEligibleForSigning(data.isNotEligibleForSigning ?? false);
        const response = {
          ...data,
          contractTaskManagementTime: toDateType(data.contractTaskManagementTime)!,
          completionTime: toDateType(data.completionTime ?? null)!,
          contractTaskManagementProjectDepartments:
            data.contractTaskManagementProjectDepartments.map(item => ({
              ...item,
              approveTime: toDateType(item.approveTime ?? null)!,
              opinionTime: toDateType(item.opinionTime ?? null)!,
            })),
          contractTaskManagementFinanceDepartments:
            data.contractTaskManagementFinanceDepartments.map(item => ({
              ...item,
              receivedTime: toDateType(item.receivedTime ?? null)!,
              completionTime: toDateType(item.completionTime ?? null)!,
              approveTime: toDateType(item.approveTime ?? null)!,
              opinionTime: toDateType(item.opinionTime ?? null)!,
            })),
          contractTaskManagementPlanningDepartments:
            data.contractTaskManagementPlanningDepartments.map(item => ({
              ...item,
              receivedTime: toDateType(item.receivedTime ?? null)!,
              completionTime: toDateType(item.completionTime ?? null)!,
              approveTime: toDateType(item.approveTime ?? null)!,
              opinionTime: toDateType(item.opinionTime ?? null)!,
            })),
          contractTaskManagementDocuments: data.contractTaskManagementDocuments.map(item => ({
            ...item,
          })),
          itemsRecordManagement: data.itemsRecordManagement.map(item => ({
            ...item,
            dateCreate: toDateType(item.dateCreate!),
          })),
        };
        return response;
      },

      onCreateSuccess: data => {
        onContractTaskManagementMutationSuccess(data);
        goToUpdate(data);
      },

      onUpdateSuccess: onContractTaskManagementMutationSuccess,

      formOptions: {
        resolver: zodResolver(contractTaskManagementSchema),
        defaultValues,
      },
    });

  //Quản lý dự án, Tài chính tổng hợp, Kế hoạch chất lượng
  const [
    fieldsProjectDepartment,
    fieldsFinanceDepartment,
    fieldsPlanningDepartment,
    contractTaskManagementDocuments,
    projectManagementDepartmentName,
    projectManagementDirectorName,
    projectManagementDirectorId,
    projectManagementDepartmentId,
    projectId,
    contractType,
    contractId,
    isSendPmoDirector,
  ] = methods.watch([
    'contractTaskManagementProjectDepartments',
    'contractTaskManagementFinanceDepartments',
    'contractTaskManagementPlanningDepartments',
    'contractTaskManagementDocuments',
    'projectManagementDepartmentName',
    'projectManagementDirectorName',
    'projectManagementDirectorId',
    'projectManagementDepartmentId',
    'projectId',
    'contractType',
    'contractId',
    'isSendPmoDirector',
  ]);

  const { data: contract } = useQuery({
    queryKey: [QUERIES.CONTRACT, contractId],
    queryFn: () => createQueryByIdFn<Contract>('contract')(contractId || 0),
    enabled: !!contractId,
  });
  const { data: project } = useQuery({
    queryKey: [QUERIES.PROJECT, projectId],
    queryFn: () => createQueryByIdFn<Project>('project')(projectId || 0),
    enabled: !!projectId,
  });

  const { reset, onTimeChange } = useFormOperation<ContractTaskManagement>({
    model: model,
    fieldTime: 'contractTaskManagementTime',
    createCodeKey: [queryKeyCode],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
    resetQueryId();
  };

  //phụ lục hô sơ đính kèm
  const contractTaskManagementDocumentColumns: ColumnDef<ContractTaskManagementDocuments>[] =
    useMemo(() => {
      return [
        {
          id: 'components',
          accessorKey: 'components',
          header: t('fields.contractTaskManagementDocuments.components'),
          cell: props => <EditableInputCell {...props} readOnly={true} />,
          size: 750,
        },
        {
          id: 'quantity',
          accessorKey: 'quantity',
          header: t('fields.contractTaskManagementDocuments.quantity'),
          cell: props => <EditableInputCell {...props} type="number" />,
          size: 50,
        },
        {
          id: 'isAvailable',
          accessorKey: 'isAvailable',
          header: t('fields.contractTaskManagementDocuments.isAvailable'),
          cell: props => {
            return (
              <div className="mt-2 flex items-center justify-center">
                <CheckBox
                  value={props.getValue<boolean>()}
                  onValueChanged={e => {
                    props.table.options.meta?.updateData(props.row.index, props.column.id, e.value);
                  }}
                />
              </div>
            );
          },
          size: 50,
        },
        {
          id: 'isNotAvailable',
          accessorKey: 'isNotAvailable',
          header: t('fields.contractTaskManagementDocuments.isNotAvailable'),
          cell: props => {
            return (
              <div className="mt-2 flex items-center justify-center">
                <CheckBox
                  value={props.getValue<boolean>()}
                  onValueChanged={e => {
                    props.table.options.meta?.updateData(props.row.index, props.column.id, e.value);
                  }}
                />
              </div>
            );
          },
          size: 50,
        },
      ];
    }, [t]);

  //Start: in phiếu
  // const { data: printData = [], refetch } = useQuery<SaleOrderPrint[]>({
  //   queryKey: ['PRINT', QUERIES.CONTRACT_TASK_MANAGEMENT, editId],
  //   queryFn: () => {
  //     return postRequest<SaleOrderPrint[]>(
  //       '/contractor-selection-plan/print-contractor-selection-plan-by-ids',
  //       [editId]
  //     );
  //   },
  //   enabled: false,
  // });

  const getPrintDataSource = (): PrintDataSource => {
    const data = methods.getValues();
    // add order number in details
    const details = data.contractTaskManagementDocuments.map(
      (item: ContractTaskManagementDocuments, index: number) => {
        return { ...item, orderNumber: index + 1 };
      }
    );

    return {
      ...data,
      contractTaskManagementTime: formatDate(data.contractTaskManagementTime),
      contractTaskManagementFinanceDepartments: data.contractTaskManagementFinanceDepartments.map(
        item => ({
          ...item,
          receivedTime: formatDate(item.receivedTime ?? null),
          completionTime: formatDate(item.completionTime ?? null),
        })
      ),
      contractTaskManagementPlanningDepartments: data.contractTaskManagementPlanningDepartments.map(
        item => ({
          ...item,
          receivedTime: formatDate(item.receivedTime ?? null),
          completionTime: formatDate(item.completionTime ?? null),
        })
      ),
      completionTime: formatDate(data.completionTime),
      documents: [...details],
      formDocumentManagerId: contract?.formDocumentManagerId || 0,
    };
  };

  const getFormattedDateString = (date: Date | null | undefined): string => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) return '...';
    return formatDate(date);
  };

  const getPrintDataSourceContract = () => {
    const data = { ...contract };
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    // sanitizeObject(data);
    const signingDateValue = contract?.signingDate;
    const signingDate = new Date(contract?.signingDate as string | number | Date);
    const dots = '...';
    const advancePayment =
      ((contract?.advancePaymentPercen || 0) / 100) * (contract?.totalAmount || 0);
    const totalAmount: number = contract?.totalAmount ?? 0;

    return {
      ...data,
      day: signingDateValue ? signingDate.getDate() : dots,
      month: signingDateValue ? signingDate.getMonth() + 1 : dots,
      year: signingDateValue ? signingDate.getFullYear() : dots,
      totalAmount:
        totalAmount === 0
          ? dots
          : realNumberDecimalFormat(roundScaleNumber(totalAmount).toString()),
      totalAmountText:
        totalAmount === 0 ? dots : decimalToVietnameseWords(roundScaleNumber(totalAmount)),
      advancePayment:
        advancePayment === 0
          ? dots
          : realNumberDecimalFormat(roundScaleNumber(advancePayment).toString()),
      advancePaymentText:
        advancePayment === 0 ? dots : decimalToVietnameseWords(roundScaleNumber(advancePayment)),
      contractTime: getFormattedDateString(contract?.contractTime),
      acceptanceDate: getFormattedDateString(contract?.acceptanceDate),
      liquidationDate: getFormattedDateString(contract?.liquidationDate),
      signingDate: getFormattedDateString(contract?.signingDate),
      implementationDate: getFormattedDateString(contract?.implementationDate),
      advancePaymentGuaranteeExpiryDate: getFormattedDateString(
        contract?.advancePaymentGuaranteeExpiryDate
      ),
      expectedCompletionDate: getFormattedDateString(contract?.expectedCompletionDate),
      contractGuaranteeExpiryDate: getFormattedDateString(contract?.contractGuaranteeExpiryDate),
    };
  };

  const contractData = getPrintDataSourceContract();

  // Tạo option của Radio Group
  const eligibleForSignings = [
    { id: 0, name: t('fields.isNotEligibleForSigning') },
    { id: 1, name: t('fields.isEligibleForSigning') },
  ];

  // Render tùy option của Radio Group
  const renderRadioGroupItem = (item: { id: number; name: string }) => {
    return (
      <div className="my-2 flex flex-col items-center justify-center">
        <FormLabel name={item.name} htmlFor={item.name}>
          {item.name}
        </FormLabel>
      </div>
    );
  };

  // Server đang lưu giá trị boolean nên tạm thời chuyển đổi từ số sang boolean
  const onValueChanged = (value: any) => {
    if (value == 0) {
      setNotEligibleForSigning(true);
      setEligibleForSigning(false);
    } else {
      setNotEligibleForSigning(false);
      setEligibleForSigning(true);
    }
  };

  // Set check mặc định cho Radio Group
  const defaultEligibleForSigningCheck = () => {
    if (editId === 'new') {
      setNotEligibleForSigning(true);
    }
    return isNotEligibleForSigning ? eligibleForSignings[0].id : eligibleForSignings[1].id;
  };

  //End: In phiếu

  //Phòng kế hoạch chất lượng, phòng tài chính tổng hợp
  const notifyDepartments = departments.filter(d => d.id === 3 || d.id === 4);

  const isProjectDepartmentApprove =
    fieldsProjectDepartment.length > 0 &&
    fieldsProjectDepartment[fieldsProjectDepartment.length - 1].isApprove === true;
  const isFinanceDepartmentApprove =
    fieldsFinanceDepartment.length > 0 &&
    fieldsFinanceDepartment[fieldsFinanceDepartment.length - 1].isApprove === true &&
    fieldsFinanceDepartment[fieldsFinanceDepartment.length - 1].actionType === 2;
  const isPlanningDepartmentApprove =
    fieldsPlanningDepartment.length > 0 &&
    fieldsPlanningDepartment[fieldsPlanningDepartment.length - 1].isApprove === true &&
    fieldsPlanningDepartment[fieldsPlanningDepartment.length - 1].actionType === 2;

  const isContractTask = model === 'contract-task-management';

  let isDisableExecutionResult = false;
  if (isContractTask) {
    isDisableExecutionResult = !(
      isProjectDepartmentApprove &&
      isFinanceDepartmentApprove &&
      isPlanningDepartmentApprove
    );
  } else {
    isDisableExecutionResult = !(isProjectDepartmentApprove && isPlanningDepartmentApprove);
  }

  const isPmoDirector = project?.pmoDirectorId === user?.userId;

  const getContent = useCallback(
    (opinion: string) => {
      return getNotifyContent({
        projectName: methods.getValues('projectName'),
        contractNumber: contract?.contractNumber,
        contractName: contract?.contractName,
        opinion: opinion,
      });
    },
    [methods, contract?.contractNumber, contract?.contractName]
  );

  function sendNotificationToDirector(): void {
    if (projectId && project) {
      sendNotify({
        title: t(notifyNeedApprove, {
          ns: 'sendNotification',
        }),
        content: getContent('Cần duyệt'),
        typeNotification: path,
        refId: Number(methods.getValues('id')) || null,
        userIds: [project.pmoDirectorId],
      });
      methods.setValue('isSendPmoDirector', true);
      void handleSubmitPromise();
    }
  }
  const [hasSelectProject, setHasSelectProject] = useState(false);

  const { data: selectedProjectDetail } = useQuery({
    queryKey: [QUERIES.PROJECT, projectId],
    queryFn: () => createQueryByIdFn<Project>('project')(projectId),
    enabled: !!projectId && hasSelectProject,
  });

  useEffect(() => {
    if (selectedProjectDetail && hasSelectProject) {
      methods.setValue(
        'projectManagementDirectorId',
        selectedProjectDetail.projectManagementDirectorId || user?.userId || 0
      );
      methods.setValue(
        'projectManagementDepartmentId',
        selectedProjectDetail.departmentInChargeId || 0
      );

      //cán bộ được giao TC-TH
      methods.setValue(
        'contractTaskManagementFinanceDepartments',
        methods.getValues('contractTaskManagementFinanceDepartments').map(item => ({
          ...item,
          assignedUserId: selectedProjectDetail.projectHumanResources.find(
            item => item.departmentType === 3
          )?.memberId,
        }))
      );

      //cán bộ được giao KH-CL
      let assignId = 0;
      //hợp đồng tư vấn
      if (contractType === 1) {
        assignId = departments.find(d => d.id === 3)?.consultingContractOfficerId || 0;
      } else {
        assignId =
          selectedProjectDetail.projectHumanResources.find(item => item.departmentType === 2)
            ?.memberId || 0;
      }
      methods.setValue(
        'contractTaskManagementPlanningDepartments',
        methods.getValues('contractTaskManagementPlanningDepartments').map(item => ({
          ...item,
          assignedUserId: assignId,
        }))
      );
    }
  }, [selectedProjectDetail, methods, user?.userId, departments, contractType, hasSelectProject]);

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              void handleSubmitPromise(
                e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
              );
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={
              (!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate) &&
              !isDisableExecutionResult
            }
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="mb-2 uppercase md:mb-0"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
                {!isDisableExecutionResult && !isPmoDirector && (
                  <Button
                    text={t('action.sendDirector', { ns: 'common' })}
                    className="uppercase"
                    stylingMode="contained"
                    type="danger"
                    icon="send"
                    onClick={sendNotificationToDirector}
                    disabled={isDisableExecutionResult || !!isSendPmoDirector}
                  />
                )}
              </>
            }
            customElementRight={
              <>
                <DocumentExportPreparation
                  data={contractData}
                  profession={PROFESSIONS.CONTRACT}
                  title={t('content.printContract', { ns: 'common' })}
                />
                <DocumentExportPreparation data={getPrintDataSource()} profession={profession} />
                <ContractTaskManagementSelectFromContractWindow
                  setHasSelectProject={() => setHasSelectProject(true)}
                  model={model}
                />
              </>
            }
          >
            <div className="mt-2">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    {/* Tab: quản lý công việc */}
                    <TabsTrigger value="detail">{t('page.form.tabs.detail')}</TabsTrigger>
                    {/* Tab: hồ sơ đính kèm */}
                    <TabsTrigger value="attachment">{t('page.form.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>

                {/* Tab content: Quản lý công việc */}
                <TabsContent value="detail" className="mt-2">
                  <br></br>
                  {/* Quản lý dự án */}
                  <div className="grid grid-cols-1 gap-y-4 lg:grid-cols-24 ">
                    {/* dòng 1 */}
                    <div className="col-span-1 lg:col-span-24 ">
                      <div className="col-span-1 grid grid-cols-1 gap-x-8 gap-y-4 lg:col-span-24 lg:grid-cols-24">
                        {/* dự án */}
                        <div className="col-span-1 flex items-center lg:col-span-14  2xl:col-span-10">
                          <FormLabel
                            name="projectId"
                            htmlFor="projectId"
                            className="hidden w-[100px] md:block"
                          >
                            {t('fields.projectId')}
                          </FormLabel>
                          <FormField
                            id="projectId"
                            name={'projectId'}
                            className="min-w-0 flex-1 lg:w-[500px]"
                            label={t('fields.projectId')}
                          >
                            {/* <SelectBox
                              items={projects}
                              searchExpr={['name', 'code']}
                              valueExpr="id"
                              onFocusIn={e => {
                                const input = e.element.querySelector(
                                  'input.dx-texteditor-input'
                                ) as HTMLInputElement;
                                if (input) input.select();
                              }}
                              searchEnabled
                              searchMode="contains"
                              focusStateEnabled={false}
                              displayExpr={displayExpr(['name'])}
                              showClearButton
                              onValueChanged={() => {
                                setHasSelectProject(true);
                              }}
                            /> */}
                            <FormCombobox
                              options={projects}
                              queryKey={[QUERIES.PROJECT]}
                              placeholder={`${selectLabel} ${t('fields.projectId')}`}
                              onSelectItem={() => {
                                setHasSelectProject(true);
                              }}
                            />
                          </FormField>
                        </div>

                        <div className="col-span-1 flex items-center  lg:col-span-8 2xl:col-span-6">
                          {/* ngày gửi */}
                          <FormLabel
                            htmlFor="contractTaskManagementTime"
                            className="hidden w-[100px]  md:block"
                          >
                            {t('fields.contractTaskManagementTime')}
                          </FormLabel>
                          <FormField
                            id="contractTaskManagementTime"
                            name={'contractTaskManagementTime'}
                            className="min-w-0 flex-1 md:w-[250px]"
                            type="date"
                            onChange={e => onTimeChange(e.target.value)}
                            label={t('fields.contractTaskManagementTime')}
                          >
                            <DateBox
                              placeholder={`${enterLabel} ${t('fields.contractTaskManagementTime')}`}
                              pickerType="calendar"
                              focusStateEnabled={false}
                            />
                          </FormField>
                        </div>
                        <div className="col-span-6 hidden items-center 2xl:flex"></div>
                      </div>
                    </div>

                    {/* dòng 2 */}
                    <div className="col-span-1 lg:col-span-24">
                      <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                        {/* Mã phiếu*/}
                        <div className="col-span-1 flex items-center  lg:col-span-14 2xl:col-span-10">
                          <FormLabel htmlFor="contractNumber" className="hidden w-[100px] md:block">
                            {t('fields.contractNumber')}
                          </FormLabel>
                          <FormField
                            id="contractNumber"
                            name={'contractNumber'}
                            className="min-w-0 flex-1 lg:w-[250px]"
                            label={t('fields.contractNumber')}
                          >
                            <TextBox
                              placeholder={`${enterLabel} ${t('fields.contractNumber')}`}
                              readOnly={true}
                            />
                          </FormField>
                        </div>

                        {/* Mã phiếu*/}
                        <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
                          <FormLabel htmlFor="code" className="hidden w-[100px]  md:block">
                            {t('fields.code')}
                          </FormLabel>
                          <FormField
                            id="code"
                            name={'code'}
                            className="min-w-0 flex-1 md:w-[250px]"
                            label={t('fields.code')}
                          >
                            <TextBox
                              placeholder={`${enterLabel} ${t('fields.code')}`}
                              readOnly={true}
                            />
                          </FormField>
                        </div>
                      </div>
                    </div>
                    <div className="col-span-1 lg:col-span-24">
                      <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                        {/* Loại hợp đồng */}
                        <div className="col-span-1 flex items-center lg:col-span-14 2xl:col-span-10">
                          <FormLabel
                            name="contractType"
                            htmlFor="contractType"
                            className="hidden w-[100px] md:block"
                          >
                            {t('fields.contractType')}
                          </FormLabel>
                          <FormField
                            id="contractType"
                            name={'contractType'}
                            className="min-w-0 flex-1 md:w-[250px]"
                            label={t('fields.contractType')}
                          >
                            <FormCombobox
                              options={CONTRACT_TYPES}
                              queryKey={['CONTRACT_TYPES']}
                              placeholder={`${selectLabel} ${t('fields.contractType')}`}
                              defaultValue={CONTRACT_TYPES[0].id}
                            />
                          </FormField>
                        </div>
                        <div className="col-span-4 hidden items-center lg:flex"></div>
                      </div>
                    </div>

                    <div className="col-span-1 lg:col-span-24">
                      <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                        {/* Phòng QLDA */}
                        <div className="col-span-1 flex items-center lg:col-span-14 2xl:col-span-10">
                          <FormLabel
                            name="projectManagementDepartmentId"
                            htmlFor="projectManagementDepartmentId"
                            className="hidden w-[100px] md:block"
                          >
                            {t('fields.projectManagementDepartmentId')}
                          </FormLabel>
                          <FormField
                            id="projectManagementDepartmentId"
                            name={'projectManagementDepartmentId'}
                            className="min-w-0 flex-1 md:w-[250px]"
                            label={t('fields.projectManagementDepartmentId')}
                          >
                            <FormCombobox
                              model="department"
                              queryKey={[QUERIES.DEPARTMENT]}
                              placeholder={`${selectLabel} ${t('fields.projectManagementDepartmentId')}`}
                              defaultText={projectManagementDepartmentName}
                            />
                          </FormField>
                        </div>
                        <div className="col-span-4 hidden items-center lg:flex"></div>
                      </div>
                    </div>
                    <div className="col-span-1 lg:col-span-24">
                      <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                        {/* GĐ.QLDA */}
                        <div className="col-span-1 flex items-center lg:col-span-14 2xl:col-span-10">
                          <FormLabel
                            name="projectManagementDirectorId"
                            htmlFor="projectManagementDirectorId"
                            className="hidden w-[100px] md:block"
                          >
                            {t('fields.projectManagementDirectorId')}
                          </FormLabel>
                          <FormField
                            id="projectManagementDirectorId"
                            name={'projectManagementDirectorId'}
                            className="min-w-0 flex-1 md:w-[250px]"
                            label={t('fields.projectManagementDirectorId')}
                          >
                            <FormCombobox
                              model="user"
                              queryKey={[QUERIES.USERS]}
                              placeholder={`${selectLabel} ${t('fields.projectManagementDirectorId')}`}
                              defaultText={projectManagementDirectorName}
                            />
                          </FormField>
                        </div>
                      </div>
                    </div>
                    <div className="col-span-1 lg:col-span-24">
                      <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                        {/* Ghi chú */}
                        <div className="col-span-1 flex items-center  lg:col-span-14 2xl:col-span-10">
                          <FormLabel htmlFor="note" className="hidden w-[100px] md:block">
                            {t('fields.note')}
                          </FormLabel>
                          <FormField
                            id="note"
                            name={'note'}
                            className="min-w-0 flex-1 md:w-[500px]"
                            label={t('fields.note')}
                          >
                            <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                          </FormField>
                        </div>
                      </div>
                    </div>
                  </div>

                  {fieldsProjectDepartment.map((item, index) => {
                    const isAssigner = user?.userId === projectManagementDirectorId;
                    const department = departments.find(
                      item => item.id === projectManagementDepartmentId
                    );
                    const isApprover = user?.userId === department?.departmentHeadId;

                    const isSubmitted = fieldsProjectDepartment[index]?.isSaveTmp === false;
                    const isApproved = fieldsProjectDepartment[index]?.isApprove !== null;

                    const canSubmit = !isSubmitted && isAssigner;
                    const canApprove = isSubmitted && !isApproved && isApprover;

                    return (
                      <>
                        <div
                          className="gird-cols-1 grid gap-x-8 gap-y-4  lg:grid-cols-24"
                          key={item.id}
                        >
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                              {/* Nội dung thay đổi lần: */}
                              <div className="col-span-1 flex items-center  lg:col-span-10 ">
                                <FormLabel htmlFor="revisedNumber" className="w-[500px]">
                                  {index > 0 && (
                                    <>
                                      <br />
                                      <br />
                                      {`${getOpinionTimeText(item.opinionTime, index)}`}
                                    </>
                                  )}
                                </FormLabel>
                              </div>
                              <div hidden>
                                <FormField
                                  id={`contractTaskManagementProjectDepartments.${index}.revisedNumber`}
                                  name={`contractTaskManagementProjectDepartments.${index}.revisedNumber`}
                                  className="min-w-0 flex-1 md:w-[500px]"
                                >
                                  <InputNumber
                                    placeholder={`${enterLabel} ${t('fields.contractTaskManagementProjectDepartments.revisedNumber')}`}
                                    value={index}
                                  />
                                </FormField>
                              </div>
                            </div>
                          </div>

                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                              {/* Nội dung trình*/}
                              <div className="col-span-1 flex items-center  lg:col-span-14 2xl:col-span-10">
                                <FormLabel
                                  htmlFor="submissionContent"
                                  className="hidden w-[100px] md:block"
                                >
                                  {t(
                                    'fields.contractTaskManagementProjectDepartments.submissionContent'
                                  )}
                                </FormLabel>
                                <FormField
                                  id={`contractTaskManagementProjectDepartments.${index}.submissionContent`}
                                  name={`contractTaskManagementProjectDepartments.${index}.submissionContent`}
                                  className="min-w-0 flex-1 lg:w-[500px]"
                                  label={t(
                                    'fields.contractTaskManagementProjectDepartments.submissionContent'
                                  )}
                                >
                                  <TextArea
                                    autoResizeEnabled={true}
                                    placeholder={`${enterLabel} ${t('fields.contractTaskManagementProjectDepartments.submissionContent')}`}
                                    readOnly={!canSubmit}
                                  />
                                </FormField>
                              </div>

                              {/* Ý kiến TP.QLDA*/}
                              {/* Ẩn đi từ lg trở xuống */}
                              <div className="col-span-1 hidden  items-center lg:col-span-10 lg:flex 2xl:col-span-10">
                                <FormLabel
                                  htmlFor="departmentHeadOpinion"
                                  className="hidden w-[100px] md:block"
                                >
                                  {t(
                                    'fields.contractTaskManagementProjectDepartments.departmentHeadOpinion'
                                  )}
                                </FormLabel>
                                <FormField
                                  label={t(
                                    'fields.contractTaskManagementProjectDepartments.departmentHeadOpinion'
                                  )}
                                  id={`contractTaskManagementProjectDepartments.${index}.departmentHeadOpinion`}
                                  name={`contractTaskManagementProjectDepartments.${index}.departmentHeadOpinion`}
                                  className="min-w-0 flex-1 lg:w-[500px]"
                                >
                                  <TextArea
                                    autoResizeEnabled={true}
                                    placeholder={`${enterLabel} ${t('fields.contractTaskManagementProjectDepartments.departmentHeadOpinion')}`}
                                    readOnly={!canApprove}
                                  />
                                </FormField>
                              </div>
                            </div>
                          </div>

                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                              {/* Ý kiến Ý kiến GĐ QLDA*/}
                              <div className="col-span-1 flex items-center  lg:col-span-14 2xl:col-span-10">
                                <FormLabel
                                  htmlFor="directorOpinion"
                                  className="hidden w-[100px] md:block"
                                >
                                  {t(
                                    'fields.contractTaskManagementProjectDepartments.directorOpinion'
                                  )}
                                </FormLabel>
                                <FormField
                                  label={t(
                                    'fields.contractTaskManagementProjectDepartments.directorOpinion'
                                  )}
                                  id={`contractTaskManagementProjectDepartments.${index}.directorOpinion`}
                                  name={`contractTaskManagementProjectDepartments.${index}.directorOpinion`}
                                  className="min-w-0 flex-1 lg:w-[500px]"
                                >
                                  <TextArea
                                    autoResizeEnabled={true}
                                    placeholder={`${enterLabel} ${t('fields.contractTaskManagementProjectDepartments.directorOpinion')}`}
                                    readOnly={!canSubmit}
                                  />
                                </FormField>
                              </div>

                              {/* Ý kiến TP.QLDA*/}
                              {/* Ẩn đi từ lg trở lên */}
                              <div className="col-span-1 flex  items-center lg:col-span-14 lg:hidden 2xl:col-span-10">
                                <FormLabel
                                  htmlFor="departmentHeadOpinion"
                                  className="hidden w-[100px] md:block"
                                >
                                  {t(
                                    'fields.contractTaskManagementProjectDepartments.departmentHeadOpinion'
                                  )}
                                </FormLabel>
                                <FormField
                                  label={t(
                                    'fields.contractTaskManagementProjectDepartments.departmentHeadOpinion'
                                  )}
                                  id={`contractTaskManagementProjectDepartments.${index}.departmentHeadOpinion`}
                                  name={`contractTaskManagementProjectDepartments.${index}.departmentHeadOpinion`}
                                  className="min-w-0 flex-1 lg:w-[500px]"
                                >
                                  <TextArea
                                    autoResizeEnabled={true}
                                    placeholder={`${enterLabel} ${t('fields.contractTaskManagementProjectDepartments.departmentHeadOpinion')}`}
                                    readOnly={!canApprove}
                                  />
                                </FormField>
                              </div>

                              {/* Duyệt */}

                              {isSubmitted && (
                                <div className="  flex flex-col items-end justify-start space-y-4 align-bottom lg:col-span-10">
                                  {isApproved && (
                                    <FormLabel
                                      htmlFor="approveTime"
                                      className="flex items-end justify-end"
                                    >
                                      <>{`${getApproveTimeText(item.approveTime, item.userApproveName)}`}</>
                                    </FormLabel>
                                  )}
                                  {canApprove && (
                                    <div className="space-x-4">
                                      <Button
                                        id={`contractTaskManagementProjectDepartments.${index}.isApprove`}
                                        text="Đồng ý"
                                        className="uppercase"
                                        type="success"
                                        icon="check"
                                        onClick={e => {
                                          // Lấy dữ liệu hiện tại từ form
                                          const currentValues = methods.getValues(
                                            'contractTaskManagementProjectDepartments'
                                          );

                                          // Cập nhật giá trị `isApprove` cho phần tử có index hiện tại và thêm phần tử mới vào mảng
                                          const updatedValues = currentValues.map((item, idx) =>
                                            idx === index
                                              ? {
                                                  ...item,
                                                  isApprove: true,
                                                  userApproveId: user?.userId,
                                                  approveTime: new Date(),
                                                }
                                              : item
                                          );

                                          // Cập nhật lại giá trị vào form một lần duy nhất
                                          methods.setValue(
                                            'contractTaskManagementProjectDepartments',
                                            [...updatedValues]
                                          );

                                          //thêm dòng mới cho phòng tài chính, kế hoạch chất lượng nếu cần
                                          const finance = methods.getValues(
                                            'contractTaskManagementFinanceDepartments'
                                          );
                                          if (
                                            finance.length === 0 ||
                                            finance[finance.length - 1].isApprove
                                          ) {
                                            if (finance.length <= 0) {
                                              methods.setValue(
                                                'contractTaskManagementFinanceDepartments',
                                                [
                                                  ...finance,
                                                  defaultValuesContractTaskManagement
                                                    .contractTaskManagementFinanceDepartments[0],
                                                ]
                                              );
                                            } else {
                                              const lastOne = finance[finance.length - 1];
                                              methods.setValue(
                                                'contractTaskManagementFinanceDepartments',
                                                [
                                                  ...finance,
                                                  {
                                                    ...defaultValuesContractTaskManagement
                                                      .contractTaskManagementFinanceDepartments[0],
                                                    receivedTime: lastOne.receivedTime,
                                                    completionTime: lastOne.completionTime,
                                                    assignedUserId: lastOne.assignedUserId,
                                                    opinion: lastOne.opinion,
                                                    actionType: lastOne.actionType,
                                                  },
                                                ]
                                              );
                                            }
                                          }
                                          const planning = methods.getValues(
                                            'contractTaskManagementPlanningDepartments'
                                          );
                                          if (
                                            planning.length === 0 ||
                                            planning[planning.length - 1].isApprove
                                          ) {
                                            if (planning.length <= 0) {
                                              methods.setValue(
                                                'contractTaskManagementPlanningDepartments',
                                                [
                                                  ...planning,
                                                  defaultValuesContractTaskManagement
                                                    .contractTaskManagementPlanningDepartments[0],
                                                ]
                                              );
                                            } else {
                                              const lastOne = planning[planning.length - 1];
                                              methods.setValue(
                                                'contractTaskManagementPlanningDepartments',
                                                [
                                                  ...planning,
                                                  {
                                                    ...defaultValuesContractTaskManagement
                                                      .contractTaskManagementPlanningDepartments[0],
                                                    receivedTime: lastOne.receivedTime,
                                                    completionTime: lastOne.completionTime,
                                                    assignedUserId: lastOne.assignedUserId,
                                                    opinion: lastOne.opinion,
                                                    actionType: lastOne.actionType,
                                                  },
                                                ]
                                              );
                                            }
                                          }

                                          // Gửi thông báo
                                          // Tài chính: trưởng phòng
                                          // Kế hoạch chất lượng:
                                          // 1. hợp đồng tư vấn: nhân viên nhận hợp đồng tư vấn và phó phòng
                                          // 2. hợp đồng khác: TP KH-CL + cán bộ thực hiện (nếu có)
                                          void handleSubmitPromise(
                                            e.event
                                              ?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                                          ).then(data => {
                                            const newData = data as ContractTaskManagementResponse;
                                            const receivers: (number | null | undefined)[] = [];

                                            // 1. hợp đồng tư vấn: nhân viên nhận hợp đồng tư vấn và phó phòng
                                            if (newData.contractType === 1) {
                                              const departmentReceivers = notifyDepartments
                                                .flatMap(item => [
                                                  item.consultingContractOfficerId,
                                                  item.departmentDeputyId,
                                                ])
                                                .filter(id => id);
                                              receivers.push(...departmentReceivers);
                                            }
                                            // 2. hợp đồng khác: TP TCTH/KH-CL + cán bộ thực hiện (nếu có)
                                            else {
                                              const otherReceivers = [
                                                newData.departmentFinanceHeadId,
                                                newData.departmentPlanningHeadId,
                                                newData.contractTaskManagementPlanningDepartments?.at(
                                                  -1
                                                )?.assignedUserId,
                                                newData.contractTaskManagementFinanceDepartments?.at(
                                                  -1
                                                )?.assignedUserId,
                                              ].filter((id): id is number => !!id);
                                              receivers.push(...otherReceivers);
                                            }

                                            if (receivers.length) {
                                              sendNotify({
                                                title: t(notifyNew, {
                                                  ns: 'sendNotification',
                                                }),
                                                content: getContent(
                                                  newData.contractTaskManagementProjectDepartments?.at(
                                                    -1
                                                  )?.departmentHeadOpinion || ''
                                                ),
                                                typeNotification: path,
                                                refId: Number(newData.id) || null,
                                                userIds: receivers,
                                              });
                                            }
                                          });
                                        }}
                                      />
                                      <Button
                                        id={`contractTaskManagementProjectDepartments.${index}.isNotApprove`}
                                        text="Từ chối"
                                        className="uppercase"
                                        type="danger"
                                        icon="close"
                                        onClick={e => {
                                          // Lấy dữ liệu hiện tại từ form
                                          const currentValues = methods.getValues(
                                            'contractTaskManagementProjectDepartments'
                                          );

                                          // Cập nhật giá trị `isApprove` cho phần tử có index hiện tại và thêm phần tử mới vào mảng
                                          const updatedValues = currentValues.map((item, idx) =>
                                            idx === index
                                              ? {
                                                  ...item,
                                                  isApprove: false,
                                                  userApproveId: user?.userId,
                                                  approveTime: new Date(),
                                                }
                                              : item
                                          );

                                          // Thêm phần tử mới vào mảng sau khi cập nhật giá trị `isApprove`
                                          const newDepartment = {
                                            ...defaultValuesContractTaskManagement
                                              .contractTaskManagementProjectDepartments[0],
                                            submissionContent:
                                              currentValues[index].submissionContent,
                                            directorOpinion: currentValues[index].directorOpinion,
                                            id: -getRandomNumber(),
                                          };

                                          // Cập nhật lại giá trị vào form một lần duy nhất
                                          methods.setValue(
                                            'contractTaskManagementProjectDepartments',
                                            [...updatedValues, newDepartment]
                                          );

                                          // Gửi thông báo cho Giám đốc quản lý dự án
                                          void handleSubmitPromise(
                                            e.event
                                              ?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                                          ).then(data => {
                                            const newData = data as ContractTaskManagementResponse;
                                            if (newData.projectManagementDirectorId) {
                                              sendNotify({
                                                title: t(notifyRechecked, {
                                                  ns: 'sendNotification',
                                                }),
                                                content: getContent(
                                                  newData.contractTaskManagementProjectDepartments?.at(
                                                    -1
                                                  )?.departmentHeadOpinion || ''
                                                ),
                                                typeNotification: path,
                                                refId: Number(newData.id) || null,
                                                userIds: [newData.projectManagementDirectorId],
                                              });
                                            }
                                          });
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>

                          {canSubmit && (
                            <div className="col-span-1 lg:col-span-24">
                              <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                                {/* Lưu và lưu tạm */}
                                <div className="col-span-1 flex items-center justify-end space-x-4 lg:col-span-14 2xl:col-span-10">
                                  <Button
                                    id={`contractTaskManagementProjectDepartments.${index}.isApprove`}
                                    text="Lưu tạm"
                                    className="uppercase"
                                    type="default"
                                    icon="save"
                                    stylingMode="outlined"
                                    onClick={e => {
                                      void handleSubmitPromise(
                                        e.event
                                          ?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                                      );
                                    }}
                                  />
                                  <Button
                                    id={`contractTaskManagementProjectDepartments.${index}.isNotApprove`}
                                    text="Lưu lại"
                                    className="uppercase"
                                    type="success"
                                    icon="save"
                                    onClick={e => {
                                      // Lấy dữ liệu hiện tại từ form
                                      const currentValues = methods.getValues(
                                        'contractTaskManagementProjectDepartments'
                                      );

                                      // Cập nhật giá trị `isSaveTmp` cho phần tử có index hiện tại
                                      const updatedValues = currentValues.map((item, idx) =>
                                        idx === index
                                          ? { ...item, isSaveTmp: false, revisedNumber: index }
                                          : item
                                      );

                                      // Cập nhật lại giá trị vào form
                                      methods.setValue(
                                        'contractTaskManagementProjectDepartments',
                                        updatedValues
                                      );
                                      // Gửi thông báo cho Trưởng phòng quản lý dự án
                                      void handleSubmitPromise(
                                        e.event
                                          ?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                                      ).then(data => {
                                        const newData = data as ContractTaskManagementResponse;

                                        if (newData.departmentProjectHeadId) {
                                          sendNotify({
                                            title: t(
                                              `${editId === 'new' ? notifyNew : notifyUpdated}`,
                                              {
                                                ns: 'sendNotification',
                                              }
                                            ),
                                            content: getContent(
                                              newData.contractTaskManagementProjectDepartments?.at(
                                                -1
                                              )?.directorOpinion || ''
                                            ),
                                            typeNotification: path,
                                            refId: Number(newData.id) || null,
                                            userIds: [newData.departmentProjectHeadId],
                                          });
                                        }
                                      });
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    );
                  })}

                  {/* Phòng tài chính tổng hợp */}
                  {isContractTask && (
                    <div className="mt-8">
                      <Tabs defaultValue="financeDepartments">
                        <div className="w-full border-b border-[#e0e0e0]">
                          <TabsList>
                            <TabsTrigger value="financeDepartments">
                              {t('page.tabs.financeDepartments')}
                            </TabsTrigger>
                          </TabsList>
                        </div>
                        <TabsContent value="financeDepartments" className="mt-2">
                          {/*Phòng tài chính tổng hợp:*/}
                          {fieldsFinanceDepartment.map((item, index) => {
                            const isAssigner = user?.userId === item.assignedUserId;
                            const department = departments.find(item => item.id === 4);
                            const isApprover = user?.userId === department?.departmentHeadId;

                            const isSubmitted = item.isSaveTmp === false;
                            const isApprovedOrRejected = item.isApprove !== null;

                            const isProjectDepartmentApproved =
                              fieldsProjectDepartment[fieldsProjectDepartment.length - 1]
                                .isApprove === true;

                            const canSave =
                              isProjectDepartmentApproved &&
                              !isApprovedOrRejected &&
                              !isSubmitted &&
                              isAssigner;
                            const canApproveOrReject =
                              isProjectDepartmentApproved &&
                              isSubmitted &&
                              !isApprovedOrRejected &&
                              isApprover;

                            return (
                              <ContractTaskManagementFormDetail
                                key={item.id}
                                model={model}
                                index={index}
                                item={item}
                                canApproveOrReject={canApproveOrReject}
                                canSave={canSave}
                                isApprover={isApprover}
                                isSubmitted={isSubmitted}
                                isApprovedOrRejected={isApprovedOrRejected}
                                fieldsDepartmentDetails={fieldsFinanceDepartment}
                                getValues={methods.getValues}
                                setValue={methods.setValue}
                                handleSubmitPromise={handleSubmitPromise}
                                listName="contractTaskManagementFinanceDepartments"
                              />
                            );
                          })}
                        </TabsContent>
                      </Tabs>
                    </div>
                  )}

                  {/* Kế hoạch chất lượng */}
                  <div className="mt-8">
                    <Tabs defaultValue="planningDepartments">
                      <div className="w-full border-b border-[#e0e0e0]">
                        <TabsList>
                          <TabsTrigger value="planningDepartments">
                            {t('page.tabs.planningDepartments')}
                          </TabsTrigger>
                        </TabsList>
                      </div>
                      <TabsContent value="planningDepartments" className="mt-2">
                        {/* Kế hoạch chất lượng */}
                        {fieldsPlanningDepartment?.map((item, index) => {
                          const isAssigner = user?.userId === item.assignedUserId;
                          const department = departments.find(item => item.id === 3);
                          const isApprover = user?.userId === department?.departmentHeadId;

                          const isSubmitted = item.isSaveTmp === false;
                          const isApprovedOrRejected = item.isApprove !== null;
                          const isProjectDepartmentApproved =
                            fieldsProjectDepartment[fieldsProjectDepartment.length - 1]
                              .isApprove === true;

                          const canSave =
                            isProjectDepartmentApproved &&
                            !isApprovedOrRejected &&
                            !isSubmitted &&
                            isAssigner;
                          const canApproveOrReject =
                            isProjectDepartmentApproved &&
                            isSubmitted &&
                            !isApprovedOrRejected &&
                            isApprover;

                          return (
                            <ContractTaskManagementFormDetail
                              key={item.id}
                              model={model}
                              index={index}
                              item={item}
                              canApproveOrReject={canApproveOrReject}
                              canSave={canSave}
                              isApprover={isApprover}
                              isSubmitted={isSubmitted}
                              isApprovedOrRejected={isApprovedOrRejected}
                              fieldsDepartmentDetails={fieldsPlanningDepartment}
                              getValues={methods.getValues}
                              setValue={methods.setValue}
                              handleSubmitPromise={handleSubmitPromise}
                              listName="contractTaskManagementPlanningDepartments"
                            />
                          );
                        })}
                      </TabsContent>
                    </Tabs>
                  </div>

                  {/* Kết quả thực hiện */}
                  <div className="mt-8">
                    <Tabs defaultValue="executionResult">
                      <div className="w-full border-b border-[#e0e0e0]">
                        <TabsList>
                          <TabsTrigger value="executionResult">
                            {t('page.tabs.executionResult')}
                          </TabsTrigger>
                        </TabsList>
                      </div>
                      <br></br>
                      <TabsContent value="executionResult" className="mt-2">
                        <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                              {/* Ngày hoàn thành*/}
                              <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
                                <FormLabel
                                  htmlFor="completionTime"
                                  className="hidden w-[100px] md:block"
                                >
                                  {t('fields.completionTime')}
                                </FormLabel>
                                <FormField
                                  label={t('fields.completionTime')}
                                  id="completionTime"
                                  name={'completionTime'}
                                  className="min-w-0 flex-1 md:w-[250px]"
                                  type="date"
                                >
                                  <DateBox
                                    placeholder={`${enterLabel} ${t('fields.completionTime')}`}
                                    disabled={isDisableExecutionResult}
                                    pickerType="calendar"
                                    focusStateEnabled={false}
                                  />
                                </FormField>
                              </div>
                            </div>
                          </div>
                          {/* Dự thảo BB hoàn thiện/thương thảo HĐ */}
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                              <div className="col-span-1 flex items-center lg:col-span-8 ">
                                <FormLabel className="w-[280px]">
                                  {t('fields.isDraftCompletion')}
                                </FormLabel>
                                <FormField
                                  name="isDraftCompletion"
                                  className="ml-[115px] md:ml-[115px]"
                                  type="checkbox"
                                >
                                  <CheckBox disabled={isDisableExecutionResult} />
                                </FormField>
                              </div>
                            </div>
                          </div>
                          {/* Dự thảo hợp đồng */}
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                              <div className="col-span-1 flex items-center lg:col-span-8 ">
                                <FormLabel className="w-[280px]">
                                  {t('fields.isDraftContract')}
                                </FormLabel>
                                <FormField
                                  name="isDraftContract"
                                  className="ml-[115px] md:ml-[115px]"
                                  type="checkbox"
                                >
                                  <CheckBox disabled={isDisableExecutionResult} />
                                </FormField>
                              </div>
                            </div>
                          </div>
                          {/* Dự thảo phuj luc hợp đồng */}
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                              <div className="col-span-1 flex items-center lg:col-span-8 ">
                                <FormLabel className="w-[280px]">
                                  {t('fields.isDraftSubContract')}
                                </FormLabel>
                                <FormField
                                  name="isDraftSubContract"
                                  className="ml-[115px] md:ml-[115px]"
                                  type="checkbox"
                                >
                                  <CheckBox disabled={isDisableExecutionResult} />
                                </FormField>
                              </div>
                            </div>
                          </div>
                          {/* hồ sơ đủ điều kiện ký kết */}
                          <div className="col-span-1 lg:col-span-24">
                            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
                              <div className="col-span-1 flex items-center lg:col-span-8 ">
                                <FormRadioGroup
                                  items={eligibleForSignings}
                                  valueExpr="id"
                                  displayExpr="name"
                                  value={defaultEligibleForSigningCheck}
                                  disabled={isDisableExecutionResult}
                                  itemRender={renderRadioGroupItem}
                                  onChange={onValueChanged}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>

                    {/* Phụ lục hồ sơ đính kè */}
                    <div className="mt-8">
                      <Tabs defaultValue="contractTaskManagementDocuments">
                        <div className="w-full border-b border-[#e0e0e0]">
                          <TabsList>
                            <TabsTrigger value="contractTaskManagementDocuments">
                              {t('page.tabs.contractTaskManagementDocuments')}
                            </TabsTrigger>
                          </TabsList>
                        </div>
                        <TabsContent value="contractTaskManagementDocuments" className="mt-2">
                          <DataTable
                            tableId={tableKey}
                            sortColumn="components"
                            showPagination={false}
                            columns={contractTaskManagementDocumentColumns}
                            syncQueryParams={false}
                            editableData={contractTaskManagementDocuments}
                            setEditableData={editedData => {
                              methods.setValue('contractTaskManagementDocuments', editedData);
                            }}
                          />
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>

                  {/* GĐ ban đồng ý/ từ chối */}
                  <div className="mt-8">
                    <ContractTaskManagementDirectorApproveForm
                      model={model}
                      getValues={methods.getValues}
                      setValue={methods.setValue}
                      handleSubmitPromise={handleSubmitPromise}
                      isDisableExecutionResult={isDisableExecutionResult}
                      isPmoDirector={isPmoDirector}
                    />
                  </div>
                </TabsContent>

                {/* Tab content: Hồ sơ đính kèm */}
                <TabsContent value="attachment" className="mt-2">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="contract-task-management"
                    profession={profession}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
