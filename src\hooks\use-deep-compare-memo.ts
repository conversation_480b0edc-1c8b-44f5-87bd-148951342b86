import { useMemo, useRef } from 'react';
import isEqual from 'lodash/isEqual';

/**
 * Hook tương tự như useMemo nhưng thực hiện so sánh sâu (deep comparison) cho dependencies
 * Hữu ích khi dependencies là object hoặc array phức tạp
 * @template T - Kiểu dữ liệu trả về từ factory function
 * @param factory - Hàm tính toán giá trị cần memoize
 * @param deps - Mảng dependencies cần theo dõi
 * @returns Giá trị đã được memoize
 */
export function useDeepCompareMemo<T>(factory: () => T, deps: React.DependencyList): T {
  // Lưu trữ dependencies từ lần render trước
  const previousDeps = useRef<React.DependencyList>([]);

  // So sánh sâu dependencies hiện tại với dependencies từ lần render trước
  // Nếu khác nhau, cập nhật previousDeps
  if (!isEqual(previousDeps.current, deps)) {
    previousDeps.current = deps;
  }

  // Sử dụng useMemo với dependencies là kết quả của việc so sánh sâu
  // Chỉ tính toán lại khi previousDeps.current thay đổi
  return useMemo(factory, [factory, previousDeps.current]);
}
