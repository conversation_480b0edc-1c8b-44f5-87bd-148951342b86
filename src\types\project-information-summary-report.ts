import { z } from 'zod';

export const projectInformationSummaryReportSchema = z.object({
  id: z.number(),
  ordinalNumber: z.number(),
  projectId: z.number().nullable(),
  projectName: z.string().nullable().optional(),
  projectLocation: z.string().nullable().optional(),
  totalInvestment: z.number().nullable().optional(),
  constructionDate: z.string().nullable().optional(),
  yearlyPlan: z.number().nullable().optional(),
  progress: z.string().nullable().optional(),
  cumulativeDisbursementYearToDate: z.number().nullable().optional(),
  disbursementRateYearToDate: z.number().nullable().optional(),
});

export type ProjectInformationSummaryReport = z.infer<typeof projectInformationSummaryReportSchema>;
