/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DevexDataGrid } from '@/components/devex-data-grid';
import { getIndexedList } from '@/components/devex-tree-list';
import { PageLayout } from '@/components/page-layout';
import { Label } from '@/components/ui/label';
import { QUERIES, TABLES, viewLabel } from '@/constant';
import { toLocaleDate } from '@/lib/date';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, getSumsFromArray, hash } from '@/lib/utils';
import { createQueryReportWithSummary } from '@/services';
import { ProjectDeploymentStatusReport, ProjectDeploymentStatusReportSummary } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON>, DateBox } from 'devextreme-react';
import {
  Column,
  Export,
  FilterRow,
  HeaderFilter,
  IColumnProps,
  Pager,
  Paging, 
} from 'devextreme-react/data-grid';
import { dxDataGridColumn } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const t = translationWithNamespace('projectDeploymentStatusReport');
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

type BudgetColumn = dxDataGridColumn &
  IColumnProps & {
    id: number;
    parentId: number | null;
    columns: BudgetColumn[];
  };

const ignoredFieldRows = [
  'budgetFundId',
  'budgetFundCode',
  'budgetFundName',
  'parentId',
  'ordinalNumber',
];

const displayItems = [
  'TỔNG CỘNG',
  'Tổng số DA (1 bước)',
  '+ Tổng số DA được phê duyệt DA (Báo cáo kinh tế kỹ thuật)',
  'Tổng số DA (2 bước)',
  '+ Tổng số DA được phê duyệt DA',
  '+ Tổng số DA được duyệt thiết kế XD triển khai sau thiết kế cơ sở',
];

const getNestedBudgetColumns = (
  data: ProjectDeploymentStatusReport[]
): [BudgetColumn[], Map<number, BudgetColumn>] => {
  const map = new Map<number, BudgetColumn>();

  for (const item of data) {
    if (item.budgetFundId && !map.has(item.budgetFundId)) {
      map.set(item.budgetFundId, {
        id: item.budgetFundId,
        dataField: item.budgetFundId?.toString(),
        caption: item.budgetFundName || '',
        parentId: item.parentId,
        alignment: 'center',
        columns: [],
      });
    }
  }

  const columns: BudgetColumn[] = [
    {
      id: -getRandomNumber(),
      dataField: 'name',
      caption: t('fields.name').toUpperCase(),
      parentId: null,
      cellRender: (props: { text: string }) => {
        if (displayItems.includes(props.text.trim())) {
          return <div className="font-bold">{props.text}</div>;
        }

        return props.text;
      },
      columns: [],
    },
  ];

  for (const item of map.values()) {
    if (item.parentId) {
      const parent = map.get(item.parentId);
      if (parent) parent.columns.push(item);
    } else {
      columns.push({
        ...item,
        dataField: item.caption,
      });
    }
  }

  for (const item of map.values()) {
    if (item.columns.length > 0) {
      const totalColumn: BudgetColumn = {
        id: -getRandomNumber(),
        dataField: item.id.toString(),
        caption: t('fields.total'),
        parentId: item.id,
        alignment: 'center',
        columns: [],
      };
      item.columns.unshift(totalColumn);
      map.set(totalColumn.id, totalColumn);
    }
  }

  return [columns, map];
};
/// START FIX kietPT
/// Function sum Bfs bị lỗi không đúng fix theo code mới

// const sumBfs = (details: ProjectDeploymentStatusReport[] | undefined) => {
//   if (!details || details.length === 0) return [];

//   const items = details.map(item => ({ ...item }));
//   const map = new Map<number, (typeof items)[0]>();
//   const queue: typeof items = [];
//   const visited = new Set();
//   const rowFields = Object.keys(details[0]).filter(field => {
//     return !ignoredFieldRows.includes(field);
//   }) as (keyof (typeof details)[0])[];

//   for (const item of items) {
//     if (item.budgetFundId) {
//       map.set(item.budgetFundId, item);
//     }

//     if (item.parentId && rowFields.some(column => !!item[column])) {
//       queue.push(item);
//     }
//   }

//   while (queue.length) {
//     const node = queue.shift();
//     if (node) {
//       const key = node?.budgetFundId;

//       if (!key) continue;

//       visited.add(key);

//       const sum: Record<string, number> = {};
//       for (const item of map.values()) {
//         if (item.parentId === node.budgetFundId) {
//           for (const field of rowFields) {
//             sum[field] = Number(sum[field] || 0) + Number(item[field] || 0);
//           }
//         }
//       }

//       const { sum: sumFields } = rowFields.reduce(
//         (acc, column) => {
//           const currentValue = Number(node[column] || 0);
//           const result = currentValue + (sum[column] || 0);

//           acc.sum[column] = result;

//           return acc;
//         },
//         { sum: {} } as { sum: Record<string, number> }
//       );

//       const newNode = { ...node, ...sumFields };

//       map.set(key, newNode);

//       if (!node?.parentId) continue;

//       if (map.has(node.parentId)) {
//         const parent = map.get(node.parentId)!;
//         if (!visited.has(node.parentId)) queue.push(parent);
//       }
//     }
//   }

//   return Array.from(map.values());
// };

///END FIX kietPT

const sumBfs = (details: ProjectDeploymentStatusReport[] | undefined) => {
  if (!details || details.length === 0) return [];

  const items = details.map(item => ({ ...item }));
  const map = new Map<number, ProjectDeploymentStatusReport>();
  const childrenMap = new Map<number, ProjectDeploymentStatusReport[]>();

  const ignoredFieldRows = [
    'ordinalNumber',
    'budgetFundId',
    'budgetFundCode',
    'budgetFundName',
    'parentId',
  ];

  const rowFields = Object.keys(details[0]).filter(
    field => !ignoredFieldRows.includes(field)
  ) as (keyof ProjectDeploymentStatusReport)[];

  for (const item of items) {
    map.set(item.budgetFundId!, item);
    if (item.parentId !== null) {
      if (!childrenMap.has(item.parentId)) {
        childrenMap.set(item.parentId, []);
      }
      childrenMap.get(item.parentId)!.push(item);
    }
  }

  const visited = new Set<number>();

  const sumFields = (node: ProjectDeploymentStatusReport): ProjectDeploymentStatusReport => {
    if (visited.has(node.budgetFundId!)) return map.get(node.budgetFundId!)!;
    visited.add(node.budgetFundId!);

    const children = childrenMap.get(node.budgetFundId!) || [];
    const result: ProjectDeploymentStatusReport = { ...node };

    for (const child of children) {
      const summedChild = sumFields(child);
      for (const field of rowFields) {
        (result as any)[field] = Number(result[field] || 0) + Number(summedChild[field] || 0);
      }
    }

    map.set(node.budgetFundId!, result);
    return result;
  };

  // Tính tổng từ tất cả các node gốc (parentId === null)
  for (const item of items) {
    if (item.parentId === null) {
      sumFields(item);
    }
  }

  return Array.from(map.values());
};

const transformColumnsToRows = (
  data: ProjectDeploymentStatusReport[],
  columnMap: Map<number, BudgetColumn>
) => {
  if (data.length === 0) return [];

  const [firstItem] = data;

  const columnFields = Array.from(columnMap.values()).filter(column => column.columns.length === 0);

  const rowFields = Object.keys(firstItem).filter(field => {
    return !ignoredFieldRows.includes(field);
  }) as (keyof typeof firstItem)[];

  const dataMap = hash(data, 'budgetFundId');
  if (dataMap === null) return [];

  const result = [];
  for (const rowField of rowFields) {
    const item: Record<string, any> = {
      name: t(`fields.${rowField}`),
    };

    for (const { dataField } of columnFields) {
      if (dataField) {
        item[dataField] = dataMap[dataField][rowField];
      }
    }

    result.push(item);
  }

  const sumArr = getSumsFromArray(result, columnFields.map(item => item.dataField) as string[]);
  const sumFields = columnFields.reduce(
    (acc, column, index) => {
      acc[column.dataField!] = sumArr[index];
      return acc;
    },
    {} as Record<string, number>
  );

  result.unshift({ name: t('fields.sum').toUpperCase(), ...sumFields });

  return result;
};

export const ProjectDeploymentStatusReportDataTable = () => {
  const { t } = useTranslation('projectDeploymentStatusReport');

  const [budgetYear, setBudgetYear] = useState<Date>(new Date());

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.PROJECT_DEPLOYMENT_STATUS_REPORT],
    queryFn: () => {
      return createQueryReportWithSummary<
        ProjectDeploymentStatusReport,
        ProjectDeploymentStatusReportSummary
      >('project-deployment-status-report')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'BudgetFundId',
        sortOrder: 1,
        isPage: true,
        filterColumn: [],
        objParam: {
          budgetYear: toLocaleDate(budgetYear),
        },
      });
    },
  });

  const { items } = data || { items: [] };

  const [columns, dataSource] = useMemo(() => {
    const summedItems = sumBfs(items);
    const indexedList = getIndexedList(
      summedItems,
      'budgetFundName',
      'budgetFundName',
      'budgetFundId'
    );

    const [columns, columnMap] = getNestedBudgetColumns(indexedList);
    const details = transformColumnsToRows(summedItems, columnMap);
    const dataSource = details.map((item: (typeof details)[0]) => {
      const columnsObj = Array.from(columnMap.keys()).filter(item => item > 0);
      if (item.name === 'TỔNG CỘNG') {
        const objTem: Record<string, any> = item;
        const objTem5: Record<string, any> = details[5];
        const objTem9: Record<string, any> = details[9];
        for (const field of columnsObj) {
          objTem[field] = Number(objTem5[field]) + Number(objTem9[field]);
        }
      }
      return {
        ...item,
      };
    });

    return [columns, dataSource];
  }, [items]);

  const renderColumns = (columns: BudgetColumn[]) => {
    return columns.map(column => {
      const { dataField, caption, ...props } = column;

      return (
        <Column key={dataField} dataField={dataField} caption={caption} {...props}>
          {renderColumns(column.columns)}
        </Column>
      );
    });
  };

  return (
    <PageLayout header={<>{t('page.header')}</>}>
      <div
        aria-description="project-data-filter"
        className="flex flex-col gap-x-4 gap-y-4 md:flex-row"
      >
        <div className="flex flex-col gap-x-2 md:flex-row md:items-center">
          <Label className="shrink-0 text-nowrap" htmlFor="time">
            {t('page.budgetYear')}
          </Label>
          <DateBox
            type="date"
            calendarOptions={{
              maxZoomLevel: 'decade',
              minZoomLevel: 'decade',
            }}
            value={budgetYear}
            displayFormat={'year'}
            onValueChange={value => {
              setBudgetYear(value as Date);
            }}
            pickerType="calendar"
            focusStateEnabled={false}
          />
        </div>
        <Button
          text={viewLabel}
          className="w-full md:w-fit"
          stylingMode="contained"
          type="default"
          icon="search"
          onClick={() => {
            callbackWithTimeout(refetch);
          }}
        />
      </div>
      <DevexDataGrid
        id={TABLES.PROJECT_DEPLOYMENT_STATUS_REPORT}
        dataSource={dataSource}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        keyExpr={'name'}
        onContentReady={() => {
          const footer = document?.querySelector?.('.dx-datagrid-total-footer');
          if (!footer) return;
          document?.querySelector?.('.dx-data grid-rowsview')?.before(footer);
        }}
        className="column-header-wrap"
        editing={{
          allowUpdating: false,
          allowAdding: false,
          allowDeleting: false,
        }}
        height={`${dataSource.length > 15 ? 'calc(100% - 50px)' : 'auto'}`}
      >
        <Export enabled={true} />
        <FilterRow visible={false} showOperationChooser />
        <HeaderFilter visible={false} />
        <Paging enabled defaultPageSize={0} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={[5, 10, 50, 100, 'all']}
        />
        {renderColumns(columns)}
      </DevexDataGrid>
    </PageLayout>
  );
};
