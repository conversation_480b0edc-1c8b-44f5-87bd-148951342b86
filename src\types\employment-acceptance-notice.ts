import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { recordAttachmentSchema } from './records-attachment';

const requireSpendingCommitmentText = requiredTextWithNamespace('employmentAcceptanceNotice');

export const employmentAcceptanceNoticeSchema = z.object({
  branchId: z.number().nullable(),
  id: z.number(),
  code: z.string().nullable().optional(),
  projectId: z
    .number({
      required_error: requireSpendingCommitmentText('projectId', 'select'),
      invalid_type_error: requireSpendingCommitmentText('projectId', 'select'),
    })
    .min(1, requireSpendingCommitmentText('projectId', 'select')), //dự án
  userCreatedId: z.number().nullable(),
  userCreatedName: z.string().nullable().optional(),
  employmentAcceptanceNoticeTime: z.coerce.date().nullable().optional(), // <PERSON><PERSON>y lập
  approvalNumber: z.string().nullable().optional(), // Số quyết định
  approvalDate: z.coerce.date().nullable().optional(), // Ngày quyết định
  approvalContent: z.string().nullable().optional(),
  note: z.string().nullable().optional(),
  ids: z.number().nullable(),
  sort: z.string().nullable().optional(),
  finalizationDossierDeadline: z.coerce.date().nullable().optional(),
  setupExpirationDate: z.number().nullable().optional(), // Số tháng hết hạn lập hồ sơ quyết toán
  warrantyExpirationDate: z.coerce.date().nullable().optional(), // Ngày hết hạn bảo hành
  itemsRecordManagement: z.array(recordAttachmentSchema),
});

export type EmploymentAcceptanceNotice = z.infer<typeof employmentAcceptanceNoticeSchema>;

export const defaultValuesEmploymentAcceptanceNotice: EmploymentAcceptanceNotice = {
  branchId: null, // mã chi nhánh
  id: 0, // Khóa chính
  employmentAcceptanceNoticeTime: new Date(), // Ngày lập
  userCreatedId: null, // Người lập
  code: '', // Mã phiếu
  projectId: 0, // Dự án
  setupExpirationDate: 0, // Số tháng hết hạn lập hồ sơ quyết toán
  note: '', // Ghi chú
  ids: null, // ids
  sort: '',
  approvalNumber: '', // Số quyết định
  approvalDate: new Date(), // Ngày quyết định
  warrantyExpirationDate: null, // Ngày hết hạn bảo hành
  approvalContent: '', // Nội dung quyết định
  itemsRecordManagement: [], // Hồ sơ đính kèm
};
