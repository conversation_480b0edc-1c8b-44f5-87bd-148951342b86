/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { DisbursementProgressSummary } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('disbursementProgressSummary');
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const DisbursementProgressSummaryDataTable = ({
  path,
  permission,
}: {
  path: string;
  permission: number;
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('disbursementProgressSummary');

  const role = usePermission(permission);

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const getTargetAlias = (target: DisbursementProgressSummary | undefined) => {
    if (!target) {
      return '';
    }
    return target.note!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<DisbursementProgressSummary, PeriodFilter>({
    queryRangeName: 'disbursementProgressSummaryTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<DisbursementProgressSummary>('disbursement-progress-summary'),
    deleteKey: [MUTATE.DELETE_DISBURSEMENT_PROGRESS_SUMMARY],
    invalidateKey: [QUERIES.DISBURSEMENT_PROGRESS_SUMMARY],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.DISBURSEMENT_PROGRESS_SUMMARY],
    queryFn: () => {
      return createQueryPaginationFn<DisbursementProgressSummary>('disbursement-progress-summary')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'DisbursementProgressSummaryTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<DisbursementProgressSummary>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<DisbursementProgressSummary>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'disbursementProgressSummaryTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.DISBURSEMENT_PROGRESS_SUMMARY}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="budgetYear"
          caption={t('fields.budgetYear')}
          dataType="date"
          alignment="left"
          format={'year'}
        />
        <Column
          dataField="disbursementProgressSummaryTime"
          caption={t('fields.disbursementProgressSummaryTime')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="note" caption={t('fields.note')} alignment="left" />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="disbursementProgressSummary"
      />
    </PageLayout>
  );
};
