import {
  DataTable,
  DataTableRowActions,
  EditableDropdownCell,
  EditableFileCell,
  EditableInputCell,
} from '@/components/data-table';
import { RecordFilesAttachButton } from '@/components/records-attachment';
import { addLabel, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useBoolean, useEntity, usePermission } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import { FileTypeForm } from '@/pages/file-type';
import {
  defaultValuesRecordAttachment,
  IUserPermission,
  RecordAttachment,
  RecordAttachmentFile,
} from '@/types';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from 'devextreme-react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { BasicDialog } from '@/components/basic-dialog';

type RecordFilesEditableTable = {
  role?: IUserPermission;
  folder?: string;
  setIsLoading: (is: boolean) => void;
};

const [defaultRow] = defaultValuesRecordAttachment.itemFile;

export const RecordFilesEditableTableFileSystem = ({
  role,
  folder,
  setIsLoading,
}: RecordFilesEditableTable) => {
  const { t } = useTranslation('recordsAttachment');

  // const isMobile = useMediaQuery('(max-width: 768px)');

  const { control, setValue } = useFormContext<RecordAttachment>();
  const [typeUpload, editableData] = useWatch({
    control,
    name: [`typeUpload`, `itemFile`],
  });

  const setEditableData = (editableData: RecordAttachmentFile[]) => {
    setValue('itemFile', editableData);
  };

  const fileTypeRole = usePermission(PERMISSIONS.FILE_TYPE);

  const { state: isAddNewFileTypeFormOpen, toggle: toggleAddNewFileTypeForm } = useBoolean(false);
  const { fetch: fetchFileTypes } = useEntity({
    model: 'file-type',
    queryKey: [QUERIES.FILE_TYPE],
  });

  const columns: ColumnDef<RecordAttachmentFile>[] = [
    {
      id: 'typeFileId',
      accessorKey: 'typeFileId',
      header: t('fields.itemFile.typeFileId'),
      cell: props => (
        <EditableDropdownCell {...props} queryKey={[QUERIES.FILE_TYPE]} model="file-type" />
      ),
      size: 100,
    },
    {
      id: 'name',
      accessorKey: 'name',
      header: t('fields.itemFile.name'),
      cell: props => <EditableInputCell {...props} />,
      size: 150,
    },
    {
      id: 'fileName',
      accessorKey: 'fileName',
      header: t('fields.itemFile.fileName'),
      cell: props => <EditableFileCell {...props} folder={folder} />,
      size: 100,
    },
    {
      id: 'note',
      accessorKey: 'note',
      header: t('fields.itemFile.note'),
      cell: props => <EditableInputCell {...props} />,
      size: 150,
    },
    {
      id: 'removeRow',
      header: ' ',
      maxSize: 10,
      cell: props => {
        return (
          <DataTableRowActions
            onDelete={() => {
              props.table.options.meta?.removeRowByIndex(props.row.index);
            }}
            canDelete={role?.isCreate || role?.isUpdate}
          />
        );
      },
    },
  ];

  return (
    <div>
      <DataTable
        tableId={TABLES.SALES_ORDER_RECORD_FILES}
        sortColumn="id"
        role={role}
        columns={columns}
        editableData={editableData}
        syncQueryParams={false}
        setEditableData={setEditableData}
        onAddButtonClick={() => {
          const newRow = {
            ...defaultRow,
            typeUpload,
            id: -getRandomNumber(),
          };
          setEditableData([...editableData, newRow]);
        }}
        customToolbar={() => (
          <>
            <Button
              icon="plus"
              type="default"
              stylingMode="text"
              className="w-[193px]"
              onClick={toggleAddNewFileTypeForm}
              text={`${addLabel} ${t('fields.itemFile.typeFileId')}`}
            />
            <RecordFilesAttachButton
              folder={folder}
              setIsLoading={setIsLoading}
              onResponse={response => {
                const data: RecordAttachmentFile[] = response.map(item => {
                  return {
                    ...defaultRow,
                    id: -getRandomNumber(),
                    name: item.name,
                    folder: item.src,
                    fileName: item.fileName,
                    host: item.host,
                    size: item.size,
                    type: item.type,
                    typeUpload,
                  };
                });
                const newData = [...(editableData || []).filter(item => item.fileName), ...data];
                setEditableData(newData);
              }}
            />
          </>
        )}
      />
      <BasicDialog
        className="max-w-[100vw] md:w-auto"
        open={isAddNewFileTypeFormOpen}
        title={t('page.form.addNew', { ns: 'fileType' })}
        toggle={() => {
          toggleAddNewFileTypeForm();
          fetchFileTypes({});
        }}
      >
        <FileTypeForm editId={0} role={fileTypeRole} />
      </BasicDialog>
    </div>
  );
};
