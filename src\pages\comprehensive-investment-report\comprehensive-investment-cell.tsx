import { <PERSON><PERSON>ield, FormLabel } from '@/components/ui/form';
import { TextBox } from 'devextreme-react';
import { InputNumber } from '@/components/ui/input';
import { ComprehensiveInvestmentReportDetail } from '@/types';

type Props = {
  detail: ComprehensiveInvestmentReportDetail;
  index: number;
};

export const ComprehensiveInvestmentCell = ({ detail, index }: Props) => {
  return (
    <div className="flex flex-col items-start self-end">
      {/* Label */}
      <FormLabel
        name={`comprehensiveInvestmentReportDetails.${index}.value`}
        htmlFor={`comprehensiveInvestmentReportDetails.${index}.value`}
        className="hidden md:block md:w-[250px]"
      >
        {detail.fieldName}
      </FormLabel>
      <FormField
        id={`comprehensiveInvestmentReportDetails.${index}.value`}
        name={`comprehensiveInvestmentReportDetails.${index}.value`}
        className="min-w-0 flex-1 md:w-[250px]"
        label={detail?.fieldName || ''}
      >
        {detail.valueType === 'number' && <InputNumber />}
        {(!detail.valueType || detail.valueType === 'string') && <TextBox />}
      </FormField>
    </div>
  );
};
