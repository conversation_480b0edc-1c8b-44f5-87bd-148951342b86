{"name": "mks-beverage-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:code": "concurrently \"node src/code-generator.js\" \"vite\"", "build": "tsc && vite build --mode development", "build:staging": "tsc && vite build --mode staging", "build:prod": "vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-pwa-assets": "pwa-assets-generator --preset minimal public/logo_bqlda.png"}, "dependencies": {"@hello-pangea/dnd": "^16.3.0", "@hookform/resolvers": "^3.1.1", "@langchain/core": "^0.3.36", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-visually-hidden": "^1.1.2", "@rehype-pretty/transformers": "^0.13.2", "@tanstack/react-query": "^5.40.1", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-table": "^8.9.3", "@types/firebase": "^3.2.1", "@types/react-beautiful-dnd": "^13.1.4", "@types/uuid": "^9.0.2", "axios": "^1.4.0", "body-parser": "^1.20.3", "chart.js": "^4.0.0", "ckeditor4-react": "^5.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.9", "devextreme": "24.2.3", "devextreme-react": "24.2.3", "dompurify": "^3.2.6", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "expr-eval": "^2.0.2", "express": "^4.21.2", "file-saver": "^2.0.5", "firebase": "^10.7.1", "get-orientation": "^1.1.2", "html-entities": "^2.5.2", "html2canvas": "^1.4.1", "jodit-pro": "^4.5.5", "jodit-pro-react": "^5.2.3", "jodit-react": "^5.2.15", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.263.1", "match-sorter": "^6.3.1", "nunjucks": "^3.2.4", "path-to-regexp": "^8.2.0", "react": "^18.2.0", "react-avatar-editor": "^13.0.0", "react-chartjs-2": "^5.0.0", "react-day-picker": "^8.8.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.0", "react-error-boundary": "^4.0.11", "react-helmet": "^6.1.0", "react-hook-form": "7.45.2", "react-i18next": "^13.5.0", "react-number-format": "^5.3.0", "react-resizable-panels": "^2.1.6", "react-router-dom": "^6.27.0", "react-to-print": "^2.15.1", "react-toastify": "^9.1.3", "rehype-pretty-code": "^0.14.0", "rehype-stringify": "^10.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.1", "shiki": "^1.29.2", "sort-by": "^0.0.2", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "uuid": "^9.0.0", "xlsx": "^0.18.5", "zod": "^3.21.4"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/ckeditor4": "~4.20.7", "@types/cors": "^2.8.17", "@types/eslint": "~8.56.1", "@types/eslint-config-prettier": "~6.11.3", "@types/express": "^5.0.0", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.3", "@types/leaflet": "^1.9.4", "@types/lodash": "^4.14.202", "@types/node": "^20.17.23", "@types/nunjucks": "^3.2.6", "@types/react": "^18.2.15", "@types/react-avatar-editor": "^13.0.0", "@types/react-dom": "^18.2.7", "@types/react-helmet": "~6.1.11", "@types/sort-by": "~1.2.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vite-pwa/assets-generator": "^0.2.6", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.14", "concurrently": "^9.1.2", "devextreme-themebuilder": "^24.2.3", "eslint": "^8.45.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.3", "postcss": "^8.4.27", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.3.3", "typescript": "^5.8.2", "vite": "^4.4.5", "vite-plugin-pwa": "^0.21.1"}}