/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { DisbursementProgressByFundingSource } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('disbursementProgressByFundingSource');
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const DisbursementProgressByFundingSourceDataTable = ({
  path,
  permission,
}: {
  path: string;
  permission: number;
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('disbursementProgressByFundingSource');

  const role = usePermission(permission);

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: budgetFunds } = useEntity({
    queryKey: [QUERIES.BUDGET_FUND],
    model: 'budget-fund',
  });

  const getTargetAlias = (target: DisbursementProgressByFundingSource | undefined) => {
    if (!target) {
      return '';
    }
    return target.note!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<DisbursementProgressByFundingSource, PeriodFilter>({
    queryRangeName: 'FiscalYear',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<DisbursementProgressByFundingSource>(
      'disbursement-progress-by-funding-source'
    ),
    deleteKey: [MUTATE.DELETE_DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
    invalidateKey: [QUERIES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
    queryFn: () => {
      return createQueryPaginationFn<DisbursementProgressByFundingSource>(
        'disbursement-progress-by-funding-source'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'FiscalYear',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<DisbursementProgressByFundingSource>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<DisbursementProgressByFundingSource>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };
  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };
  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn('FiscalYear', from!, to!);
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataType="date"
          format={'year'}
          dataField="fiscalYear"
          caption={t('fields.fiscalYear')}
        />
        <Column dataField="budgetFundId" caption={t('fields.budgetFundId')}>
          <Lookup dataSource={budgetFunds} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="note" caption={t('fields.note')} />
        <Column dataField="creatorId" caption={t('fields.creatorId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="disbursementProgressByFundingSource"
      />
    </PageLayout>
  );
};
