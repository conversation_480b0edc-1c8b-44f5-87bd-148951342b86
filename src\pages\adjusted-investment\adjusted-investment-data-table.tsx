/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, PROFESSIONS, QUERIES, TABLES } from '@/constant';
import {
  useAuth,
  useDataTable,
  useEntity,
  usePermission,
  useStatus,
  useSyncProjectFilter,
} from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { AdjustedInvestment, Status } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useContext } from 'react';
import { SelectedProjectContext } from '@/provider/selected-project-context';

const t = translationWithNamespace('adjustedInvestment');

const path = PATHS.ADJUSTED_INVESTMENT;
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

const renderStatusCell = (cellData: any, status: Status[]) => {
  // Lấy giá trị từ danh sách Lookup
  const objectStatus = status.find(item => item.id === cellData.data.statusId);
  const statusColor = objectStatus?.colorCode || '#000000';

  return (
    <div className={`status-contact flex items-center gap-1`} style={{ color: statusColor }}>
      {objectStatus && (
        <>
          <span
            className={`h-[10px] w-[10px] rounded-full`}
            style={{ backgroundColor: statusColor }}
            aria-hidden="true"
          />
          <span style={{ color: statusColor, fontWeight: 'bold' }}>{objectStatus?.name}</span>
        </>
      )}
    </div>
  );
};

export const AdjustedInvestmentDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('adjustedInvestment');

  const role = usePermission(PERMISSIONS.ADJUSTED_INVESTMENT);
  const { user, projects } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;

  const { list: approvalProcesss } = useEntity({
    queryKey: [QUERIES.APPROVAL_PROCESS],
    model: 'approval-process',
  });
  // const { list: projects } = useEntity({ queryKey: [QUERIES.PROJECT], model: 'project' });
  const { list: statuss } = useEntity({ queryKey: [QUERIES.STATUS], model: 'status' });
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const getTargetAlias = (target: AdjustedInvestment | undefined) => {
    if (!target) {
      return '';
    }
    return target.note!;
  };
  const { selectedProject } = useContext(SelectedProjectContext);

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<AdjustedInvestment, PeriodFilter>({
    queryRangeName: 'adjustedInvestmentTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<AdjustedInvestment>('adjusted-investment'),
    deleteKey: [MUTATE.DELETE_ADJUSTED_INVESTMENT],
    invalidateKey: [QUERIES.ADJUSTED_INVESTMENT],
    initialQuery: {
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${selectedProject?.id || projectIds || 0})`,
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.ADJUSTED_INVESTMENT],
    queryFn: () => {
      return createQueryPaginationFn<AdjustedInvestment>('adjusted-investment')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'AdjustedInvestmentTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<AdjustedInvestment>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<AdjustedInvestment>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  const statusOptions = useStatus(PROFESSIONS.ADJUSTED_INVESTMENT);

  useSyncProjectFilter({
    queryListParams,
    queryListMethods,
    onSyncedParams: () => {
      callbackWithTimeout(refetch);
    },
  });

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn('adjustedInvestmentTime', from!, to!);
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.ADJUSTED_INVESTMENT}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="adjustedInvestmentTime"
          caption={t('fields.adjustedInvestmentTime')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="code" caption={t('fields.code')} alignment="left" />
        <Column dataField="documentCode" caption={t('fields.documentCode')} alignment="left" />
        <Column dataField="projectId" caption={t('fields.projectId')}>
          <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="note" caption={t('fields.note')} alignment="left" />
        <Column
          dataField="totalPostTaxValue"
          caption={t('fields.totalPostTaxValue')}
          dataType="number"
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="signingDate"
          caption={t('fields.signingDate')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="signerId" caption={t('fields.signerId')}></Column>
        <Column
          dataField="statusId"
          caption={t('fields.statusId')}
          cellRender={cell => renderStatusCell(cell, statusOptions)}
        >
          <Lookup dataSource={statuss} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="approvalProcessId" caption={t('fields.approvalProcessId')}>
          <Lookup
            dataSource={approvalProcesss}
            displayExpr={displayExpr(['name'])}
            valueExpr={'id'}
          />
        </Column>
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="adjustedInvestment"
      />
    </PageLayout>
  );
};
