import { InputNumber } from '@/components/ui/input';
import { DEFAULT_DECIMAL, DEFAULT_DECIMAL_SCALE } from '@/constant';
import { TextArea } from 'devextreme-react';
import { DataGridTypes } from 'devextreme-react/data-grid';

export const CellRendererMoney = (cell: DataGridTypes.ColumnEditCellTemplateData) => {
  return (
    <InputNumber
      defaultValue={cell?.value}
      value={cell?.value}
      isMoney
      decimal={DEFAULT_DECIMAL}
      onChange={(value: number | undefined) => {
        if (value === undefined) return;
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        cell.setValue(value);
      }}
    />
  );
};
export const CellRendererNumber = (cell: DataGridTypes.ColumnEditCellTemplateData) => {
  return (
    <InputNumber
      defaultValue={cell?.value}
      value={cell?.value}
      decimal={DEFAULT_DECIMAL_SCALE}
      onChange={(value: number | undefined) => {
        if (value === undefined) return;
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        cell.setValue(value);
      }}
    />
  );
};
export const CellRendererTextArea = (cell: DataGridTypes.ColumnEditCellTemplateData) => {
  // const textAreaRef = useRef<TextAreaRef>(null);
  // console.log('textAreaRef current', textAreaRef.current);
  // if (textAreaRef.current) {
  //   console.log('textAreaRef', textAreaRef.current.instance());
  // }
  return (
    <TextArea
      // ref={textAreaRef}
      className="h-full w-full whitespace-pre-wrap break-words"
      value={cell?.value}
      onValueChange={value => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        cell.setValue(value);
      }}
    />
  );
};
