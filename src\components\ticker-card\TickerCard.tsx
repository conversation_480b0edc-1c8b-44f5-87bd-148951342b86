import { clsx } from 'clsx';
import { ChevronRight } from 'lucide-react';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

const colorMap = {
  green: 'text-green-600 bg-green-100 border-b-green-500 hover:bg-green-200',
  red: 'text-red-600 bg-red-100 border-b-red-500 hover:bg-red-200',
  yellow: 'text-yellow-600 bg-yellow-100 border-b-yellow-500 hover:bg-yellow-200',
  blue: 'text-blue-600 bg-blue-100 border-b-blue-500 hover:bg-blue-200',
  pink: 'text-pink-600 bg-pink-100 border-b-pink-500 hover:bg-pink-200',
};
export const TickerCard = ({
  title,
  icon,
  tone,
  value,
  percentage,
  formatValue = v => `${v}`,
  link,
}: {
  title: string;
  icon: string | ReactNode;
  tone?: keyof typeof colorMap;
  value: number;
  percentage?: number;
  formatValue?: (value: number) => string;
  link?: string;
}) => {
  const isPositive = percentage ?? 0 > 0;

  const status = tone || (isPositive ? 'blue' : 'red');
  const colorClass = colorMap[status];
  const renderIcon = () => {
    if (typeof icon === 'string') {
      return <i className={`dx-icon dx-icon-${icon}`} />;
    }
    return icon; // React component
  };

  return (
    <div
      className={clsx('flex items-center gap-3 rounded-lg border-b-4 p-4 shadow-sm', colorClass)}
    >
      <div
        className={clsx(
          'flex h-12 w-12 items-center justify-center rounded-full text-xl',
          colorClass
        )}
      >
        {renderIcon()}
      </div>

      <div className="flex-1">
        <div className="mb-1 h-8 text-xs font-bold text-gray-700 xl:h-8">{title.toUpperCase()}</div>
        <div className="text-2xl font-semibold text-gray-900">{formatValue(value)}</div>
      </div>

      <div
        className={clsx(
          'flex items-center rounded-full px-2 py-1 text-xs font-semibold',
          colorClass
        )}
      >
        <Link to={link || ''}>
          <ChevronRight className={clsx('h-10 w-10', colorClass)} />
        </Link>
      </div>
    </div>
  );
};
