import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { ArrayElement } from './common';
import { recordAttachmentSchema } from './records-attachment';
export const requireFinancialSettlementReportText = requiredTextWithNamespace(
  'financialSettlementReport'
);

export const financialSettlementReportSchema = z.object({
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  projectId: z.number().nullable(),
  projectCode: z.string().nullable(),
  projectName: z.string().nullable(),
  projectOwnerId: z.number().nullable(),
  projectOwnerName: z.string().nullable(),
  financialSettlementReportTime: z.date().nullable().optional(),
  ids: z.number().nullable(),
  sort: z.string().nullable().optional(),
  code: z.string().nullable().optional(),
  id: z.number(),
  price: z.number().nullable(),
  totalAmount: z.number().nullable(),
  usageStartDate: z.date().nullable().optional(),
  receivingUnit: z.string().nullable(),
  damageCosts: z.number().nullable().optional(),
  nonCapitalizableCosts: z.number().nullable().optional(),
  projectProgressStatus: z.string().nullable().optional(),
  projectImplementationReviewAndEvaluation: z.string().nullable().optional(),
  proposal: z.string().nullable().optional(),
  financialSettlementReportAssetGroups: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().nullable().optional(),
      groupName: z.string().nullable().optional(),
      totalAmount: z.number().nullable().optional(),
    })
  ),
  financialSettlementReportBudgetFunds: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().optional().nullable(),
      budgetFundId: z.number().optional().nullable(),
      budgetFundName: z.string().optional().nullable(),
      budgetFundParentId: z.number().optional().nullable(),
      approvedFinalProjectBudget: z.number().nullable(),
      allocatedPlannedCapital: z.number().nullable(),
      disbursedFunds: z.number().nullable(),
      child: z.array(z.number().nullable()).nullable().optional(),
      level: z.number().nullable().optional(),
      parentId: z.number().nullable().optional(),
    })
  ),
  financialSettlementReportDataReconciliationTables: z.array(
    z.object({
      id: z.number(),
      ordinalNumber: z.number().nullable().optional(),
      financialSettlementReportId: z.number().nullable(),
      budgetFundId: z.number().nullable().optional(),
      budgetFundName: z.string().nullable().optional(),
      poPlannedCapital: z.number().nullable().optional(),
      poDisbursedTotal: z.number().nullable().optional(),
      poDisbursedCompletedWorkload: z.number().nullable().optional(),
      poDisbursedAdvance: z.number().nullable().optional(),
      amountPlannedCapital: z.number().nullable().optional(),
      amountAllocatedTotal: z.number().nullable().optional(),
      amountAllocatedCompletedWorkload: z.number().nullable().optional(),
      amountAllocatedAdvance: z.number().nullable().optional(),
      difference: z.number().nullable().optional(),
      notes: z.string().nullable().optional(),
      budgetYear: z.number().nullable().optional(),
    })
  ),
  financialSettlementReportDocumentLists: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().nullable().optional(),
      groupDocId: z.number().nullable().optional(),
      typeDocId: z.number().nullable().optional(),
      noDoc: z.string().nullable().optional(),
      dateCreate: z.date().nullable().optional(),
      content: z.string().nullable().optional(),
      agencyId: z.number().nullable().optional(),
      agencyName: z.string().nullable().optional(),
      typeDocParentId: z.number().nullable().optional(),
      typeDocName: z.string().nullable().optional(),
      typeDocParentName: z.string().nullable().optional(),
      groupDocName: z.string().nullable().optional(),
      notes: z.string().nullable().optional(),
    })
  ),
  financialSettlementReportInvestmentCosts: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().nullable().optional(),
      costItemTypeId: z.number().nullable().optional(),
      approvedFinalProjectBudget: z.number().nullable().optional(),
      proposedSettlementValue: z.number().nullable().optional(),
      proposedLiquidationAmount: z.number().nullable().optional(),
    })
  ),
  financialSettlementReportProposedSettlementInvestmentCosts: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().nullable().optional(),
      costItemId: z.number().nullable().optional(),
      costItemTypeId: z.number().nullable().optional(),
      approvedFinalProjectBudget: z.number().nullable().optional(),
      approvedFinalEstimate: z.number().nullable().optional(),
      proposedSettlementValue: z.number().nullable().optional(),
      increaseDecreaseReason: z.string().nullable().optional(),
    })
  ),
  financialSettlementReportProjectDebtStatusStatistics: z.array(
    z.object({
      id: z.number(),
      financialSettlementReportId: z.number().nullable().optional(),
      contractorId: z.number().nullable().optional(),
      contractorName: z.string().nullable().optional(),
      costItemId: z.number().nullable().optional(),
      costItemName: z.string().nullable().optional(),
      proposedSettlementValue: z.number().nullable().optional(),
      disbursedCapital: z.number().nullable().optional(),
      payableAmount: z.number().nullable().optional(),
      receivableAmount: z.number().nullable().optional(),
      note: z.string().nullable().optional(),
    })
  ),
  itemsRecordManagement: z.array(recordAttachmentSchema),
  formCreator: z.string().nullable().optional(),
  headOfFinanceDepartment: z.string().nullable().optional(),
  pmoDirector: z.string().nullable().optional(),
});
export const getFinancialSettlementReportDto = z.object({
  rptDataReconciliationTableDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      budgetFundId: z.number().nullable(),
      budgetFundCode: z.string().nullable().optional(),
      budgetFundName: z.string().nullable().optional(),
      poPlannedCapital: z.number().nullable(),
      poDisbursedTotal: z.number().nullable(),
      poDisbursedCompletedWorkload: z.number().nullable(),
      poDisbursedAdvance: z.number().nullable(),
      budgetYear: z.number().nullable(),
    })
  ),
  rptFinancialSettlementReportByBudgetFundDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      budgetFundId: z.number().nullable(),
      budgetFundCode: z.string().nullable().optional(),
      budgetFundName: z.string().nullable().optional(),
      budgetFundParentId: z.number().nullable(),
      approvedFinalProjectBudget: z.number().nullable(),
      allocatedPlannedCapital: z.number().nullable(),
      disbursedFunds: z.number().nullable(),
    })
  ),
  rptDocumentListOfProjectDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      projectId: z.number().nullable(),
      groupDocId: z.number().nullable(),
      groupDocName: z.string().nullable().optional(),
      typeDocId: z.number().nullable(),
      typeDocName: z.string().nullable().optional(),
      noDoc: z.string().nullable().optional(),
      dateCreate: z.date().nullable().optional(),
      content: z.string().nullable().optional(),
      agencyId: z.number().nullable(),
      agencyName: z.string().nullable().optional(),
      notes: z.string().nullable().optional(),
      typeDocParentId: z.number().nullable(),
      typeDocParentName: z.string().optional(),
    })
  ),
  rptProjectDebtStatusStatisticsDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      contractorId: z.number().nullable(),
      contractorCode: z.string().nullable().optional(),
      contractorName: z.string().nullable().optional(),
      costItemId: z.number().nullable(),
      costItemCode: z.string().nullable().optional(),
      costItemName: z.string().nullable().optional(),
      proposedSettlementValue: z.number().nullable(),
      disbursedCapital: z.number().nullable(),
      payableAmount: z.number().nullable(),
      receivableAmount: z.number().nullable(),
      note: z.string().nullable().optional(),
    })
  ),
  proposedSettlementInvestmentCostDetailDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      costItemId: z.number().nullable(),
      costItemCode: z.string().nullable().optional(),
      costItemName: z.string().nullable().optional(),
      costItemTypeId: z.number().nullable(),
      costItemTypeCode: z.string().nullable().optional(),
      costItemTypeName: z.string().nullable().optional(),
      approvedFinalProjectBudget: z.number().nullable(),
      approvedFinalEstimate: z.number().nullable(),
      proposedSettlementValue: z.number().nullable(),
    })
  ),
  rptInvestmentCostTypeDtos: z.array(
    z.object({
      ordinalNumber: z.number().nullable(),
      costItemTypeId: z.number().nullable(),
      costItemTypeCode: z.string().nullable().optional(),
      costItemTypeName: z.string().nullable().optional(),
      approvedFinalProjectBudget: z.number().nullable(),
      proposedSettlementValue: z.number().nullable(),
    })
  ),
});

export type GetFinancialSettlementReportDto = z.infer<typeof getFinancialSettlementReportDto>;

export type RptDataReconciliationTable = ArrayElement<
  GetFinancialSettlementReportDto['rptDataReconciliationTableDtos']
>;

export type RptFinancialSettlementReportByBudgetFundDtos = ArrayElement<
  GetFinancialSettlementReportDto['rptFinancialSettlementReportByBudgetFundDtos']
>;

export type RptDocumentListOfProjectDtos = ArrayElement<
  GetFinancialSettlementReportDto['rptDocumentListOfProjectDtos']
>;

export type RptProjectDebtStatusStatisticsDtos = ArrayElement<
  GetFinancialSettlementReportDto['rptProjectDebtStatusStatisticsDtos']
>;

export type ProposedSettlementInvestmentCostDetailDtos = ArrayElement<
  GetFinancialSettlementReportDto['proposedSettlementInvestmentCostDetailDtos']
>;

export type RptInvestmentCostTypeDtos = ArrayElement<
  GetFinancialSettlementReportDto['rptInvestmentCostTypeDtos']
>;

export type FinancialSettlementReport = z.infer<typeof financialSettlementReportSchema>;
export type FinancialSettlementReportAssetGroup = ArrayElement<
  FinancialSettlementReport['financialSettlementReportAssetGroups']
>;
export type FinancialSettlementReportBudgetFund = ArrayElement<
  FinancialSettlementReport['financialSettlementReportBudgetFunds']
>;
export type FinancialSettlementReportDataReconciliationTable = ArrayElement<
  FinancialSettlementReport['financialSettlementReportDataReconciliationTables']
>;
export type FinancialSettlementReportDocumentList = ArrayElement<
  FinancialSettlementReport['financialSettlementReportDocumentLists']
>;
export type FinancialSettlementReportInvestmentCost = ArrayElement<
  FinancialSettlementReport['financialSettlementReportInvestmentCosts']
>;
export type FinancialSettlementReportProposedSettlementInvestmentCost = ArrayElement<
  FinancialSettlementReport['financialSettlementReportProposedSettlementInvestmentCosts']
>;
export type FinancialSettlementReportProjectDebtStatusStatistics = ArrayElement<
  FinancialSettlementReport['financialSettlementReportProjectDebtStatusStatistics']
>;

export const defaultValuesFinancialSettlementReport: FinancialSettlementReport = {
  storeId: null, //
  branchId: null, //
  projectId: null, // Dự án
  projectCode: '', // Mã dự án
  projectName: '',
  projectOwnerId: null, // Chủ đầu tư
  projectOwnerName: '',
  financialSettlementReportTime: new Date(), // Ngày lập
  ids: null, // ids
  sort: '', // sort
  code: '', // Mã phiếu
  id: 0, // Khóa chính
  price: 0, // Giá đơn vị
  totalAmount: 0, // Tổng nguyên giá
  usageStartDate: new Date(), // Ngày đưa tài sản dài hạn vào sử dụng
  receivingUnit: '', // Đơn vị tiếp nhận sử dụng,
  damageCosts: 0,
  nonCapitalizableCosts: 0,
  projectProgressStatus: '',
  projectImplementationReviewAndEvaluation: '',
  proposal: '',
  itemsRecordManagement: [],
  financialSettlementReportAssetGroups: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      groupName: '', //
      totalAmount: 0, //
    },
  ],
  financialSettlementReportBudgetFunds: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      budgetFundId: null, // Nguồn ngân sách
      budgetFundParentId: null, // Nguồn ngân sách cha
      approvedFinalProjectBudget: 0, // Tổng mức đầu tư của dự án
      allocatedPlannedCapital: 0, // Vốn kế hoạch được giao
      disbursedFunds: 0, // Vốn đã giải ngân
    },
  ],
  financialSettlementReportDataReconciliationTables: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      budgetFundId: null, // Nội dung
      budgetFundName: '', // Nội dung
      poPlannedCapital: 0, // Số liệu của chủ đầu tư - vốn kế hoạch
      poDisbursedTotal: 0, // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Tổng số
      poDisbursedCompletedWorkload: 0, // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Thanh toán khối lượng hoàn thành
      poDisbursedAdvance: 0, // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Tạm ứng
      amountPlannedCapital: 0, // Thành tiền - Kế hoạch vốn
      amountAllocatedTotal: 0, // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Tổng số
      amountAllocatedCompletedWorkload: 0, // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Thanh toán KLHT
      amountAllocatedAdvance: 0, // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Tạm ứng
      difference: 0, // Chênh lệch
      notes: '', // Ghi chú
      budgetYear: null, // Năm ngân sách
    },
  ],
  financialSettlementReportDocumentLists: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      groupDocId: null, // Nhóm văn bản
      typeDocId: null, //Loại văn bản
      noDoc: '', // Mã số hồ sơ
      dateCreate: new Date(), // Ngày tạo hồ sơ
      content: '', // Tên văn bản
      agencyId: null, // Cơ quan ban hành
      typeDocParentId: null, // loại văn bản cha
    },
  ],
  financialSettlementReportInvestmentCosts: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      costItemTypeId: null, // Nội dung chi phí
      approvedFinalProjectBudget: 0, // Tổng mức đầu tư của dự án (dự án thành phần, tiểu dự án độc lập) hoặc dự toán (công trình, hạng mục công trình) được phê duyệt hoặc điều chỉnh lần cuối
      proposedSettlementValue: 0, // Giá trị đề nghị quyết toán
      proposedLiquidationAmount: 0, // Giá trị tăng giảm
    },
  ],
  financialSettlementReportProposedSettlementInvestmentCosts: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      costItemId: null, // Nội dung chi phí
      costItemTypeId: null, //Loại Nội dung chi phí
      approvedFinalProjectBudget: 0, // Tổng mức đầu tư của dự án (dự án thành phần, tiểu dự án độc lập) hoặc dự toán (công trình, hạng mục công trình) được phê duyệt hoặc điều chỉnh lần cuối
      approvedFinalEstimate: 0, // Dự toán (Tổng dự toán) được phê duyệt hoặc điều chỉnh lần cuối
      proposedSettlementValue: 0, // Giá trị đề nghị quyết toán
      increaseDecreaseReason: '', // Nguyên nhân tăng, giảm
    },
  ],
  financialSettlementReportProjectDebtStatusStatistics: [
    {
      id: 0, // Khóa chính
      financialSettlementReportId: 0, //
      contractorId: null, // Tên cá nhân, đơn vị thực hiện
      costItemId: null, // Nội dung công việc, hợp đồng thực hiện
      proposedSettlementValue: 0, // Giá trị đề nghị quyết toán
      disbursedCapital: 0, // Vốn đã giải ngân
      payableAmount: 0, // Phải trả
      receivableAmount: 0, // Phải thu
      note: '', // Ghi chú
    },
  ],
};
