import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { recordAttachmentSchema } from './records-attachment';

const requireSpendingCommitmentText = requiredTextWithNamespace('completionReport');

export const completionReportSchema = z.object({
  branchId: z.number().nullable(),
  id: z.number(),
  code: z.string().nullable().optional(),
  projectId: z
    .number({
      required_error: requireSpendingCommitmentText('projectId', 'select'),
      invalid_type_error: requireSpendingCommitmentText('projectId', 'select'),
    })
    .min(1, requireSpendingCommitmentText('projectId', 'select')), //dự án
  userCreatedId: z.number().nullable(),
  userCreatedName: z.string().nullable().optional(),
  completionReportTime: z.coerce.date().nullable().optional(), // <PERSON><PERSON><PERSON> lập
  approvalNumber: z.string().nullable().optional(), // <PERSON><PERSON> quyết định
  approvalDate: z.coerce.date().nullable().optional(), // Ng<PERSON>y quyết định
  approvalContent: z.string().nullable().optional(),
  note: z.string().nullable().optional(),
  ids: z.number().nullable(),
  sort: z.string().nullable().optional(),
  projectOrSectionName: z.string().nullable().optional(),
  itemsRecordManagement: z.array(recordAttachmentSchema),
});

export type CompletionReport = z.infer<typeof completionReportSchema>;

export const defaultValuesCompletionReport: CompletionReport = {
  branchId: null, // mã chi nhánh
  id: 0, // Khóa chính
  completionReportTime: new Date(), // Ngày lập
  userCreatedId: null, // Người lập
  code: '', // Mã phiếu
  projectId: 0, // Dự án
  note: '', // Ghi chú
  ids: null, // ids
  sort: '',
  approvalNumber: '', // Số quyết định
  approvalDate: new Date(), // Ngày quyết định
  approvalContent: '', // Nội dung quyết định
  itemsRecordManagement: [], // Hồ sơ đính kèm
};
