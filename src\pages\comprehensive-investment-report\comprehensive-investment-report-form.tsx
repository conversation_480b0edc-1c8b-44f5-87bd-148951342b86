import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { Form, FormField, FormLabel } from '@/components/ui/form';
import { MUTATE, PATHS, PERMISSIONS, PROFESSIONS, QUERIES, selectLabel } from '@/constant';
import { useAuth, useFormHandler, useFormOperation, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import {
  ComprehensiveInvestmentReport,
  comprehensiveInvestmentReportSchema,
  defaultValuesComprehensiveInvestmentReport,
} from '@/types';
import Button from 'devextreme-react/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RecordEditableTable } from '@/components/records-attachment';
import { RowSelectionState } from '@tanstack/react-table';
import {
  ComprehensiveInvestmentTab,
  ComprehensiveInvestmentTabType,
} from './comprehensive-investment-tab';
import { DateBox } from 'devextreme-react';

const tabs: ComprehensiveInvestmentTabType[] = [
  // {
  //   code: 'I',
  //   name: 'I. TÌNH HÌNH BAN HÀNH CÁC VĂN BẢN HƯỚNG DẪN CÁC CHÍNH SÁCH, PHÁP LUẬT LIÊN QUAN ĐẾN ĐẦU TƯ THEO THẨM QUYỀN',
  // },
  {
    code: 'II',
    name: 'II. TÌNH HÌNH QUẢN LÝ QUY HOẠCH',
  },
  {
    code: 'III',
    name: 'III. TÌNH HÌNH THỰC HIỆN KẾ HOẠCH VỐN ĐẦU TƯ',
    children: [
      {
        code: 'III_1',
        name: '1. Tổng hợp số liệu về kế hoạch vốn đầu tư trong năm',
      },
      {
        code: 'III_2',
        name: '2. Tình hình thực hiện vốn đầu tư trong kỳ',
      },
      {
        code: 'III_3',
        name: '3. Tình hình giải ngân vốn đầu tư trong kỳ',
      },
    ],
  },
  {
    code: 'V',
    name: 'V. TÌNH HÌNH QUẢN LÝ CÁC DỰ ÁN ĐẦU TƯ CÔNG',
    children: [
      {
        code: 'V_1',
        name: '1. Tình hình lập, thẩm định, quyết định/ quyết định điều chỉnh chủ trương đầu tư',
        children: [
          {
            code: 'V_1_a',
            name: 'a) Báo cáo đề xuất chủ trương đầu tư đã được lập (trong năm)',
          },
          {
            code: 'V_1_b',
            name: 'b) Báo cáo đề xuất chủ trương đầu tư đã được thẩm định',
          },
        ],
      },
    ],
  },
];

const onComprehensiveInvestmentReportMutationSuccess = createMutationSuccessFn(
  'comprehensiveInvestmentReport'
);
export const ComprehensiveInvestmentReportForm = () => {
  const { id: editId } = useParams();

  const { t } = useTranslation('comprehensiveInvestmentReport');

  const role = usePermission(PERMISSIONS.COMPREHENSIVE_INVESTMENT_REPORT);
  const { user } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(
    PATHS.COMPREHENSIVE_INVESTMENT_REPORT
  );

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesComprehensiveInvestmentReport,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<ComprehensiveInvestmentReport>({
    queryKey: [MUTATE.COMPREHENSIVE_INVESTMENT_REPORT, editId],
    mutateKey: [MUTATE.COMPREHENSIVE_INVESTMENT_REPORT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.COMPREHENSIVE_INVESTMENT_REPORT],
    readFn: createQueryByIdFn<ComprehensiveInvestmentReport>('comprehensive-investment-report'),
    createFn: createPostMutateFn<ComprehensiveInvestmentReport>('comprehensive-investment-report'),
    updateFn: createPutMutateFn<ComprehensiveInvestmentReport>('comprehensive-investment-report'),
    formatPayloadFn: data => ({
      ...data,
    }),
    formatResponseFn: data => ({
      ...data,
    }),
    onCreateSuccess: data => {
      onComprehensiveInvestmentReportMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onComprehensiveInvestmentReportMutationSuccess,
    formOptions: {
      resolver: zodResolver(comprehensiveInvestmentReportSchema),
      defaultValues,
    },
  });

  const { reset, onTimeChange } = useFormOperation<ComprehensiveInvestmentReport>({
    model: 'comprehensive-investment-report',
    fieldTime: 'comprehensiveInvestmentReportTime',
    createCodeKey: [QUERIES.COMPREHENSIVE_INVESTMENT_REPORT],
    formMethods: methods,
  });

  const details = methods.watch('comprehensiveInvestmentReportDetails');

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
          >
            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4 xl:max-w-screen-2xl">
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4">
                <div className="flex items-center">
                  <FormLabel htmlFor="year" className="hidden w-[100px] md:block">
                    {t('fields.year')}
                  </FormLabel>
                  <FormField
                    id="year"
                    name="year"
                    className="min-w-0 flex-1 md:w-[250px]"
                    label={t('fields.year')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.year')}`}
                      calendarOptions={{
                        maxZoomLevel: 'decade',
                        minZoomLevel: 'decade',
                      }}
                      displayFormat={'year'}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                {/* Ngày lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="completionAcceptanceNoticeTime"
                    htmlFor="completionAcceptanceNoticeTime"
                    className="hidden w-[90px] md:block"
                  >
                    {t('fields.completionAcceptanceNoticeTime')}
                  </FormLabel>
                  <FormField
                    id="completionAcceptanceNoticeTime"
                    name="completionAcceptanceNoticeTime"
                    className="min-w-0 flex-1"
                    type="date"
                    onChange={e => {
                      onTimeChange(e.target.value);
                    }}
                    label={t('fields.completionAcceptanceNoticeTime')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.completionAcceptanceNoticeTime')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                {/* Mã phiếu */}
                <div className="flex items-center">
                  <FormLabel name="code" htmlFor="code" className="hidden w-[90px] md:block">
                    {t('fields.code')}
                  </FormLabel>
                  <FormField
                    id="code"
                    name="code"
                    className="min-w-0 flex-1"
                    label={t('fields.code')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly={true} />
                  </FormField>
                </div>

                {/* Người lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="userCreatedId"
                    htmlFor="userCreatedId"
                    className="hidden w-[90px] md:block"
                  >
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name="userCreatedId"
                    className="min-w-0 flex-1"
                    label={t('fields.userCreatedId')}
                  >
                    <FormCombobox
                      defaultText={userCreatedName}
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      disabled
                    />
                  </FormField>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue={'attachment'}>
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger key="attachment" value="attachment">
                      {t('page.tabs.attachment')}
                    </TabsTrigger>
                    {tabs.map(tab => (
                      <TabsTrigger key={tab.code} value={tab.code}>
                        {tab.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="comprehensive-investment-report"
                    profession={PROFESSIONS.COMPREHENSIVE_INVESTMENT_REPORT}
                  />
                </TabsContent>
                {tabs.map(tab => (
                  <ComprehensiveInvestmentTab key={tab.code} item={tab} details={details} />
                ))}
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
