import { PageLayout } from '@/components/page-layout';
import { QUERIES, TABLES } from '@/constant';
import { useEntity } from '@/hooks';
import {
  defaultValuesProjectApproval,
  Project,
  ProjectApproval,
  ProjectTabChildrenProps,
} from '@/types';
import { Button } from 'devextreme-react';
import { SyntheticEvent } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { getRandomNumber } from '@/lib/number';
import { DevexDataGridEditable } from '@/components/devex-data-grid';
import { Column, Item, Lookup } from 'devextreme-react/data-grid';
import { displayExpr } from '@/lib/utils';

const defaultRow = defaultValuesProjectApproval;

export const ProjectApprovalsForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { control, setValue } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectApprovals', 'id'],
  });
  const { list: agencies } = useEntity({
    queryKey: [QUERIES.AGENCY],
    model: 'agency',
  });

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <p className="col-span-1 font-semibold xl:col-span-24">Thiết lập căn cứ quyết định</p>
        <div className="col-span-1 xl:col-span-24">
          <DevexDataGridEditable
            id={TABLES.PROJECT_APPROVALS}
            dataSource={editableData}
            editing={{
              mode: 'cell',
              allowUpdating: role?.isUpdate,
              allowDeleting: role?.isDelete,
              allowAdding: role?.isCreate,
              useIcons: true,
              confirmDelete: false,
              newRowPosition: 'last',
            }}
            onEditorPreparing={e => {
              if (e.parentType === 'dataRow' && e.dataField === 'approvalContent') {
                e.editorName = 'dxTextArea';
              }
            }}
            onEditorPrepared={e => {
              if (e.parentType === 'dataRow') {
                const element = e.editorElement.querySelector(
                  '.dx-texteditor-input'
                ) as HTMLTextAreaElement;
                if (element) {
                  if (e.dataField === 'approvalContent') {
                    element.style.lineHeight = '1';
                    element.style.minHeight = '200px';

                    // // Auto resize function
                    // const autoResize = () => {
                    //   element.style.height = 'auto';
                    //   element.style.height = element.scrollHeight + 'px';
                    //   const overlay = document.querySelector(
                    //     '.dx-datagrid-focus-overlay'
                    //   ) as HTMLDivElement;
                    //   if (overlay) {
                    //     overlay.style.height = element.scrollHeight + 4 + 'px';
                    //   }
                    // };

                    // // Gọi khi load lần đầu
                    // autoResize();

                    // // Gọi khi người dùng gõ
                    // element.addEventListener('input', autoResize);
                  }
                }
              }
            }}
            onRowInserting={e => {
              e.data = {
                ...(e.data as ProjectApproval),
                id: -getRandomNumber(),
                projectId,
              };
            }}
            onSavedCustom={dataSource => {
              setValue('projectApprovals', dataSource as ProjectApproval[]);
            }}
            onInitNewRow={e => {
              e.data = { ...defaultRow, projectId };
            }}
            customToolbar={
              <>
                <Item name="addRowButton" />
              </>
            }
          >
            <Column
              dataField="approvalDate"
              caption={t('fields.projectApprovals.approvalDate')}
              dataType="date"
            />
            <Column
              dataField="approvalNumber"
              caption={t('fields.projectApprovals.approvalNumber')}
            />
            <Column dataField="agencyId" caption={t('fields.projectApprovals.agencyId')}>
              <Lookup dataSource={agencies} valueExpr={'id'} displayExpr={displayExpr(['name'])} />
            </Column>
            <Column
              dataField="approvalContent"
              caption={t('fields.projectApprovals.approvalContent')}
              cssClass="whitespace-pre-wrap break-words"
              // showEditorAlways
            />
          </DevexDataGridEditable>
        </div>
      </div>
    </PageLayout>
  );
};
