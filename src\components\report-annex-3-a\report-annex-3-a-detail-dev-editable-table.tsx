import { ErrorMessage } from '@/components/ui/error-message';
import { DEFAULT_DECIMAL, DEFAULT_DECIMAL_SCALE, getDataLabel, QUERIES, TABLES } from '@/constant';
import {
  ConstructionTask,
  defaultValuesReportAnnex3a,
  GetReportAnnex3a,
  GetReportAnnex3aContractor,
  IUserPermission,
  ReportAnnex3a,
  ReportAnnex3aDetail,
  Unit,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { useEntity, useScreenSize } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import notification from '@/lib/notifications';
import { getRandomNumber } from '@/lib/number';
import { getRequest, Model } from '@/services';
import { useQuery } from '@tanstack/react-query';
import { Button, NumberBox } from 'devextreme-react';
import { useCallback, useMemo } from 'react';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  Scrolling,
  Paging,
  Pager,
  Editing,
  Toolbar,
  Item,
  type DataGridTypes,
} from 'devextreme-react/data-grid';
import { customizeNumberCell, onCellPrepared } from '../devex-data-grid';
import { InputNumber } from '../ui/input';
import { SelectBoxTypes } from 'devextreme-react/cjs/select-box';
import { FormCombobox } from '../ui/form';
import { cn } from '@/lib/utils';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';

const [defaultRow] = defaultValuesReportAnnex3a.reportAnnex3aDetails;

type ReportAnnex3aDetailEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  isForFinance?: boolean;
};

const t = translationWithNamespace('reportAnnex3A');

const columnNameAlias = [
  'contractedOrEstimatedQuantity_',
  'cumulativePreviousPeriod_',
  'currentPeriodExecution_',
  'accumulatedUpToTheEndOfThisPeriod_',
  'contractedOrEstimatedQuantityTotalAmount_',
  'cumulativePreviousPeriodTotalAmount_',
  'currentPeriodExecutionTotalAmount_',
  'accumulatedUpToTheEndOfThisPeriodTotalAmount_',
];

export const ReportAnnex3aDetailDevEditableTable = ({
  role,
  calculateForm,
  isForFinance = false,
}: ReportAnnex3aDetailEditableTableProps) => {
  const {
    setValue,
    getValues,
    control,
    formState: { errors },
  } = useFormContext<ReportAnnex3a>();

  let model: Model = 'report-annex-3a';
  if (isForFinance) {
    model = 'report-annex-3a-finance';
  }

  const { height } = useScreenSize();

  const [editableData, completionAcceptanceId] = useWatch({
    control,
    name: ['reportAnnex3aDetailsLocal', 'completionAcceptanceId'],
  });

  const { data: contractors = [] } = useQuery({
    queryKey: [QUERIES.REPORT_ANNEX_3A_CONTRACTOR, completionAcceptanceId],
    queryFn: () => {
      return getRequest<GetReportAnnex3aContractor[]>(
        `/${model}/get-report-annex-3-a-contractor/${completionAcceptanceId || '0'}`
      );
    },
    enabled: !isNaN(Number(completionAcceptanceId)),
  });

  const calculateRow = useCallback(
    (
      row: ReportAnnex3aDetail & Record<string, number | string | null>,
      column: string,
      value: number
    ): Record<string, string | number | null | undefined> => {
      const newRow = { ...row };

      if (column === 'contractedOrEstimatedPaymentPrice') {
        contractors.forEach(item => {
          newRow[`contractedOrEstimatedQuantityTotalAmount_${item.contractorId}`] =
            Number(value) *
            Number(newRow[`contractedOrEstimatedQuantity_${item.contractorId}`] || 0);
          newRow[`cumulativePreviousPeriodTotalAmount_${item.contractorId}`] =
            Number(value) * Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0);
          newRow[`currentPeriodExecutionTotalAmount_${item.contractorId}`] =
            Number(value) * Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0);
          newRow[`accumulatedUpToTheEndOfThisPeriodTotalAmount_${item.contractorId}`] =
            Number(value) *
            (Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
              Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0));
          newRow[`accumulatedUpToTheEndOfThisPeriod_${item.contractorId}`] =
            Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
            Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0);
        });
      }

      if (column.startsWith('contractedOrEstimatedQuantity')) {
        contractors.forEach(item => {
          newRow[`contractedOrEstimatedQuantityTotalAmount_${item.contractorId}`] =
            Number(value) * Number(newRow.contractedOrEstimatedPaymentPrice || 0);
        });
      }
      if (column.startsWith('cumulativePreviousPeriod')) {
        contractors.forEach(item => {
          newRow[`cumulativePreviousPeriodTotalAmount_${item.contractorId}`] =
            Number(value) * Number(newRow.contractedOrEstimatedPaymentPrice || 0);
          newRow[`accumulatedUpToTheEndOfThisPeriodTotalAmount_${item.contractorId}`] =
            Number(value) *
            (Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
              Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0));
          newRow[`accumulatedUpToTheEndOfThisPeriod_${item.contractorId}`] =
            Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
            Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0);
        });
      }
      if (column.startsWith('currentPeriodExecution')) {
        contractors.forEach(item => {
          newRow[`currentPeriodExecutionTotalAmount_${item.contractorId}`] =
            Number(value) * Number(newRow.contractedOrEstimatedPaymentPrice || 0);
          newRow[`accumulatedUpToTheEndOfThisPeriodTotalAmount_${item.contractorId}`] =
            Number(value) *
            (Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
              Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0));
          newRow[`accumulatedUpToTheEndOfThisPeriod_${item.contractorId}`] =
            Number(newRow[`cumulativePreviousPeriod_${item.contractorId}`] || 0) +
            Number(newRow[`currentPeriodExecution_${item.contractorId}`] || 0);
        });
      }
      return newRow;
    },
    [contractors]
  );

  const handleCellValueChange = useCallback(
    (cell: DataGridTypes.ColumnEditCellTemplateData, value: number | undefined) => {
      if (value === undefined) return;

      const newRowValue = calculateRow(
        cell.data as ReportAnnex3aDetail & Record<string, number | string | null>,
        cell.column.dataField as string,
        value
      );
      newRowValue[cell.column.dataField as string] = value;

      const allRows = getValues('reportAnnex3aDetailsLocal');
      const updatedData = [...(allRows || [])];
      updatedData[cell.rowIndex] = newRowValue;

      setValue('reportAnnex3aDetailsLocal', updatedData, { shouldDirty: true });
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      cell.setValue(value);

      calculateForm?.();
    },
    [calculateRow, getValues, setValue, calculateForm]
  );

  const { list: constructionTasks } = useEntity<ConstructionTask>({
    model: 'construction-task',
    queryKey: [QUERIES.CONSTRUCTION_TASK],
  });

  const { list: units } = useEntity<Unit>({
    queryKey: [QUERIES.UNIT],
    model: 'unit',
  });

  //
  const defaultColumns = useMemo(() => {
    // Thêm thuộc tính mới vào defaultRow bằng cách loop qua mảng `columnNameAlias`,
    // sử dụng contractor_id để vào prefix được khai báo trong mảng `columnNameAlias`
    const newDefaultRow: any = {
      ...defaultRow,
      contractorId: contractors.length > 0 ? contractors[0].contractorId : 0,
    };
    columnNameAlias.forEach((item: string) => {
      contractors.forEach(contractor => {
        newDefaultRow[`${item}${contractor.contractorId.toString()}`] = 0;
      });
    });
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return newDefaultRow;
  }, [contractors]);

  return (
    <div>
      <DataGrid
        id={TABLES.REPORT_ANNEX_3A_DETAIL}
        keyExpr={'id'}
        dataSource={editableData}
        columnAutoWidth
        allowColumnResizing
        columnResizingMode="widget"
        allowColumnReordering
        showBorders
        showColumnLines
        showRowLines
        wordWrapEnabled
        hoverStateEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onInitNewRow={e => {
          e.data = { ...defaultColumns, id: -getRandomNumber() };
        }}
        repaintChangesOnly
        className={cn(
          'column-header-wrap',
          'max-h-[calc(100vh-9.8rem-32.8px)]',
          height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
        )}
        onRowInserted={e => {
          const allRows = getValues('reportAnnex3aDetailsLocal') || [];
          setValue('reportAnnex3aDetailsLocal', [...allRows, e.data], { shouldDirty: true });
          calculateForm?.();
        }}
        onRowUpdated={e => {
          const allRows = getValues('reportAnnex3aDetailsLocal') || [];
          const index = allRows.findIndex(item => item.id === e.key);
          if (index > -1) {
            allRows[index] = { ...allRows[index], ...e.data };
            setValue('reportAnnex3aDetailsLocal', allRows, { shouldDirty: true });
            calculateForm?.();
          }
        }}
        onRowRemoved={e => {
          const allRows = getValues('reportAnnex3aDetailsLocal') || [];
          const newRows = allRows.filter(item => item.id !== e.key);
          setValue('reportAnnex3aDetailsLocal', newRows, { shouldDirty: true });
          calculateForm?.();
        }}
        onCellPrepared={onCellPrepared}
      >
        <Editing
          mode="cell"
          allowUpdating={!isForFinance && (role?.isCreate || role?.isUpdate)}
          allowDeleting={!isForFinance && (role?.isCreate || role?.isUpdate)}
          allowAdding={!isForFinance && (role?.isCreate || role?.isUpdate)}
          confirmDelete={false}
          useIcons
          newRowPosition="last"
        />
        <ColumnChooser enabled mode="select" height="45rem">
          <ColumnChooserSearch enabled />
          <ColumnChooserSelection allowSelectAll selectByClick recursive />
        </ColumnChooser>
        <Scrolling mode="standard" rowRenderingMode="standard" />
        <Paging enabled defaultPageSize={10} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={[5, 10, 50, 100]}
        />
        <Column
          dataField="serialNumber"
          caption="STT"
          dataType="number"
          format={',##0,##'}
          alignment="center"
          width={60}
          allowEditing={false}
          allowFiltering={false}
          allowSorting={false}
          fixedPosition="left"
          cellRender={(cellInfo: {
            column: dxDataGridColumn;
            columnIndex: number;
            component: dxDataGrid;
            data: Record<string, any>;
            displayValue: any;
            oldValue: any;
            row: dxDataGridRowObject;
            rowIndex: number;
            rowType: string;
            text: string;
            value: any;
            watch: () => void;
          }) => {
            if (cellInfo.rowType === 'data') {
              const pageIndex = cellInfo.component.pageIndex();
              const pageSize = cellInfo.component.pageSize();
              const visibleRowIndex = cellInfo.component
                .getVisibleRows()
                .filter(item => item.rowType === 'data')
                .indexOf(cellInfo.row);
              return pageIndex * pageSize + visibleRowIndex + 1;
            }
          }}
        />
        {/* Tên công việc */}
        <Column
          dataField={'constructionTaskId'}
          caption={t('fields.reportAnnex3aDetails.constructionTaskId')}
          lookup={{ dataSource: constructionTasks, displayExpr: 'name', valueExpr: 'id' }}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            const onValueChanged = (e: SelectBoxTypes.ValueChangedEvent) => {
              // eslint-disable-next-line @typescript-eslint/no-unsafe-call
              cell.setValue(e);
            };
            return (
              <FormCombobox<ConstructionTask>
                defaultValue={cell.value}
                value={cell.value}
                model="construction-task"
                queryKey={[QUERIES.CONSTRUCTION_TASK]}
                defaultText={cell.displayValue || cell.value}
                className={cn('rounded-none border-none disabled:opacity-100')}
                onChange={onValueChanged}
                isWrap={true}
                isTable
                disabled={isForFinance}
              />
            );
          }}
        />
        {/* Đơn vị tính */}
        <Column
          dataField={'unitId'}
          caption={t('fields.reportAnnex3aDetails.unitId')}
          lookup={{ dataSource: units, displayExpr: 'name', valueExpr: 'id' }}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            const onValueChanged = (e: SelectBoxTypes.ValueChangedEvent) => {
              // eslint-disable-next-line @typescript-eslint/no-unsafe-call
              cell.setValue(e);
            };
            return (
              <FormCombobox<Unit>
                defaultValue={cell.value}
                value={cell.value}
                model="unit"
                queryKey={[QUERIES.UNIT]}
                defaultText={cell.displayValue || cell.value}
                // className={cn('rounded-none border-none disabled:opacity-100')}
                onChange={onValueChanged}
                disabled={isForFinance}
              />
            );
          }}
        />
        {/* Đơn giá thanh toán theo hợp đồng hoặc dự án */}
        <Column
          dataField={'contractedOrEstimatedPaymentPrice'}
          caption={t('fields.reportAnnex3aDetails.contractedOrEstimatedPaymentPrice')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={NumberBox}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                readOnly={isForFinance}
                isMoney
                onChange={value => handleCellValueChange(cell, value)}
              />
            );
          }}
        />
        {/* Khối lượng theo hợp đồng hoặc dự toán */}
        <Column caption={t('fields.reportAnnex3aDetails.contractedOrEstimatedQuantity')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`contractedOrEstimatedQuantity_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(DEFAULT_DECIMAL_SCALE)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL_SCALE}
                        readOnly={isForFinance}
                        onChange={value => handleCellValueChange(cell, value)}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Lũy kế đến hết kỳ trước */}
        <Column caption={t('fields.reportAnnex3aDetails.cumulativePreviousPeriod')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`cumulativePreviousPeriod_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(DEFAULT_DECIMAL_SCALE)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL_SCALE}
                        readOnly={isForFinance}
                        onChange={value => handleCellValueChange(cell, value)}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Thực hiện kỳ này */}
        <Column caption={t('fields.reportAnnex3aDetails.currentPeriodExecution')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`currentPeriodExecution_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(DEFAULT_DECIMAL_SCALE)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL_SCALE}
                        readOnly={isForFinance}
                        onChange={value => handleCellValueChange(cell, value)}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Lũy kế đến hết kỳ này */}
        <Column caption={t('fields.reportAnnex3aDetails.accumulatedUpToTheEndOfThisPeriod')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`accumulatedUpToTheEndOfThisPeriod_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(DEFAULT_DECIMAL_SCALE)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL_SCALE}
                        readOnly={isForFinance}
                        onChange={value => {
                          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                          cell.setValue(value as number);
                        }}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Khối lượng theo hợp đồng hoặc dự toán */}
        <Column caption={t('fields.reportAnnex3aDetails.contractedOrEstimatedQuantityTotalAmount')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`contractedOrEstimatedQuantityTotalAmount_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(0)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL}
                        readOnly={isForFinance}
                        isMoney
                        onChange={value => {
                          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                          cell.setValue(value as number);
                        }}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Thực hiện kỳ trước */}
        <Column caption={t('fields.reportAnnex3aDetails.cumulativePreviousPeriodTotalAmount')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`cumulativePreviousPeriodTotalAmount_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(0)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL}
                        readOnly={isForFinance}
                        isMoney
                        onChange={value => {
                          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                          cell.setValue(value as number);
                        }}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Thực hiện kỳ này */}
        <Column caption={t('fields.reportAnnex3aDetails.currentPeriodExecutionTotalAmount')}>
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`currentPeriodExecutionTotalAmount_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(0)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL}
                        readOnly={isForFinance}
                        isMoney
                        onChange={value => {
                          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                          cell.setValue(value as number);
                        }}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Lũy kế đến hết kỳ này */}
        <Column
          caption={t('fields.reportAnnex3aDetails.accumulatedUpToTheEndOfThisPeriodTotalAmount')}
        >
          {contractors.length > 0 &&
            contractors.map(item => {
              return (
                <Column
                  dataField={`accumulatedUpToTheEndOfThisPeriodTotalAmount_${item.contractorId}`}
                  caption={item.name || ''}
                  customizeText={customizeNumberCell(0)}
                  editCellComponent={InputNumber}
                  editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
                    return (
                      <InputNumber
                        defaultValue={cell?.value}
                        value={cell?.value}
                        decimal={DEFAULT_DECIMAL}
                        isMoney
                        readOnly={isForFinance}
                        onChange={value => {
                          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                          cell.setValue(value as number);
                        }}
                      />
                    );
                  }}
                />
              );
            })}
        </Column>
        {/* Ghi chú */}
        <Column dataField={'note'} caption={t('fields.reportAnnex3aDetails.note')} />
        <Toolbar>
          {errors.reportAnnex3aDetails?.message && (
            <ErrorMessage message={errors.reportAnnex3aDetails?.message} />
          )}
          <Item location="before">
            {!isForFinance && (
              <Button
                icon="search"
                type="default"
                onClick={() => {
                  if (!completionAcceptanceId) {
                    notification.warning(t('page.notification.warning.completionAcceptanceId'));
                    return;
                  }
                  getRequest<GetReportAnnex3a[]>(
                    `/${model}/get-report-annex-3-a/${Number(completionAcceptanceId)}`
                  )
                    .then(response => {
                      if (!response) return;
                      const groupedData = response.reduce<
                        Record<string, ReportAnnex3aDetail & Record<string, number | string | null>>
                      >((acc, item) => {
                        const key = item.constructionTaskCode!;

                        if (!acc[key]) {
                          acc[key] = {
                            contractedOrEstimatedPaymentPrice: 0,
                            contractedorestimatedquantity: 0,
                            cumulativePreviousPeriod: 0,
                            currentPeriodExecution: 0,
                            contractedOrEstimatedQuantity: 0,
                            id: -getRandomNumber(),
                            reportAnnex3aId: 0,
                            note: '',
                            contractorId: item.contractorId,
                            constructionTaskId: item.constructionTaskId,
                            constructionTaskCode: item.constructionTaskCode,
                            constructionTaskName: item.constructionTaskName,
                            unitId: item.unitId,
                            unitName: item.unitName,
                          };
                        }

                        // Thêm các trường động theo contractorId
                        const contractorKey = item.contractorId;
                        acc[key][`contractedOrEstimatedPaymentPrice`] =
                          item.contractedOrEstimatedPaymentPrice;
                        acc[key][`contractedOrEstimatedQuantity_${contractorKey}`] =
                          item.contractedOrEstimatedQuantity;
                        acc[key][`currentPeriodExecution_${contractorKey}`] =
                          item.currentPeriodExecution;
                        acc[key][`cumulativePreviousPeriod_${contractorKey}`] =
                          item.cumulativePreviousPeriod;
                        acc[key][`accumulatedUpToTheEndOfThisPeriod_${contractorKey}`] =
                          Number(item.currentPeriodExecution) +
                          Number(item.cumulativePreviousPeriod);

                        acc[key][`contractedOrEstimatedQuantityTotalAmount_${contractorKey}`] =
                          Number(item.contractedOrEstimatedQuantity) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`currentPeriodExecutionTotalAmount_${contractorKey}`] =
                          Number(item.currentPeriodExecution) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`cumulativePreviousPeriodTotalAmount_${contractorKey}`] =
                          Number(item.cumulativePreviousPeriod) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`accumulatedUpToTheEndOfThisPeriodTotalAmount_${contractorKey}`] =
                          (Number(item.currentPeriodExecution) +
                            Number(item.cumulativePreviousPeriod)) *
                          Number(item.contractedOrEstimatedPaymentPrice);

                        return acc;
                      }, {});
                      const result = Object.values(groupedData);
                      setValue('reportAnnex3aDetailsLocal', result);
                    })
                    .catch(error => {
                      console.error('error:', error);
                    });
                }}
                text={getDataLabel}
              />
            )}
            {/* <DxButton
              stylingMode="text"
              icon="download"
              text={downloadTemplateLabel}
              type="default"
              onClick={() => {
                window.open(`/templates/contractor-selection-plan/mau_import_hop_dong.xlsx`);
              }}
            /> */}
          </Item>
          <Item location="after" name="addRowButton" />
          <Item location="after" name="columnChooserButton" />
        </Toolbar>
      </DataGrid>
    </div>
  );
};
