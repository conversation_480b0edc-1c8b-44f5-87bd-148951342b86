import { MONEY_FORMAT, PERCENT_FORMAT_WITHOUT_SUFFIX, QUANTITY_FORMAT } from '@/constant';
import { IColumnProps } from 'devextreme-react/data-grid';

export const optionsQuantity: Partial<IColumnProps> = {
  dataType: 'number',
  // editCellRender: CellRendererNumber,
  format: QUANTITY_FORMAT,
  editorOptions: {
    format: QUANTITY_FORMAT,
  },
};
export const optionsPercent: Partial<IColumnProps> = {
  dataType: 'number',
  // editCellRender: CellRendererNumber,
  format: PERCENT_FORMAT_WITHOUT_SUFFIX,
  editorOptions: {
    format: PERCENT_FORMAT_WITHOUT_SUFFIX,
  },
};
export const optionsMoney: Partial<IColumnProps> = {
  dataType: 'number',
  // editCellRender: CellRendererMoney,
  format: MONEY_FORMAT,
  editorOptions: {
    format: MONEY_FORMAT,
  },
};
