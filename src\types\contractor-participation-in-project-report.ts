import { z } from 'zod';

export const contractorParticipationInProjectReportSchema = z.object({
  id: z.number(), // Id
  ordinalNumber: z.number(), // Stt

  projectId: z.number(), // Id dự án
  projectCode: z.string(), // Mã dự án
  projectName: z.string(), // Tên dự án

  contractorId: z.number(), // Id nhà thầu
  contractorCode: z.string(), // Mã nhà thầu
  contractorName: z.string(), // Tên nhà thầu

  employeeNameKeyPersonnel: z.string(), // Nhân sự chủ chốt

  projectStatusName: z.string(), // Tình trạng dự án
});

export type ContractorParticipationInProjectReport = z.infer<
  typeof contractorParticipationInProjectReportSchema
>;
