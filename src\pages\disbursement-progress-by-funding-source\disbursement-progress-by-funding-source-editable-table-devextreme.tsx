import {
  IMPLEMENTATION_STEPS_TYPES,
  MONEY_FORMAT,
  MUTATE,
  PERCENT_FORMAT_WITHOUT_SUFFIX,
  TABLES,
} from '@/constant';
import {
  DisbursementProgressByFundingSource,
  IUserPermission,
  DisbursementProgressByFundingSourceDetail,
  defaultValuesDisbursementProgressByFundingSourceDetail,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';
import { useCallback, useMemo } from 'react';

import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import 'dayjs/locale/vi';
import { Button } from 'devextreme-react';
import { useMutation } from '@tanstack/react-query';
import axiosInstance, { request } from '@/axios-instance';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toRoman } from '@/lib/numberToText';
import { DevexDataGrid } from '@/components/devex-data-grid';
import type { Column } from 'devextreme/ui/data_grid';
import { DataGridRef } from 'devextreme-react/cjs/data-grid';

dayjs.extend(weekOfYear);
dayjs.extend(localizedFormat);
dayjs.locale('vi');

const defaultRow = defaultValuesDisbursementProgressByFundingSourceDetail;

type DisbursementProgressByFundingSourceEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  setInstance: (ref: DataGridRef<any, any> | null) => void;
};

const tcommon = translationWithNamespace('common');

export const DisbursementProgressByFundingSourceEditableTableDevextreme = ({
  role,
  setInstance,
}: DisbursementProgressByFundingSourceEditableTableProps) => {
  const { id: editId } = useParams();
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const { setValue, control, watch, getValues } =
    useFormContext<DisbursementProgressByFundingSource>();

  const fiscalYearValue = watch('fiscalYear');
  const { t: translator } = useTranslation('disbursementProgressByFundingSource');
  const t = useCallback(
    (fieldName: string, options?: Record<string, string | number>) => {
      const thisYear = fiscalYearValue?.getFullYear() || new Date().getFullYear();
      const year = thisYear;
      const nextYear = thisYear + 1;
      const previousYear = thisYear - 1;

      return translator(fieldName, {
        year,
        previousYear,
        nextYear,
        ...options,
        interpolation: { escapeValue: false },
      });
    },
    [fiscalYearValue, translator]
  );
  const [editableData] = useWatch({
    control,
    name: ['disbursementProgressByFundingSourceDetails'],
  });

  const createWeekColumns = useMemo(
    () =>
      (weekIndex: number, year: number): Column[] => {
        const weekNum = String(weekIndex).padStart(2, '0');
        const weekKey = `week${weekNum}`;
        const landKey = `landWeek${weekNum}`;

        const startDate = dayjs().year(year).week(weekIndex).day(1); // day(1) là thứ Hai
        const endDate = dayjs().year(year).week(weekIndex).day(6); // day(6) là thứ Bảy
        const displayStartDate =
          startDate.year() === year ? startDate : dayjs().year(year).startOf('year');
        const displayEndDate = endDate.year() === year ? endDate : dayjs().year(year).endOf('year');

        const fromDate = displayStartDate.format('DD/MM');
        const toDate = displayEndDate.format('DD/MM');
        const dateRange = `(${fromDate}-${toDate})`;

        return [
          {
            dataField: weekKey,
            caption: t('fields.disbursementProgressByFundingSourceDetails.weekHeader', {
              weekNum,
              dateRange,
              fromDate,
              toDate,
            }),
            allowEditing: false,
            dataType: 'string',
            // cellTemplate: renderCell,
          },
          {
            dataField: landKey,
            caption: t('fields.disbursementProgressByFundingSourceDetails.landWeekHeader', {
              weekNum,
              dateRange,
              fromDate,
              toDate,
            }),
            allowEditing: false,
            dataType: 'string',
            // cellTemplate: renderCell,
          },
        ];
      },
    [t]
  );
  const { allWeekColumns } = useMemo(() => {
    const calculationYear =
      fiscalYearValue instanceof Date ? dayjs(fiscalYearValue).year() : dayjs().year();

    const allWeekColumns = Array.from({ length: 52 }, (_, i) =>
      createWeekColumns(i + 1, calculationYear)
    ).flat();

    const currentWeek = dayjs().week();
    let startWeek: number;
    let endWeek: number;

    if (currentWeek === 1) {
      startWeek = 1;
      endWeek = 3;
    } else if (currentWeek === 52) {
      startWeek = 50;
      endWeek = 52;
    } else {
      startWeek = currentWeek - 1;
      endWeek = currentWeek + 1;
    }

    const getWeekNumberFromId = (id: string | undefined): number | null => {
      if (!id) return null;
      const match = id.match(/(?:week|landWeek)(\d{2})$/);
      return match ? parseInt(match[1], 10) : null;
    };

    allWeekColumns.forEach(col => {
      if (col.dataField) {
        const weekNum = getWeekNumberFromId(col.dataField);
        const isVisibleByDefault = weekNum !== null && weekNum >= startWeek && weekNum <= endWeek;
        col.visible = isVisibleByDefault;
      }
    });

    return {
      allWeekColumns: [...allWeekColumns],
    };
  }, [fiscalYearValue, createWeekColumns]);

  const allMonthColumns = useMemo(() => {
    const columns: Column[] = [];
    for (let month = 1; month <= 12; month++) {
      const strMonth = String(month).padStart(2, '0');
      const keyMonth = `month${strMonth}`;
      const keyMonthFinance = `month${strMonth}Finance`;

      columns.push({
        dataField: keyMonth,
        caption: t('fields.disbursementProgressByFundingSourceDetails.month', {
          month: month,
        }),
        dataType: 'number',
        format: MONEY_FORMAT,
      });
      columns.push({
        dataField: keyMonthFinance,
        caption: t('fields.disbursementProgressByFundingSourceDetails.monthFinance', {
          month: month,
        }),
        dataType: 'number',
        format: MONEY_FORMAT,
      });

      if (month % 3 == 0 && month < 12) {
        const quater = month / 3;
        const quaterFormat = String(quater).padStart(2, '0');
        const strQuater = toRoman(quater);
        const keyQuater = `disbursementRate${quaterFormat}`;
        const keyQuaterFinance = `disbursementRate${quaterFormat}Finance`;

        columns.push({
          dataField: keyQuater,
          caption: t('fields.disbursementProgressByFundingSourceDetails.quater', {
            quater: strQuater,
          }),
          dataType: 'number',
          format: PERCENT_FORMAT_WITHOUT_SUFFIX,
        });

        columns.push({
          dataField: keyQuaterFinance,
          caption: t('fields.disbursementProgressByFundingSourceDetails.quaterFinance', {
            quater: strQuater,
          }),
          dataType: 'number',
          format: PERCENT_FORMAT_WITHOUT_SUFFIX,
        });
      }
    }
    return columns;
  }, [t]);

  const columns: Column[] = [
    {
      cellTemplate: (container, options) => {
        const pageIndex = options.component.pageIndex();
        const pageSize = options.component.pageSize();
        const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
        container.textContent = serialNumber.toString();
      },
      caption: 'STT',
      fixed: true,
      fixedPosition: 'left',
      alignment: 'center',
      width: 50,
      dataField: 'serialNumber',
      allowSorting: false,
      allowFiltering: false,
      allowEditing: false,
      format: ',##0,##',
    },
    {
      dataField: 'projectCode',
      caption: t('fields.disbursementProgressByFundingSourceDetails.projectCode'),
      allowEditing: false,
      fixed: true,
      fixedPosition: 'left',
    },
    {
      dataField: 'projectName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.projectId'),
      allowEditing: false,
      fixed: true,
      fixedPosition: 'left',
    },
    {
      dataField: 'totalInvestment',
      caption: t('fields.disbursementProgressByFundingSourceDetails.totalInvestment'),
      dataType: 'number',
      format: MONEY_FORMAT,
      allowEditing: false,
    },
    {
      dataField: 'totalFundingRequirementSummary',
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementSummary'
      ),
      alignment: 'center',
      columns: [
        {
          dataField: 'totalFundingRequirement',
          caption: t('fields.disbursementProgressByFundingSourceDetails.totalFundingRequirement'),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'totalFundingRequirementCompensation',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementCompensation'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'totalFundingRequirementConstructionConsulting',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementConstructionConsulting'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
      ],
    },
    {
      dataField: 'cumulativeDisbursementUntilSummary',
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilSummary'
      ),
      alignment: 'center',
      columns: [
        {
          dataField: 'cumulativeDisbursementUntil',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntil'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'cumulativeDisbursementUntilCompensation',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilCompensation'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'cumulativeDisbursementUntilConstructionConsulting',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilConstructionConsulting'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
      ],
    },

    {
      dataField: 'mediumTermCapitalPlan',
      caption: t('fields.disbursementProgressByFundingSourceDetails.mediumTermCapitalPlan'),
      dataType: 'number',
      format: MONEY_FORMAT,
    },
    {
      dataField: 'allocatedCapitalPlan',
      caption: t('fields.disbursementProgressByFundingSourceDetails.allocatedCapitalPlan'),
      dataType: 'number',
      format: MONEY_FORMAT,
      allowEditing: false,
    },
    {
      dataField: 'annualImplementationPlanFinance',
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.annualImplementationPlanFinance'
      ),
      dataType: 'number',
      format: MONEY_FORMAT,
    },
    {
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementYearToDateSummary'
      ),
      alignment: 'center',
      columns: [
        {
          dataField: 'cumulativeDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'compensationDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.compensationDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
        {
          dataField: 'constructionConsultingDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.constructionConsultingDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
        },
      ],
    },
    {
      dataField: 'disbursementRateYearToDate',
      caption: t('fields.disbursementProgressByFundingSourceDetails.disbursementRateYearToDate'),
      dataType: 'number',
      format: PERCENT_FORMAT_WITHOUT_SUFFIX,
      allowEditing: false,
    },
    //months
    ...allMonthColumns,
    {
      dataField: 'plannedCumulativeFullYear',
      caption: t('fields.disbursementProgressByFundingSourceDetails.plannedCumulativeFullYear'),
      dataType: 'number',
      format: MONEY_FORMAT,
    },
    {
      dataField: 'plannedCumulativeFullYearFinance',
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.plannedCumulativeFullYearFinance'
      ),
      dataType: 'number',
      format: MONEY_FORMAT,
    },
    {
      dataField: 'disbursementRateFullYear',
      caption: t('fields.disbursementProgressByFundingSourceDetails.disbursementRateFullYear'),
      dataType: 'number',
      format: PERCENT_FORMAT_WITHOUT_SUFFIX,
    },
    {
      dataField: 'disbursementRateFullYearFinance',
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.disbursementRateFullYearFinance'
      ),
      dataType: 'number',
      format: PERCENT_FORMAT_WITHOUT_SUFFIX,
    },
    {
      dataField: 'constructionPeriod',
      caption: t('fields.disbursementProgressByFundingSourceDetails.constructionPeriod'),
      dataType: 'string',
    },
    //weeks
    ...allWeekColumns,
    {
      dataField: 'issuesAndRecommendations',
      caption: t('fields.disbursementProgressByFundingSourceDetails.issuesAndRecommendations'),
      dataType: 'string',
    },
    {
      dataField: 'deploymentPhaseName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.deploymentPhaseId'),
      allowEditing: false,
    },
    {
      dataField: 'implementationStepsType',
      caption: t('fields.disbursementProgressByFundingSourceDetails.implementationStepsType'),
      lookup: {
        dataSource: IMPLEMENTATION_STEPS_TYPES,
        valueExpr: 'id',
        displayExpr: 'name',
      },
      allowEditing: false,
    },
    {
      dataField: 'completionAcceptanceDate',
      caption: t('fields.disbursementProgressByFundingSourceDetails.completionAcceptanceDate'),
      dataType: 'date',
      allowEditing: false,
    },
    {
      dataField: 'locationMap',
      caption: t('fields.disbursementProgressByFundingSourceDetails.locationMap'),
      dataType: 'string',
      allowEditing: false,
      cellTemplate: (cellElement, cellInfo) => {
        if (cellInfo.value) {
          cellElement.innerHTML = `<a href="${cellInfo.value}" target="_blank" class="hover:text-blue-700 text-blue-600 underline">Bấm vào link</a>`;
        }
      },
    },
    {
      dataField: 'planningInformation',
      caption: t('fields.disbursementProgressByFundingSourceDetails.planningInformation'),
      dataType: 'string',
      allowEditing: false,
    },
    {
      dataField: 'projectManagementDirectorName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.projectManagementDirectorId'),
      allowEditing: false,
    },
    {
      dataField: 'accountantName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.accountantId'),
      allowEditing: false,
    },
    {
      dataField: 'departmentInChargeName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.departmentInChargeId'),
      allowEditing: false,
    },
  ];

  const allowEditColumns = ['mediumTermCapitalPlan', 'annualImplementationPlanFinance'];
  columns.forEach(column => {
    column.allowEditing = allowEditColumns.includes(column.dataField || '');
  });

  const { mutate: mutateDetails, isPending: loadingData } = useMutation({
    mutationKey: [MUTATE.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_GET_REPORT],
    mutationFn: ({
      year,
      budgetFundId,
      disbursementProgressByFundingSourceId,
    }: {
      year: Date;
      budgetFundId: number;
      disbursementProgressByFundingSourceId: number;
    }) => {
      return request<{ items: DisbursementProgressByFundingSourceDetail[] }>(
        axiosInstance.post('/disbursement-progress-by-funding-source/get-report', {
          filterColumn: [],
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'Id',
          sortOrder: 0,
          isPage: false,
          objParam: {
            year: year.toISOString(),
            budgetFundId,
            disbursementProgressByFundingSourceId,
          },
        })
      );
    },
    onSuccess: data => {
      setValue(
        'disbursementProgressByFundingSourceDetails',
        data.items.map(item => ({ ...item, id: -getRandomNumber() }))
      );
    },
  });

  const handleGetData = () => {
    const [year, budgetFundId, disbursementProgressByFundingSourceId] = getValues([
      'fiscalYear',
      'budgetFundId',
      'id',
    ]);
    if (year && budgetFundId) {
      mutateDetails({
        year: year,
        budgetFundId: budgetFundId || 0,
        disbursementProgressByFundingSourceId,
      });
    }
  };

  return (
    <div>
      <>
        <div className="col-span-1 flex justify-end xl:col-span-3 xl:justify-start">
          <Button
            text={tcommon('action.getData')}
            className="w-fit"
            stylingMode="contained"
            type="default"
            icon="search"
            onClick={() => void handleGetData()}
            disabled={loadingData}
            //
          />
        </div>
        <DevexDataGrid
          setRef={setInstance}
          id={`${TABLES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}/${editId}`}
          dataSource={editableData}
          onAddNewClick={() => {
            const newRow = { ...defaultRow, id: -getRandomNumber() };
            setValue('disbursementProgressByFundingSourceDetails', [...editableData, newRow]);
          }}
          columns={columns}
          editing={{
            mode: 'cell',
            allowUpdating: role?.isUpdate,
            allowDeleting: role?.isDelete,
            allowAdding: role?.isCreate,
          }}

          // onRowUpdating={e => {
          //   const updatingRow: DisbursementProgressByFundingSourceDetail = {
          //     ...editableData.find(item => item.id === e.key)!,
          //     ...e.newData,
          //   };
          //   console.log('updatingRow', updatingRow);
          //   const instance = e.component;
          //   void instance
          //     .getDataSource()
          //     .store()
          //     .update(e.key, updatingRow)
          //     .then(void instance.refresh());

          //   const updatedData = editableData.map(item =>
          //     item.id === e.key ? { ...item, ...e.newData } : item
          //   );
          //   void Promise.all(
          //     updatedData.map(u => instance.getDataSource().store().update(u.id, u))
          //   ).then(() => {
          //     void instance.refresh();
          //     // .then(() => setValue('reportPublicInvestmentSettlementDetails', newList)); // Cập nhật giao diện sau khi update
          //   });
          //   // setValue('disbursementProgressByFundingSourceDetails', updatedData);
          // }}
        />
      </>
    </div>
  );
};
