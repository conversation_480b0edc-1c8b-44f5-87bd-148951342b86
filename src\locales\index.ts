import enCommon from './en/common.json';
import enCompany from './en/company.json';
import enNavigation from './en/navigation.json';
import enUser from './en/user.json';

import viAgency from './vi/agency.json';
import viBiddingMethod from './vi/bidding-method.json';
import viBiddingSector from './vi/bidding-sector.json';
import viBranch from './vi/branch.json';
import viBusinessTable from './vi/business-table.json';
import viCareerTraining from './vi/career-training.json';
import viCommon from './vi/common.json';
import viCompany from './vi/company.json';
import viContractTaskManagement from './vi/contract-task-management.json';
import viContractType from './vi/contract-type.json';
import viContractorType from './vi/contractor-type.json';
import viContractor from './vi/contractor.json';
import viCostItemType from './vi/cost-item-type.json';
import viCustomerGroup from './vi/customer-group.json';
import viCustomer from './vi/customer.json';
import viDataTable from './vi/data-table.json';
import viData from './vi/data.json';
import viDepartment from './vi/department.json';
import viDeploymentPhase from './vi/deployment-phase.json';
import viDocumentGroup from './vi/document-group.json';
import viEvaluationResult from './vi/evaluation-result.json';
import viExpertise from './vi/expertise.json';
import viFileType from './vi/file-type.json';
import viForeignLanguage from './vi/foreign-language.json';
import viImport from './vi/import.json';
import viItCourse from './vi/it-course.json';
import viMajor from './vi/major.json';
import viNavigation from './vi/navigation.json';
import viPermissionGroup from './vi/permission-groups.json';
import viPolitics from './vi/politics.json';
import viPosition from './vi/position.json';
import viPrintForm from './vi/print-form.json';
import viProject from './vi/project.json';
import viRecordsAttachment from './vi/records-attachment.json';
import viSalesOrder from './vi/sales-order.json';
import viSignIn from './vi/sign-in.json';
import viStatisticsByReportTemplate from './vi/statistics-by-report-template.json';
import viSupplierGroup from './vi/supplier-group.json';
import viSupplier from './vi/supplier.json';
import viTenderType from './vi/tender-type.json';
import viTrainingManagement from './vi/training-management.json';

import viAccountFund from './vi/account-fund.json';
import viAdjustedInvestment from './vi/adjusted-investment.json';
import viApprovalProcess from './vi/approval-process.json';
import viAssetIncrement from './vi/asset-increment.json';
import viAssetType from './vi/asset-type.json';
import viAsset from './vi/asset.json';
import viBacklogProjectManagement from './vi/backlog-project-management.json';
import viBoardOfDirectorsWorkSchedule from './vi/board-of-directors-work-schedule.json';
import viBorrowDocument from './vi/borrow-document.json';
import viBudgetFund from './vi/budget-fund.json';
import viBudgetSourceCode from './vi/budget-source-code.json';
import viCapitalIncreasePlan from './vi/capital-increase-plan.json';
import viCompletionAcceptance from './vi/completion-acceptance.json';
import viConstructionItem from './vi/construction-item.json';
import viConstructionTask from './vi/construction-task.json';
import viContractAppendixType from './vi/contract-appendix-type.json';
import viContractAppendix from './vi/contract-appendix.json';
import viContract from './vi/contract.json';
import viContractorSelectionPlan from './vi/contractor-selection-plan.json';
import viCorrespondenceType from './vi/correspondence-type.json';
import viCostItem from './vi/cost-item.json';
import viDesignTaskManagement from './vi/design-task-management.json';
import viDirectiveContent from './vi/directive-content.json';
import viDirectiveTask from './vi/directive-task.json';
import viDirectives from './vi/directives.json';
import viDistrict from './vi/district.json';
import viDocumentDecision from './vi/document-decision.json';
import viDocumentFormEntry from './vi/document-form-entry.json';
import viEmployeePayrollReport from './vi/employee-payroll-report.json';
import viEmployeeType from './vi/employee-type.json';
import viEmployee from './vi/employee.json';
import viGenderType from './vi/gender-type.json';
import viGuaranteeLetterTracking from './vi/guarantee-letter-tracking.json';
import viHistoryAction from './vi/history-action.json';
import viInsuranceContributionReport from './vi/insurance-contribution-report.json';
import viInventoryItemType from './vi/inventory-item-type.json';
import viInventoryItem from './vi/inventory-item.json';
import viLeave from './vi/leave.json';
import viOutstandingEquipment from './vi/outstanding-equipment.json';
import viOvertimeAttendanceTracking from './vi/overtime-attendance-tracking.json';
import viOvertimeRegistration from './vi/overtime-registration.json';
import viPaymentBeneficiaryReport from './vi/payment-beneficiary-report.json';
import viProjectGroup from './vi/project-group.json';
import viProjectManagementType from './vi/project-management-type.json';
import viProjectOwner from './vi/project-owner.json';
import viProjectScheduleSetup from './vi/project-schedule-setup.json';
import viProjectStatus from './vi/project-status.json';
import viReportSerialManagement from './vi/report-serial-management.json';
import viReportTemplate from './vi/report-template.json';
import viSalarySheetForLaborContractReport from './vi/salary-sheet-for-labor-contract-report.json';
import viSalarySheet from './vi/salary-sheet.json';
import viSetupAnnualHoliday from './vi/setup-annual-holiday.json';
import viSetupReportTemplate from './vi/setup-report-template.json';
import viStateManagement from './vi/state-management.json';
import viStatus from './vi/status.json';
import viTarget from './vi/target.json';
import viTenderPackage from './vi/tender-package.json';
import viTrainingInstitution from './vi/training-institution.json';
import viUIGenerateTool from './vi/ui-generate-tool.json';
import viUnit from './vi/unit.json';
import viUploadImage from './vi/upload-image.json';
import viUser from './vi/user.json';
import viWard from './vi/ward.json';
import viWarehouse from './vi/warehouse.json';
import viWorkManagementDesignBidEstimation from './vi/work-management-design-bid-estimation.json';
import viWorkManagementDirectiveContent from './vi/work-management-directive-content.json';
import viWorkManagementOther from './vi/work-management-other.json';
import viWorkManagementTarget from './vi/work-management-target.json';
import viWorkManagementTask from './vi/work-management-task.json';
import viWorkManagement from './vi/work-management.json';
import viWorkPosition from './vi/work-position.json';

import viAdjustedCostEstimation from './vi/adjusted-cost-estimation.json';
import viConstructionType from './vi/construction-type.json';
import viFormDocumentManager from './vi/form-document-manager.json';
import viInvestmentForm from './vi/investment-form.json';
import viInvestmentType from './vi/investment-type.json';
import viNotification from './vi/notification.json';
import viProfessionKeyWord from './vi/profession-keyword.json';
import viSpendingCommitment from './vi/spending-commitment.json';

import viAnnualTaskListStatisticsReport from './vi/annual-task-list-statistics-report.json';
import viTargetsAndTasksDirectiveContent from './vi/targets-and-tasks-directive-content.json';

import viPaymentReceipt from './vi/payment-receipt.json';
import viProfessionApprovalProcessApproval from './vi/profession-approval-process-approval.json';
import viProfessionApprovalProcessForward from './vi/profession-approval-process-forward.json';

import viABAdjustmentSettlement from './vi/a-b-adjustment-settlement.json';
import viABSettlement from './vi/a-b-settlement.json';
import viAdvancePayment from './vi/advance-payment.json';
import viBudgetItemCode from './vi/budget-item-code.json';
import viCapitalPlanDisbursementProgressReport from './vi/capital-plan-disbursement-progress-report.json';
import viContractSettlement from './vi/contract-settlement.json';
import viContractorParticipationTenderPackageListReport from './vi/contractor-participation-tender-package-list-report.json';
import viContractorProjectStaffReport from './vi/contractor-project-staff-report.json';
import viContractorSelectionResultByDepartmentReport from './vi/contractor-selection-result-by-department-report.json';
import viContractorSelectionResultByManagingDirectorReport from './vi/contractor-selection-result-by-managing-director-report.json';
import viContractorSelectionResultByYearReport from './vi/contractor-selection-result-by-year-report.json';
import viContractorSelectionResultSummaryByYearReport from './vi/contractor-selection-result-summary-by-year-report.json';
import viContractorSelectionResult from './vi/contractor-selection-result.json';
import viDataReconciliationTable from './vi/data-reconciliation-table.json';
import viDocumentType from './vi/document-type.json';
import viEmployeeAnnualEvaluationResultReport from './vi/employee-annual-evaluation-result-report.json';
import viFundingProgramCode from './vi/funding-program-code.json';
import viMonthlyPlannedDisbursement from './vi/monthly-planned-disbursement.json';
import viProjectDebtStatusStatisticsReport from './vi/project-debt-status-statistics-report.json';
import viProjectDepartmentDisbursementProgressReport from './vi/project-department-disbursement-progress-report.json';
import viProjectDeploymentStatusReport from './vi/project-deployment-status-report.json';
import viProjectDisbursement from './vi/project-disbursement.json';
import viProposedSettlementInvestmentCost from './vi/proposed-settlement-investment-cost.json';
import viReportAnnex3A from './vi/report-annex-3-a.json';
import viReportPublicInvestmentSettlement from './vi/report-public-investment-settlement.json';
import viRptReportOnImplementationOfDirectives from './vi/rpt-report-on-implementation-of-directives.json';
import viSavingInBiddingReport from './vi/saving-in-bidding-report.json';
import viSavingRateContractorSelectionPlanReport from './vi/saving-rate-contractor-selection-plan-report.json';
import viSectorCode from './vi/sector-code.json';
import viSendNotification from './vi/send-notification.json';
import viSummaryTargetsTasksFirst6MonthsReport from './vi/summary-targets-tasks-first-6-months-report.json';
import viSummaryTargetsTasksLast6MonthsReport from './vi/summary-targets-tasks-last-6-months-report.json';
import viTemplateStatisticReport from './vi/template-statistic-report.json';
import viTypeCode from './vi/type-code.json';
import viWeeklyProjectSchedulePlan from './vi/weekly-project-schedule-plan.json';
import viYearlySummaryReport from './vi/yearly-summary-report.json';
import viDirectiveImplementation from './vi/directive-implementation.json';
import viDisbursementProgressByFundingSource from './vi/disbursement-progress-by-funding-source.json';
import viFinancialSettlementReport from './vi/financial-settlement-report.json';
import viRptReportOnImplementationOfDirectivesByUser from './vi/rpt-report-on-implementation-of-directives-by-user.json';
import viRptReportOnImplementationOfDirectivesByUserFinance from './vi/rpt-report-on-implementation-of-directives-by-user-finance.json';
import viDisbursementProgressSummary from './vi/disbursement-progress-summary.json';
import viLandAcquisitionAndCompensationProgressReport from './vi/land-acquisition-and-compensation-progress-report.json';
import viBoardOfDirector from './vi/board-of-director.json';
import viBank from './vi/bank.json';
import viAdjustedCapitalIncreasePlan from './vi/adjusted-capital-increase-plan.json';
import viCompletionAcceptanceNotice from './vi/completion-acceptance-notice.json';
import viProjectSettlement from './vi/project-settlement.json';
import viTrainingTracking from './vi/training-tracking.json';
import viContractorParticipationInProjectReport from './vi/contractor-participation-in-project-report.json';
import viAiWarningCostOverrun from './vi/ai-warning-cost-overrun.json';
import viAiWarningOfConstructionProgress from './vi/ai-warning-of-construction-progress.json';
import viContractorSelectionPlanTaskManagement from './vi/contractor-selection-plan-task-management.json';
import viProjectInformationSummaryReport from './vi/project-information-summary-report.json';
import viCompletionReport from './vi/completion-report.json';
import viEmploymentAcceptanceNotice from './vi/employment-acceptance-notice.json';
import viComprehensiveInvestmentReport from './vi/comprehensive-investment-report.json';

const locales = {
  en: {
    common: enCommon,
    navigation: enNavigation,
    company: enCompany,
    user: enUser,
  },
  vi: {
    trainingTracking: viTrainingTracking,
    common: viCommon,
    signIn: viSignIn,
    navigation: viNavigation,
    company: viCompany,
    user: viUser,
    uploadImage: viUploadImage,
    dataTable: viDataTable,
    data: viData,
    supplierGroup: viSupplierGroup,
    supplier: viSupplier,
    customerGroup: viCustomerGroup,
    customer: viCustomer,
    warehouse: viWarehouse,
    businessTable: viBusinessTable,

    branch: viBranch,
    salesOrder: viSalesOrder,
    permissionGroup: viPermissionGroup,
    printForm: viPrintForm,
    projectGroup: viProjectGroup,
    projectOwner: viProjectOwner,
    contractor: viContractor,
    recordsAttachment: viRecordsAttachment,
    agency: viAgency,
    deploymentPhase: viDeploymentPhase,
    contractorType: viContractorType,
    tenderType: viTenderType,
    biddingSector: viBiddingSector,
    biddingMethod: viBiddingMethod,
    contractType: viContractType,
    costItemType: viCostItemType,
    documentGroup: viDocumentGroup,
    fileType: viFileType,
    department: viDepartment,
    position: viPosition,
    import: viImport,
    workPosition: viWorkPosition,
    evaluationResult: viEvaluationResult,
    expertise: viExpertise,
    major: viMajor,
    politics: viPolitics,
    careerTraining: viCareerTraining,
    itCourse: viItCourse,
    foreignLanguage: viForeignLanguage,
    statisticsByReportTemplate: viStatisticsByReportTemplate,
    correspondenceType: viCorrespondenceType,
    genderType: viGenderType,
    inventoryItem: viInventoryItem,
    trainingInstitution: viTrainingInstitution,
    district: viDistrict,
    inventoryItemType: viInventoryItemType,
    projectStatus: viProjectStatus,
    ward: viWard,
    contractTaskManagement: viContractTaskManagement,
    trainingManagement: viTrainingManagement,
    projectManagementType: viProjectManagementType,
    setupReportTemplate: viSetupReportTemplate,
    budgetFund: viBudgetFund,
    budgetSourceCode: viBudgetSourceCode,
    costItem: viCostItem,
    constructionItem: viConstructionItem,
    constructionTask: viConstructionTask,
    accountFund: viAccountFund,
    employeeType: viEmployeeType,
    contractAppendixType: viContractAppendixType,
    unit: viUnit,
    stateManagement: viStateManagement,
    status: viStatus,
    contractorSelectionPlan: viContractorSelectionPlan,
    tenderPackage: viTenderPackage,
    project: viProject,
    contract: viContract,
    contractAppendix: viContractAppendix,
    completionAcceptance: viCompletionAcceptance,
    leave: viLeave,
    boardOfDirectorsWorkSchedule: viBoardOfDirectorsWorkSchedule,
    employee: viEmployee,
    borrowDocument: viBorrowDocument,
    overtimeRegistration: viOvertimeRegistration,
    directives: viDirectives,
    directiveTask: viDirectiveTask,
    salarySheet: viSalarySheet,
    directiveContent: viDirectiveContent,
    target: viTarget,
    reportTemplate: viReportTemplate,
    workManagementDirectiveContent: viWorkManagementDirectiveContent,
    workManagementTask: viWorkManagementTask,
    workManagement: viWorkManagement,
    workManagementTarget: viWorkManagementTarget,
    insuranceContributionReport: viInsuranceContributionReport,
    workManagementDesignBidEstimation: viWorkManagementDesignBidEstimation,
    paymentBeneficiaryReport: viPaymentBeneficiaryReport,
    documentFormEntry: viDocumentFormEntry,
    employeePayrollReport: viEmployeePayrollReport,
    approvalProcess: viApprovalProcess,
    reportSerialManagement: viReportSerialManagement,
    salarySheetForLaborContractReport: viSalarySheetForLaborContractReport,
    designTaskManagement: viDesignTaskManagement,
    setupAnnualHoliday: viSetupAnnualHoliday,
    uiGenerateTool: viUIGenerateTool,
    workManagementOther: viWorkManagementOther,
    guaranteeLetterTracking: viGuaranteeLetterTracking,
    overtimeAttendanceTracking: viOvertimeAttendanceTracking,
    outstandingEquipment: viOutstandingEquipment,
    assetType: viAssetType,
    asset: viAsset,
    documentDecision: viDocumentDecision,
    capitalIncreasePlan: viCapitalIncreasePlan,
    adjustedInvestment: viAdjustedInvestment,
    projectScheduleSetup: viProjectScheduleSetup,
    backlogProjectManagement: viBacklogProjectManagement,
    assetIncrement: viAssetIncrement,
    historyAction: viHistoryAction,
    formDocumentManager: viFormDocumentManager,
    spendingCommitment: viSpendingCommitment,
    adjustedCostEstimation: viAdjustedCostEstimation,
    professionKeyWord: viProfessionKeyWord,
    investmentForm: viInvestmentForm,
    investmentType: viInvestmentType,
    constructionType: viConstructionType,
    paymentReceipt: viPaymentReceipt,
    notification: viNotification,
    professionApprovalProcessForward: viProfessionApprovalProcessForward,
    professionApprovalProcessApproval: viProfessionApprovalProcessApproval,
    annualTaskListStatisticsReport: viAnnualTaskListStatisticsReport,
    targetsAndTasksDirectiveContent: viTargetsAndTasksDirectiveContent,
    yearlySummaryReport: viYearlySummaryReport,
    summaryTargetsTasksFirst6MonthsReport: viSummaryTargetsTasksFirst6MonthsReport,
    summaryTargetsTasksLast6MonthsReport: viSummaryTargetsTasksLast6MonthsReport,
    sendNotification: viSendNotification,
    contractorSelectionResult: viContractorSelectionResult,
    rptReportOnImplementationOfDirectives: viRptReportOnImplementationOfDirectives,
    aBSettlement: viABSettlement,
    aBAdjustmentSettlement: viABAdjustmentSettlement,
    reportAnnex3A: viReportAnnex3A,
    contractorSelectionResultByManagingDirectorReport:
      viContractorSelectionResultByManagingDirectorReport,
    contractorSelectionResultByDepartmentReport: viContractorSelectionResultByDepartmentReport,
    projectDisbursement: viProjectDisbursement,
    proposedSettlementInvestmentCost: viProposedSettlementInvestmentCost,
    advancePayment: viAdvancePayment,
    projectDebtStatusStatisticsReport: viProjectDebtStatusStatisticsReport,
    contractSettlement: viContractSettlement,
    templateStatisticsReport: viTemplateStatisticReport,
    dataReconciliationTable: viDataReconciliationTable,
    savingRateContractorSelectionPlanReport: viSavingRateContractorSelectionPlanReport,
    contractorParticipationTenderPackageListReport:
      viContractorParticipationTenderPackageListReport,
    capitalPlanDisbursementProgressReport: viCapitalPlanDisbursementProgressReport,
    projectDepartmentDisbursementProgressReport: viProjectDepartmentDisbursementProgressReport,
    savingInBiddingReport: viSavingInBiddingReport,
    reportPublicInvestmentSettlement: viReportPublicInvestmentSettlement,
    sectorCode: viSectorCode,
    fundingProgramCode: viFundingProgramCode,
    typeCode: viTypeCode,
    budgetItemCode: viBudgetItemCode,
    documentType: viDocumentType,
    employeeAnnualEvaluationResultReport: viEmployeeAnnualEvaluationResultReport,
    contractorSelectionResultByYearReport: viContractorSelectionResultByYearReport,
    annualBiddingSummaryReport: viContractorSelectionResultSummaryByYearReport,
    contractorProjectStaffReport: viContractorProjectStaffReport,
    weeklyProjectSchedulePlan: viWeeklyProjectSchedulePlan,
    monthlyPlannedDisbursement: viMonthlyPlannedDisbursement,
    projectDeploymentStatusReport: viProjectDeploymentStatusReport,
    disbursementProgressByFundingSource: viDisbursementProgressByFundingSource,
    directiveImplementation: viDirectiveImplementation,
    financialSettlementReport: viFinancialSettlementReport,
    rptReportOnImplementationOfDirectivesByUser: viRptReportOnImplementationOfDirectivesByUser,
    rptReportOnImplementationOfDirectivesByUserFinance:
      viRptReportOnImplementationOfDirectivesByUserFinance,
    disbursementProgressSummary: viDisbursementProgressSummary,
    landAcquisitionAndCompensationProgressReport: viLandAcquisitionAndCompensationProgressReport,
    boardOfDirector: viBoardOfDirector,
    bank: viBank,
    adjustedCapitalIncreasePlan: viAdjustedCapitalIncreasePlan,
    completionAcceptanceNotice: viCompletionAcceptanceNotice,
    projectSettlement: viProjectSettlement,
    contractorParticipationInProjectReport: viContractorParticipationInProjectReport,
    aiWarningCostOverrun: viAiWarningCostOverrun,
    aiWarningOfConstructionProgress: viAiWarningOfConstructionProgress,
    contractorSelectionPlanTaskManagement: viContractorSelectionPlanTaskManagement,
    projectInformationSummaryReport: viProjectInformationSummaryReport,
    completionReport: viCompletionReport,
    employmentAcceptanceNotice: viEmploymentAcceptanceNotice,
    comprehensiveInvestmentReport: viComprehensiveInvestmentReport,
  },
};
export default locales;
