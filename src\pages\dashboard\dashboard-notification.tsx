import { DevexDataGrid } from '@/components/devex-data-grid';
import { Column } from 'devextreme-react/data-grid';
import { Notification } from '@/types';

export const DashboardNotification = ({ items }: { items: Notification[] }) => {
  return (
    <div className="max-w-full">
      <div className="mb-2 mt-3 text-xl font-bold">
        <p className="!text-black">Thông báo</p>
      </div>
      <DevexDataGrid
        id={'NOTIFICATION'}
        dataSource={items}
        editing={{ allowAdding: false, allowUpdating: false, allowDeleting: false, useIcons: true }}
        hideSerialNumber
        pager={{ visible: false }}
        paging={{ enabled: false }}
        wordWrapEnabled
        filterRow={{ visible: false }}
        toolbar={{ visible: false }}
        height="auto"
      >
        <Column dataField="title" caption="Tiêu đề" />
        <Column dataField="content" caption="Nội dung" />
      </DevexDataGrid>
    </div>
  );
};
