import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DocumentExportPreparation } from '@/components/document-export-preparation';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { RecordEditableTable } from '@/components/records-attachment';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import {
  useAuth,
  useDataTable,
  useEntity,
  useFormHandler,
  useFormOperation,
  usePermission,
  useSendNotification,
} from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { PrintDataSource } from '@/lib/print';
import { getSumsFromArray, getValidId, getValidItems, numberToVietnameseWords } from '@/lib/utils';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
  getRequest,
  Model,
} from '@/services';
import {
  CompletionAcceptance,
  Contract,
  defaultValuesReportAnnex3a,
  GetReportAnnex3aContractor,
  Project,
  ReportAnnex3a,
  ReportAnnex3aDetail,
  reportAnnex3aSchema,
} from '@/types';
import { useQuery } from '@tanstack/react-query';
import { RowSelectionState } from '@tanstack/react-table';
import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { ClickEvent } from 'devextreme/ui/button';
import { Combobox } from '../combobox';
import { ReportAnnex3aDetailDevEditableTable } from './report-annex-3-a-detail-dev-editable-table';
import { reportAnnex3aDetailsLocalTable } from './template/report-annex-3a-details-table';
import { sumProperty } from '@/lib/array';
import { ReportAnnex3aOverviewDevEditableTable } from './report-annex-3-a-overview-dev-editable-table';

const onReportAnnex3aMutationSuccess = createMutationSuccessFn('reportAnnex3A');

export const ReportAnnex3aForm = ({
  type = 'project-management',
}: {
  type?: 'finance' | 'project-management';
}) => {
  const isForFinance = type === 'finance';
  const isForProjectManagement = type === 'project-management';

  let model: Model = 'report-annex-3a';
  if (isForFinance) {
    model = 'report-annex-3a-finance';
  }

  let path = PATHS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT;
  if (isForFinance) {
    path = PATHS.REPORT_ANNEX_3A_FINANCE;
  }

  const { id: editId } = useParams();

  const { t } = useTranslation('reportAnnex3A');

  let permission = PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT;
  if (isForFinance) {
    permission = PERMISSIONS.REPORT_ANNEX_3A_FINANCE;
  }
  const role = usePermission(permission);
  const { user, projects } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const defaultValues = useMemo(
    () => ({
      ...defaultValuesReportAnnex3a,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<ReportAnnex3a>({
    queryKey: [MUTATE.REPORT_ANNEX_3A_PROJECT_MANAGEMENT, editId],
    mutateKey: [MUTATE.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
    readFn: createQueryByIdFn<ReportAnnex3a>(model),
    createFn: createPostMutateFn<ReportAnnex3a>(model),
    updateFn: createPutMutateFn<ReportAnnex3a>(model),
    formatPayloadFn: data => {
      const details: ReportAnnex3aDetail[] =
        data.reportAnnex3aDetailsLocal?.flatMap(item => {
          return Object.keys(item)
            .filter(key => key.startsWith('contractedOrEstimatedQuantity_'))
            .map(key => {
              const contractorId = key.split('_')[1]; // Lấy ID từ key
              return {
                id: 0,
                reportAnnex3aId: 0,
                constructionTaskId: Number(item.constructionTaskId),
                constructionTaskCode: String(item.constructionTaskCode),
                constructionTaskName: String(item.constructionTaskName),
                unitId: Number(item.unitId),
                unitName: String(item.unitName),
                contractorId: parseInt(contractorId), // Chuyển về số
                note: String(item.note),
                contractedOrEstimatedPaymentPrice: Number(
                  item[`contractedOrEstimatedPaymentPrice`]
                ),
                contractedOrEstimatedQuantity: Number(
                  item[`contractedOrEstimatedQuantity_${contractorId}`]
                ),
                currentPeriodExecution: Number(item[`currentPeriodExecution_${contractorId}`]),
                cumulativePreviousPeriod: Number(item[`cumulativePreviousPeriod_${contractorId}`]),
              };
            });
        }) ?? [];
      const payload = {
        ...data,
        reportAnnex3aTime: toLocaleDate(data.reportAnnex3aTime)!,
        reportAnnex3aOverviews: getValidItems(
          data.reportAnnex3aOverviews,
          item => !!item.contractorId
        ),
        reportAnnex3aDetails: details || [],
        itemsRecordManagement: data.itemsRecordManagement
          .filter(item => item.content)
          .map(itemRecord => ({
            ...itemRecord,
            id: getValidId(itemRecord.id),
            dateCreate: toLocaleDate(itemRecord.dateCreate!),
            itemFile: itemRecord.itemFile
              .filter(file => file.fileName)
              .map(file => ({ ...file, id: getValidId(file.id) })),
          })),
      };
      return payload;
    },
    formatResponseFn: data => {
      const details = data.reportAnnex3aDetails;
      const groupedData = details.reduce<
        Record<string, ReportAnnex3aDetail & Record<string, number | string | null | undefined>>
      >((acc, item) => {
        const key = item.constructionTaskId!;

        if (!acc[key]) {
          acc[key] = {
            contractedOrEstimatedPaymentPrice: 0,
            contractedorestimatedquantity: 0,
            cumulativePreviousPeriod: 0,
            currentPeriodExecution: 0,
            contractedOrEstimatedQuantity: 0,
            id: -getRandomNumber(),
            reportAnnex3aId: 0,
            note: '',
            contractorId: item.contractorId,
            constructionTaskId: item.constructionTaskId,
            constructionTaskCode: item.constructionTaskCode,
            constructionTaskName: item.constructionTaskName,
            unitId: item.unitId,
            unitName: item.unitName,
          };
        }

        // Thêm các trường động theo contractorId
        const contractorKey = item.contractorId;
        acc[key][`contractedOrEstimatedPaymentPrice`] = item.contractedOrEstimatedPaymentPrice;
        acc[key][`contractedOrEstimatedQuantity_${contractorKey}`] =
          item.contractedOrEstimatedQuantity;
        acc[key][`currentPeriodExecution_${contractorKey}`] = item.currentPeriodExecution;
        acc[key][`cumulativePreviousPeriod_${contractorKey}`] = item.cumulativePreviousPeriod;
        acc[key][`accumulatedUpToTheEndOfThisPeriod_${contractorKey}`] =
          Number(item.currentPeriodExecution) + Number(item.cumulativePreviousPeriod);

        acc[key][`contractedOrEstimatedQuantityTotalAmount_${contractorKey}`] =
          Number(item.contractedOrEstimatedQuantity) *
          Number(item.contractedOrEstimatedPaymentPrice);
        acc[key][`currentPeriodExecutionTotalAmount_${contractorKey}`] =
          Number(item.currentPeriodExecution) * Number(item.contractedOrEstimatedPaymentPrice);
        acc[key][`cumulativePreviousPeriodTotalAmount_${contractorKey}`] =
          Number(item.cumulativePreviousPeriod) * Number(item.contractedOrEstimatedPaymentPrice);
        acc[key][`accumulatedUpToTheEndOfThisPeriodTotalAmount_${contractorKey}`] =
          (Number(item.currentPeriodExecution) + Number(item.cumulativePreviousPeriod)) *
          Number(item.contractedOrEstimatedPaymentPrice);

        return acc;
      }, {});

      /// chỗ này có sửa lại để cho phép nhập phiếu
      ///start fix bug
      // const dataTem = Object.values(groupedData)?.flatMap(item => {
      //   return Object.keys(item)
      //     .filter(key => key.startsWith('contractedOrEstimatedQuantity_'))
      //     .map(key => {
      //       const contractorId = key.split('_')[1]; // Lấy ID từ key
      //       return {
      //         unitId: item.unitId,
      //         unitName: item.unitName,
      //         contractorId: parseInt(contractorId), // Chuyển về số
      //         contractedOrEstimatedPaymentPrice: item[`contractedOrEstimatedPaymentPrice`],
      //         contractedOrEstimatedQuantity: Number(
      //           item[`contractedOrEstimatedQuantity_${contractorId}`]
      //         ),
      //         currentPeriodExecution: Number(item[`currentPeriodExecution_${contractorId}`]),
      //         cumulativePreviousPeriod: item[`cumulativePreviousPeriod_${contractorId}`],
      //         contractedOrEstimatedQuantityTotalAmount: Number(
      //           item[`contractedOrEstimatedQuantityTotalAmount_${contractorId}`]
      //         ),
      //         currentPeriodExecutionTotalAmount: Number(
      //           item[`currentPeriodExecutionTotalAmount_${contractorId}`]
      //         ),
      //         cumulativePreviousPeriodTotalAmount: Number(
      //           item[`cumulativePreviousPeriodTotalAmount_${contractorId}`]
      //         ),
      //       };
      //     });
      // });

      // const totals: Record<number, Record<string, number>> = {};

      // dataTem?.forEach(entry => {
      //   const {
      //     contractorId,
      //     contractedOrEstimatedQuantityTotalAmount,
      //     currentPeriodExecutionTotalAmount,
      //   } = entry;
      //   if (!totals[contractorId]) {
      //     totals[contractorId] = {
      //       id: 0,
      //       reportAnnex3aId: 0,
      //       contractorId,
      //       contractValue: 0, // 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng)
      //       advancePaymentValueForThePeriod: 0, // 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước
      //       advancePaymentExecution: 0, // 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước
      //       completedVolumeThisPeriod: 0, // 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này
      //       advancePaymentRecovery: 0, // 5. Thanh toán để thu hồi tạm ứng
      //       valuePaymentForCompletedVolume: 0, // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành
      //       valueAdvancePayment: 0, // 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng
      //       cumulativePaymentForCompletedVolume: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành
      //       cumulativeAdvancePayment: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng
      //     };
      //   }
      //   totals[contractorId].contractValue += contractedOrEstimatedQuantityTotalAmount;
      //   totals[contractorId].completedVolumeThisPeriod += Number(
      //     currentPeriodExecutionTotalAmount + 0
      //   );
      // });
      // Bước 3: Chuyển kết quả về dạng mảng
      // const resultOverviews = Object.values(totals);

      ///end fix bug
      const [
        contractValue,
        advancePaymentValueForThePeriod,
        advancePaymentExecution,
        completedVolumeThisPeriod,
        advancePaymentRecovery,
        valuePaymentForCompletedVolume,
        valueAdvancePayment,
        cumulativePaymentForCompletedVolume,
        cumulativeAdvancePayment,
      ] = getSumsFromArray(data.reportAnnex3aOverviews, [
        'contractValue',
        'advancePaymentValueForThePeriod',
        'advancePaymentExecution',
        'completedVolumeThisPeriod',
        'advancePaymentRecovery',
        'valuePaymentForCompletedVolume',
        'valueAdvancePayment',
        'cumulativePaymentForCompletedVolume',
        'cumulativeAdvancePayment',
      ]);

      const result = {
        ...data,
        reportAnnex3aTime: toDateType(data.reportAnnex3aTime)!,
        reportAnnex3aDetailsLocal: Object.values(groupedData),
        completionAcceptanceTime: toDateType(data.completionAcceptanceTime ?? null)!,
        completionAcceptanceContractSigningDate: toDateType(
          data.completionAcceptanceContractSigningDate ?? null
        )!,
        completionAcceptanceContractAppendixSigningDate: toDateType(
          data.completionAcceptanceContractAppendixSigningDate ?? null
        )!,
        sumContractValue: contractValue,
        sumAdvancePaymentValueForThePeriod: advancePaymentValueForThePeriod,
        sumAdvancePaymentExecution: advancePaymentExecution,
        sumCompletedVolumeThisPeriod: completedVolumeThisPeriod,
        sumAdvancePaymentRecovery: advancePaymentRecovery,
        sumDisbursementRequestValue: valuePaymentForCompletedVolume + valueAdvancePayment,
        sumCumulativeDisbursedValue: cumulativePaymentForCompletedVolume + cumulativeAdvancePayment,
        // reportAnnex3aOverviews: resultOverviews,   /// chỗ này có sửa lại để cho phép nhập phiếu
        reportAnnex3aOverviews: data.reportAnnex3aOverviews,
        itemsRecordManagement: data.itemsRecordManagement.map(item => ({
          ...item,
          dateCreate: toDateType(item.dateCreate!),
        })),
      };
      // console.log({ result });
      return result;
    },
    onCreateSuccess: data => {
      onReportAnnex3aMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onReportAnnex3aMutationSuccess,
    formOptions: {
      resolver: zodResolver(reportAnnex3aSchema),
      defaultValues,
    },
  });

  const [projectId, isForward, projectName, contractId] = methods.watch([
    'projectId',
    'isForward',
    'projectName',
    'contractId',
  ]);

  const { data: project } = useQuery({
    queryKey: [QUERIES.PROJECT, projectId],
    queryFn: () => {
      return getRequest<Project>(`/project/${projectId}`);
    },
    enabled: !isNaN(Number(projectId)),
  });

  const { reset, onTimeChange } = useFormOperation<ReportAnnex3a>({
    model: model,
    fieldTime: 'reportAnnex3aTime',
    createCodeKey: [QUERIES.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
    formMethods: methods,
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<ReportAnnex3a, PeriodFilter>({
    deleteFn: createDeleteMutateFn(model),
    deleteKey: [MUTATE.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };

  const onFormCalculate = <T,>(name?: keyof T, value?: number) => {
    const values = { ...methods.getValues(), ...(name ? { [name]: value } : {}) };
    const dataTem = values.reportAnnex3aDetailsLocal?.flatMap(item => {
      return Object.keys(item)
        .filter(key => key.startsWith('contractedOrEstimatedQuantity_'))
        .map(key => {
          const contractorId = key.split('_')[1]; // Lấy ID từ key
          return {
            unitId: item.unitId,
            unitName: item.unitName,
            contractorId: parseInt(contractorId), // Chuyển về số
            contractedOrEstimatedPaymentPrice: item[`contractedOrEstimatedPaymentPrice`],
            contractedOrEstimatedQuantity: Number(
              item[`contractedOrEstimatedQuantity_${contractorId}`]
            ),
            currentPeriodExecution: Number(item[`currentPeriodExecution_${contractorId}`]),
            cumulativePreviousPeriod: item[`cumulativePreviousPeriod_${contractorId}`],
            contractedOrEstimatedQuantityTotalAmount: Number(
              item[`contractedOrEstimatedQuantityTotalAmount_${contractorId}`]
            ),
            currentPeriodExecutionTotalAmount: Number(
              item[`currentPeriodExecutionTotalAmount_${contractorId}`]
            ),
            cumulativePreviousPeriodTotalAmount: Number(
              item[`cumulativePreviousPeriodTotalAmount_${contractorId}`]
            ),
          };
        });
    });

    const totals: Record<number, Record<string, number>> = {};

    dataTem?.forEach(entry => {
      const {
        contractorId,
        contractedOrEstimatedQuantityTotalAmount,
        currentPeriodExecutionTotalAmount,
      } = entry;
      if (!totals[contractorId]) {
        totals[contractorId] = {
          id: 0,
          reportAnnex3aId: 0,
          contractorId,
          contractValue: 0, // 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng)
          advancePaymentValueForThePeriod: 0, // 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước
          advancePaymentExecution: 0, // 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước
          completedVolumeThisPeriod: 0, // 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này
          advancePaymentRecovery: 0, // 5. Thanh toán để thu hồi tạm ứng
          valuePaymentForCompletedVolume: 0, // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành
          valueAdvancePayment: 0, // 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng
          cumulativePaymentForCompletedVolume: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành
          cumulativeAdvancePayment: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng
        };
      }
      totals[contractorId].contractValue += contractedOrEstimatedQuantityTotalAmount;
      totals[contractorId].completedVolumeThisPeriod += Number(
        currentPeriodExecutionTotalAmount + 0
      );
    });

    // Tính toán tổng giá trị hợp đồng
    const sumContractValue = Object.values(totals).reduce(
      (acc, item) => acc + item.contractValue,
      0
    );
    // Tính toán tổng giá trị sumCompletedVolumeThisPeriod
    const sumCompletedVolumeThisPeriod = Object.values(totals).reduce(
      (acc, item) => acc + item.completedVolumeThisPeriod,
      0
    );

    // Bước 3: Chuyển kết quả về dạng mảng
    const result = Object.values(totals);
    methods.reset({
      ...values,
      sumContractValue: sumContractValue,
      sumCompletedVolumeThisPeriod: sumCompletedVolumeThisPeriod,
      reportAnnex3aOverviews: result,
    });
  };

  const onFormCalculateOverview = <T,>(name?: keyof T, value?: number) => {
    const values = { ...methods.getValues(), ...(name ? { [name]: value } : {}) };
    const [contractValue] = getSumsFromArray(values.reportAnnex3aOverviews, ['contractValue']);
    const [
      advancePaymentValueForThePeriod,
      advancePaymentExecution,
      completedVolumeThisPeriod,
      advancePaymentRecovery,
      valuePaymentForCompletedVolume,
      valueAdvancePayment,
      cumulativePaymentForCompletedVolume,
      cumulativeAdvancePayment,
    ] = getSumsFromArray(values.reportAnnex3aOverviews, [
      'advancePaymentValueForThePeriod',
      'advancePaymentExecution',
      'completedVolumeThisPeriod',
      'advancePaymentRecovery',
      'valuePaymentForCompletedVolume',
      'valueAdvancePayment',
      'cumulativePaymentForCompletedVolume',
      'cumulativeAdvancePayment',
    ]);
    methods.reset({
      ...values,
      sumContractValue: contractValue,
      sumAdvancePaymentValueForThePeriod: advancePaymentValueForThePeriod,
      sumAdvancePaymentExecution: advancePaymentExecution,
      sumCompletedVolumeThisPeriod: completedVolumeThisPeriod,
      sumAdvancePaymentRecovery: advancePaymentRecovery,
      sumDisbursementRequestValue: valuePaymentForCompletedVolume + valueAdvancePayment,
      sumCumulativeDisbursedValue: cumulativePaymentForCompletedVolume + cumulativeAdvancePayment,
    });
  };

  const [completionAcceptanceName, completionAcceptanceId, userCreatedName] = methods.getValues([
    'completionAcceptanceName',
    'completionAcceptanceId',
    'userCreatedName',
  ]);

  const { data: contractors = [] } = useQuery({
    queryKey: [QUERIES.REPORT_ANNEX_3A_CONTRACTOR, completionAcceptanceId],
    queryFn: () => {
      return getRequest<GetReportAnnex3aContractor[]>(
        `/${model}/get-report-annex-3-a-contractor/${completionAcceptanceId}`
      );
    },
    enabled: !isNaN(Number(completionAcceptanceId)),
  });

  const extractYMDFromDate = (date: Date | null) => {
    return {
      year: date?.getFullYear() || '...',
      month: date?.getMonth() !== undefined ? date.getMonth() + 1 : '...',
      date: date?.getDate() || '...',
    };
  };

  const getPrintDataSource = (): PrintDataSource => {
    const data = methods.getValues();
    const dots = '...';
    return {
      ...data,
      contractor_numbers: contractors.length || 1,
      long_column: 4,
      medium_column: 3,
      short_column: 1,
      static_colums: 5,
      dynamic_columns: 8,
      contractors: contractors.length > 0 ? contractors : [{ id: 0, name: '' }],
      projectOwner: data?.projectProjectOwner || dots,
      tenderPackage: data?.completionAcceptanceTenderPackage || dots,
      completionAcceptanceCode: data?.completionAcceptanceCode || dots,
      completionAcceptanceContractAppendixNumber:
        data?.completionAcceptanceContractAppendixNumber || dots,
      completionAcceptancePhase: data?.completionAcceptancePhase || dots,
      completionAcceptanceTenderPackage: data?.completionAcceptanceTenderPackage || dots,
      contractNumber: data?.contractNumber ?? dots,
      sumDisbursementRequestValueText: numberToVietnameseWords(
        Number(data?.sumDisbursementRequestValue) || 0
      ),
      completionAcceptanceContractSigningDate: extractYMDFromDate(
        data?.completionAcceptanceContractSigningDate ?? null
      ),
      completionAcceptanceContractAppendixSigningDate: extractYMDFromDate(
        data?.completionAcceptanceContractAppendixSigningDate ?? null
      ),
      completionAcceptanceTime: extractYMDFromDate(data?.completionAcceptanceTime ?? null),
      extendTemplate: reportAnnex3aDetailsLocalTable,
      sumValuePaymentForCompletedVolume: sumProperty(
        data.reportAnnex3aOverviews,
        'valuePaymentForCompletedVolume'
      ),
      sumValueAdvancePayment: sumProperty(data.reportAnnex3aOverviews, 'valueAdvancePayment'),
    } as PrintDataSource;
  };

  const { sendNotify } = useSendNotification();

  const onForward = (e: ClickEvent) => {
    methods.setValue('isForward', true);
    handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);

    const accountDepartmentTypeId = 3;
    const receivers =
      project?.projectHumanResources
        ?.filter(staff => staff.departmentType === accountDepartmentTypeId)
        .map(staff => staff.memberId || 0) || [];
    if (receivers.length > 0) {
      sendNotify({
        title: t('reportAnnex3a.forward', {
          ns: 'sendNotification',
        }),
        content: project?.name || '',
        typeNotification: PATHS.REPORT_ANNEX_3A_FINANCE, // Thông báo cho accounting
        refId: Number(editId) || null,
        userIds: receivers,
      });
    }
  };

  let canSaveByType = true;
  if (isForFinance) {
    canSaveByType = true;
  } else {
    canSaveByType = !isForward;
  }

  const canSaveChange = (!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate) && canSaveByType;

  const { list: completionAcceptances } = useEntity<CompletionAcceptance>({
    model: 'completion-acceptance',
    queryKey: [QUERIES.COMPLETION_ACCEPTANCE],
  });
  const { list: contract } = useEntity<Contract>({
    model: 'contract',
    queryKey: [QUERIES.CONTRACT],
  });

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={canSaveChange}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              isForFinance ? (
                <></>
              ) : (
                <>
                  <Button
                    text={t('content.createNew', { ns: 'common' })}
                    className="mb-2 uppercase"
                    stylingMode="outlined"
                    type="default"
                    icon="plus"
                    onClick={onCreateNew}
                    disabled={loading}
                  />
                  <Button
                    text={t('content.forward', { ns: 'common' })}
                    className="mb-2 uppercase"
                    stylingMode="contained"
                    type="danger"
                    icon="arrowright"
                    onClick={e => onForward(e)}
                    disabled={loading || isForward || isNaN(Number(editId))}
                  />
                </>
              )
            }
            customElementRight={
              <>
                <DocumentExportPreparation
                  data={getPrintDataSource()}
                  profession={PROFESSIONS.REPORT_ANNEX_3A}
                />
              </>
            }
          >
            <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4  xl:max-w-screen-2xl xl:grid-cols-24">
              {/* Cột 1 */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4  xl:col-span-16 2xl:col-span-12">
                {/* Dự án */}
                <div className="flex items-center">
                  <FormLabel
                    name="projectId"
                    htmlFor="projectId"
                    className="hidden w-[175px] md:block"
                  >
                    {t('fields.projectId')}
                  </FormLabel>
                  <FormField
                    id="projectId"
                    name="projectId"
                    className="min-w-0 flex-1 md:w-[250px]"
                    label={t('fields.projectId')}
                  >
                    <Combobox<Project>
                      placeholder={`${selectLabel} ${t('fields.projectId')}`}
                      options={projects}
                      showFields={['name']}
                      defaultText={projectName}
                      onSelectItem={item => {
                        const selectedProject = item as Project;
                        const complettionAcception = completionAcceptances?.find(
                          item => item.projectId === selectedProject.id
                        );
                        methods.setValue('completionAcceptanceId', complettionAcception?.id);
                        methods.setValue('projectId', selectedProject.id);
                        methods.setValue('projectName', selectedProject.name);

                        methods.setValue('contractId', null);
                        methods.setValue('completionAcceptanceId', undefined);
                      }}
                      disabled={isForFinance}
                    />
                  </FormField>
                </div>

                <div className="grid grid-cols-1 gap-x-4 gap-y-4 xl:max-w-screen-2xl xl:grid-cols-12">
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* Hợp đồng */}
                    <div className="flex items-center">
                      <FormLabel
                        name="contractId"
                        htmlFor="contractId"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.contractId')}
                      </FormLabel>
                      <FormField
                        id="contractId"
                        name="contractId"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.contractId')}
                      >
                        <Combobox<Contract>
                          placeholder={`${selectLabel} ${t('fields.contractId')}`}
                          options={contract.filter(item => item.projectId === projectId)}
                          showFields={['contractNumber']}
                          // defaultText={contractName}
                          onSelectItem={item => {
                            methods.setValue('contractId', item?.id);
                            methods.setValue('completionAcceptanceId', undefined);
                          }}
                          disabled={isForFinance}
                        />
                      </FormField>
                    </div>
                  </div>
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* Nghiệm thu */}
                    <div className="flex items-center">
                      <FormLabel
                        name="completionAcceptanceId"
                        htmlFor="completionAcceptanceId"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.completionAcceptanceId')}
                      </FormLabel>
                      <FormField
                        id="completionAcceptanceId"
                        name="completionAcceptanceId"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.completionAcceptanceId')}
                      >
                        <Combobox<CompletionAcceptance>
                          placeholder={`${selectLabel} ${t('fields.completionAcceptanceId')}`}
                          options={completionAcceptances.filter(
                            item => item.contractId === contractId
                          )}
                          showFields={['code']}
                          defaultText={completionAcceptanceName}
                          onSelectItem={() => {
                            methods.setValue('reportAnnex3aDetailsLocal', []);
                            methods.setValue('reportAnnex3aDetails', []);
                          }}
                          disabled={isForFinance}
                        />
                      </FormField>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-x-4 gap-y-4 xl:max-w-screen-2xl xl:grid-cols-12">
                  {/* Cột 1 */}
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 1. Giá trị hợp đồng */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumContractValue"
                        htmlFor="sumContractValue"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumContractValue')}
                      </FormLabel>
                      <FormField
                        id="sumContractValue"
                        name="sumContractValue"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumContractValue')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumContractValue')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  {/* Cột 1 */}
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 2. Giá trị tạm ứng trong kỳ */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumAdvancePaymentValueForThePeriod"
                        htmlFor="sumAdvancePaymentValueForThePeriod"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumAdvancePaymentValueForThePeriod')}
                      </FormLabel>
                      <FormField
                        id="sumAdvancePaymentValueForThePeriod"
                        name="sumAdvancePaymentValueForThePeriod"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumAdvancePaymentValueForThePeriod')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumAdvancePaymentValueForThePeriod')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 3.Thanh toán thực hiện tạm ứng */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumAdvancePaymentExecution"
                        htmlFor="sumAdvancePaymentExecution"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumAdvancePaymentExecution')}
                      </FormLabel>
                      <FormField
                        id="sumAdvancePaymentExecution"
                        name="sumAdvancePaymentExecution"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumAdvancePaymentExecution')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumAdvancePaymentExecution')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 4. KLTH kỳ này */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumCompletedVolumeThisPeriod"
                        htmlFor="sumCompletedVolumeThisPeriod"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumCompletedVolumeThisPeriod')}
                      </FormLabel>
                      <FormField
                        id="sumCompletedVolumeThisPeriod"
                        name="sumCompletedVolumeThisPeriod"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumCompletedVolumeThisPeriod')}
                      >
                        <InputNumber
                          placeholder={`${enterLabel} ${t('fields.sumCompletedVolumeThisPeriod')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 5. Thanh toán THTU */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumAdvancePaymentRecovery"
                        htmlFor="sumAdvancePaymentRecovery"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumAdvancePaymentRecovery')}
                      </FormLabel>
                      <FormField
                        id="sumAdvancePaymentRecovery"
                        name="sumAdvancePaymentRecovery"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumAdvancePaymentRecovery')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumAdvancePaymentRecovery')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  {/* Ẩn đi trên màn hình < xl và >=2xl */}
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 6. Giá trị đề nghị giải ngân */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumDisbursementRequestValue"
                        htmlFor="sumDisbursementRequestValue"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumDisbursementRequestValue')}
                      </FormLabel>
                      <FormField
                        id="sumDisbursementRequestValue"
                        name="sumDisbursementRequestValue"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumDisbursementRequestValue')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumDisbursementRequestValue')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    {/* 7.Lũy kế giá trị giải ngân */}
                    <div className="flex items-center">
                      <FormLabel
                        name="sumCumulativeDisbursedValue"
                        htmlFor="sumCumulativeDisbursedValue"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.sumCumulativeDisbursedValue')}
                      </FormLabel>
                      <FormField
                        id="sumCumulativeDisbursedValue"
                        name="sumCumulativeDisbursedValue"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.sumCumulativeDisbursedValue')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.sumCumulativeDisbursedValue')}`}
                          readOnly
                        />
                      </FormField>
                    </div>
                  </div>

                  {/* Dự phòng phí */}
                  <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 xl:col-span-6">
                    <div className="flex items-center">
                      <FormLabel
                        name="reservefund"
                        htmlFor="reservefund"
                        className="hidden w-[175px] md:block"
                      >
                        {t('fields.reservefund')}
                      </FormLabel>
                      <FormField
                        id="reservefund"
                        name="reservefund"
                        className="min-w-0 flex-1 md:w-[250px]"
                        type="number"
                        label={t('fields.reservefund')}
                      >
                        <InputNumber
                          isMoney
                          placeholder={`${enterLabel} ${t('fields.reservefund')}`}
                          readOnly={isForFinance}
                        />
                      </FormField>
                    </div>
                  </div>
                </div>
              </div>

              {/* Cột 2 */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4  xl:col-span-8 2xl:col-span-6">
                {/* Ngày lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="reportAnnex3aTime"
                    htmlFor="reportAnnex3aTime"
                    className="hidden w-[175px] md:block"
                  >
                    {t('fields.reportAnnex3aTime')}
                  </FormLabel>
                  <FormField
                    id="reportAnnex3aTime"
                    name="reportAnnex3aTime"
                    className="min-w-0 flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.reportAnnex3aTime')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.reportAnnex3aTime')}`}
                      onChange={onTimeChange}
                      readOnly={isForFinance}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                {/* Mã phiếu */}
                <div className="flex items-center">
                  <FormLabel name="code" htmlFor="code" className="hidden w-[175px] md:block">
                    {t('fields.code')}
                  </FormLabel>
                  <FormField
                    id="code"
                    name="code"
                    className="min-w-0 flex-1 md:w-[250px]"
                    label={t('fields.code')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly />
                  </FormField>
                </div>
                {/* Người lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="userCreatedId"
                    htmlFor="userCreatedId"
                    className="hidden w-[175px] md:block"
                  >
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name="userCreatedId"
                    className="min-w-0 flex-1 md:w-[250px]"
                    label={t('fields.userCreatedId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      defaultText={userCreatedName}
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      disabled
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-1 gap-x-4 gap-y-4 xl:col-span-16 ">
                {/* Ghi chú */}
                {/* Ẩn đi trên màn hình >= xl  */}
                <div className="flex items-center">
                  <FormLabel name="note" htmlFor="note" className="hidden w-[175px] md:block">
                    {t('fields.note')}
                  </FormLabel>
                  <FormField
                    id="note"
                    name="note"
                    className="min-w-0 flex-1 "
                    label={t('fields.note')}
                  >
                    <TextBox
                      placeholder={`${enterLabel} ${t('fields.note')}`}
                      readOnly={isForFinance}
                    />
                  </FormField>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue={isForFinance ? 'overview' : 'detail'}>
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.form.tabs.detail')}</TabsTrigger>
                    {isForFinance && (
                      <TabsTrigger value="overview" hidden>
                        {t('page.form.tabs.overview')}
                      </TabsTrigger>
                    )}
                    <TabsTrigger value="attachment">{t('page.form.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <ReportAnnex3aDetailDevEditableTable
                    role={role}
                    calculateForm={onFormCalculate}
                    isForFinance={isForFinance}
                  />
                </TabsContent>
                <TabsContent
                  value="overview"
                  className="mt-4"
                  hidden={isForProjectManagement}
                  aria-disabled
                >
                  <ReportAnnex3aOverviewDevEditableTable
                    role={role}
                    calculateForm={onFormCalculateOverview}
                  />
                </TabsContent>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="report-annex-3a"
                    profession={PROFESSIONS.REPORT_ANNEX_3A}
                  />
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: "none" }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="reportAnnex3a"
        name={methods.getValues('code')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
