/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useAuth, useDataTable, useEntity, usePermission, useSyncProjectFilter } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { SelectedProjectContext } from '@/provider/selected-project-context';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { Agency, EmploymentAcceptanceNotice, ProjectStatus } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { But<PERSON>, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('employmentAcceptanceNotice');

const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const EmploymentAcceptanceNoticeDataTable = ({
  projectStatus,
}: {
  projectStatus: ProjectStatus;
}) => {
  const path = PATHS.EMPLOYMENT_ACCEPTANCE_NOTICE(projectStatus.code);
  const navigate = useNavigate();
  const { t } = useTranslation('employmentAcceptanceNotice');

  const role = usePermission(PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE);
  const { user, projects } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: agencies } = useEntity<Agency>({ queryKey: [QUERIES.AGENCY], model: 'agency' });
  const { selectedProject } = useContext(SelectedProjectContext);

  const getTargetAlias = (target: EmploymentAcceptanceNotice | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<EmploymentAcceptanceNotice, PeriodFilter>({
    queryRangeName: 'employmentAcceptanceNoticeTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<EmploymentAcceptanceNotice>('employment-acceptance-notice'),
    deleteKey: [MUTATE.DELETE_EMPLOYMENT_ACCEPTANCE_NOTICE],
    invalidateKey: [QUERIES.EMPLOYMENT_ACCEPTANCE_NOTICE],
    initialQuery: {
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${selectedProject?.id || projectIds || 0})`,
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.EMPLOYMENT_ACCEPTANCE_NOTICE],
    queryFn: () => {
      return createQueryPaginationFn<EmploymentAcceptanceNotice>('employment-acceptance-notice')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'EmploymentAcceptanceNoticeTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<EmploymentAcceptanceNotice>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<EmploymentAcceptanceNotice>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  useSyncProjectFilter({
    queryListParams,
    queryListMethods,
    onSyncedParams: () => {
      callbackWithTimeout(refetch);
    },
  });

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'employmentAcceptanceNoticeTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      >
        {/* <div className="flex w-full flex-col sm:flex-row sm:items-center">
          <FormLabel className="text-nowrap sm:w-[45px]">{t('fields.projectId')}</FormLabel>

          <FormField id="projectId" className="min-w-0 flex-1" name={'projectId'}>
            <SelectBox
              items={projects}
              searchExpr={['name', 'code']}
              valueExpr="id"
              onFocusIn={e => {
                const input = e.element.querySelector(
                  'input.dx-texteditor-input'
                ) as HTMLInputElement;
                if (input) input.select();
              }}
              searchEnabled
              searchMode="contains"
              displayExpr={displayExpr(['name'])}
              showClearButton
              focusStateEnabled={false}
            />
          </FormField>
        </div> */}
      </PeriodFilterForm>
      <DevexDataGrid
        id={TABLES.EMPLOYMENT_ACCEPTANCE_NOTICE}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="employmentAcceptanceNoticeTime"
          caption={t('fields.employmentAcceptanceNoticeTime')}
          dataType="date"
          alignment="left"
          width={150}
        />

        <Column dataField="code" caption={t('fields.code')} alignment="left" width={150} />

        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')} width={150}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="projectId" caption={t('fields.projectId')} width={200}>
          <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="agencyId" caption={t('fields.agencyId')} width={150}>
          <Lookup dataSource={agencies} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="approvalNumber"
          caption={t('fields.approvalNumber')}
          alignment="left"
          width={150}
        />
        <Column
          dataField="approvalDate"
          caption={t('fields.approvalDate')}
          dataType="date"
          alignment="left"
          width={150}
        />
        <Column
          dataField="approvalContent"
          caption={t('fields.approvalContent')}
          alignment="left"
          width={150}
        />

        <Column dataField="note" caption={t('fields.note')} alignment="left" width={150} />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="employmentAcceptanceNotice"
      />
    </PageLayout>
  );
};
