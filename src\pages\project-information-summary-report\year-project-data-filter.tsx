import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { QUERIES, selectLabel, viewLabel } from '@/constant';
import { useAuth } from '@/hooks';
import { callbackWithTimeout } from '@/lib/utils';
import { <PERSON><PERSON>, DateBox } from 'devextreme-react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { QueryType } from './query-type';

type Props = {
  onSearch: () => void;
  form: UseFormReturn<QueryType>;
};
export const YearProjectDataFilter = ({ onSearch, form }: Props) => {
  const { t } = useTranslation('projectInformationSummaryReport');
  const { projects } = useAuth();
  return (
    <Form {...form}>
      <form
        onSubmit={e => {
          e.preventDefault();
          const submit = async () => {
            const canSubmit = await form.trigger();
            if (canSubmit) {
              onSearch();
            }
          };
          callbackWithTimeout(submit);
        }}
        aria-description="Bộ lọc báo cáo"
        className="flex w-full flex-col gap-x-4 gap-y-4   md:max-w-screen-lg md:flex-row md:items-center"
      >
        <div className="flex items-center">
          <FormLabel name="year" htmlFor="year">
            {t('page.year')}
          </FormLabel>
          <FormField name="year" className="w-[110px]" type="date">
            <DateBox
              type="date"
              pickerType="calendar"
              calendarOptions={{
                maxZoomLevel: 'decade',
                minZoomLevel: 'decade',
              }}
              displayFormat={'year'}
              focusStateEnabled={false}
            />
          </FormField>
        </div>
        <div className="flex min-w-0 flex-1 items-center">
          <FormLabel name="projectId" htmlFor="projectId">
            {t('page.projectId')}
          </FormLabel>
          <FormField name="projectId" className="min-w-0 flex-1">
            <FormCombobox
              placeholder={`${selectLabel} ${t('fields.projectId')}`}
              options={projects}
              queryKey={[QUERIES.PROJECT]}
              className="w-full"
            />
          </FormField>
        </div>
        {/* Nút tìm kiếm */}
        <div className="flex items-center">
          <Button
            text={viewLabel} // Nhãn của nút
            className="w-fit"
            stylingMode="contained" // Giao diện của nút
            type="default"
            icon="search" // Icon tìm kiếm
            useSubmitBehavior // Cho phép nút tự động submit form
          />
        </div>
      </form>
    </Form>
  );
};
