import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { RecordEditableTable } from '@/components/records-attachment';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AGENCY_TYPE,
  enterLabel,
  EXPIRATION_MONTH,
  GROUP_DOC_CODE as TYPE_DOC_CODE,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import {
  useAuth,
  useDeepCompareMemo,
  useFormHandler,
  useFormOperation,
  usePermission,
} from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { displayExpr, getValidId } from '@/lib/utils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import { Agency, ProjectStatus } from '@/types';
import { RowSelectionState } from '@tanstack/react-table';
import { DateBox, SelectBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import {
  CompletionAcceptanceNotice,
  completionAcceptanceNoticeSchema,
  defaultValuesCompletionAcceptanceNotice,
} from '@/types';
import notification from '@/lib/notifications';

const onCompletionAcceptanceNoticeMutationSuccess = createMutationSuccessFn(
  'completionAcceptanceNotice'
);

export const CompletionAcceptanceNoticeForm = ({
  projectStatus,
}: {
  projectStatus: ProjectStatus;
}) => {
  const { id: editId } = useParams();

  const { t } = useTranslation('completionAcceptanceNotice');
  const role = usePermission(PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE);
  const { user, projects } = useAuth();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const path = PATHS.COMPLETION_ACCEPTANCE_NOTICE(projectStatus.code);
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesCompletionAcceptanceNotice,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<CompletionAcceptanceNotice>({
    queryKey: [MUTATE.COMPLETION_ACCEPTANCE_NOTICE, editId],
    mutateKey: [MUTATE.COMPLETION_ACCEPTANCE_NOTICE],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.COMPLETION_ACCEPTANCE_NOTICE],
    readFn: createQueryByIdFn<CompletionAcceptanceNotice>('completion-acceptance-notice'),
    createFn: createPostMutateFn<CompletionAcceptanceNotice>('completion-acceptance-notice'),
    updateFn: createPutMutateFn<CompletionAcceptanceNotice>('completion-acceptance-notice'),
    formatPayloadFn: data => ({
      ...data,
      approvalDate: toLocaleDate(data.approvalDate!),
      completionAcceptanceNoticeTime: toLocaleDate(data.completionAcceptanceNoticeTime!),
      finalizationDossierDeadline: toLocaleDate(data.finalizationDossierDeadline!),
      warrantyExpirationDate: toLocaleDate(data.warrantyExpirationDate!),
      itemsRecordManagement: data.itemsRecordManagement
        .filter(item => item.content)
        .map(itemRecord => ({
          ...itemRecord,
          id: getValidId(itemRecord.id),
          dateCreate: toLocaleDate(itemRecord.dateCreate!),
          itemFile: itemRecord.itemFile
            .filter(file => file.fileName)
            .map(file => ({ ...file, id: getValidId(file.id) })),
        })),
    }),
    formatResponseFn: response => {
      const data = {
        ...response,
        approvalDate: toDateType(response.approvalDate!),

        completionAcceptanceNoticeTime: toDateType(response.completionAcceptanceNoticeTime!),
        finalizationDossierDeadline: toDateType(response.finalizationDossierDeadline!),
        warrantyExpirationDate: toDateType(response.warrantyExpirationDate!),
        itemsRecordManagement: response.itemsRecordManagement.map(i => ({
          ...i,
          dateCreate: toDateType(i.dateCreate!),
        })),
      };
      return data;
    },
    onCreateSuccess: data => {
      onCompletionAcceptanceNoticeMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onCompletionAcceptanceNoticeMutationSuccess,
    formOptions: {
      resolver: zodResolver(completionAcceptanceNoticeSchema),
      defaultValues,
    },
  });

  const [userCreatedName, setupExpirationDate, itemsRecordManagement] = methods.watch([
    'userCreatedName',
    'setupExpirationDate',
    'itemsRecordManagement',
  ]);

  const completionAndHandoverReportDate = useDeepCompareMemo(
    () =>
      itemsRecordManagement?.find(item => item.typeDocCode === TYPE_DOC_CODE.BBNTDVSD)?.dateCreate,
    [itemsRecordManagement]
  );

  const BBNTDVSD = useDeepCompareMemo(() => {
    if (!itemsRecordManagement) return [];
    return itemsRecordManagement.filter(item => item.typeDocCode === TYPE_DOC_CODE.BBNTDVSD);
  }, [itemsRecordManagement]);

  useEffect(() => {
    if (!BBNTDVSD) return;

    if (BBNTDVSD.length > 1) {
      // Lấy phần tử đầu tiên và giữ nguyên cấu trúc mảng
      methods.setValue('itemsRecordManagement', [itemsRecordManagement[0]]);
      notification.error(t('notifiErr.errGroupDoc'));
    }
  }, [BBNTDVSD, methods, t, itemsRecordManagement]);

  useEffect(() => {
    if (completionAndHandoverReportDate) {
      // Cập nhật ngày hết hạn bảo hành (tự động +12 tháng)
      const newDate = new Date(completionAndHandoverReportDate);
      newDate.setMonth(newDate.getMonth() + EXPIRATION_MONTH.WARRANTY_EXPIRATION_DATE);
      methods.setValue('warrantyExpirationDate', newDate);

      // Cập nhật hạn nộp hồ sơ quyết toán (nếu có setupExpirationDate)
      if (typeof setupExpirationDate === 'number') {
        if (setupExpirationDate > 0) {
          const deadline = new Date(completionAndHandoverReportDate);
          deadline.setMonth(deadline.getMonth() + setupExpirationDate);
          methods.setValue('finalizationDossierDeadline', deadline);
        } else if (setupExpirationDate === 0) {
          methods.setValue('finalizationDossierDeadline', null);
        }
      }
    } else {
      methods.setValue('warrantyExpirationDate', null);
      methods.setValue('finalizationDossierDeadline', null);
    }
  }, [completionAndHandoverReportDate, setupExpirationDate, methods]);

  const { reset, onTimeChange } = useFormOperation<CompletionAcceptanceNotice>({
    model: 'completion-acceptance-notice',
    fieldTime: 'completionAcceptanceNoticeTime',
    createCodeKey: [QUERIES.COMPLETION_ACCEPTANCE_NOTICE],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
          >
            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4 xl:max-w-screen-2xl">
              {/* Cột 1 */}
              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Dự án */}
                  <div className="flex items-center">
                    <FormLabel
                      name="projectId"
                      htmlFor="projectId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.projectId')}
                    </FormLabel>
                    <FormField
                      label={t('fields.projectId')}
                      id="projectId"
                      name="projectId"
                      isRequired
                      className="min-w-0 flex-1"
                    >
                      <SelectBox
                        items={projects}
                        onSelectionChanged={e => {
                          methods.setValue(
                            'setupExpirationDate',
                            (e.selectedItem?.setupExpirationDate || 0) as number
                          );
                        }}
                        searchExpr={['name', 'code']}
                        placeholder={`${selectLabel} ${t('fields.projectId')}`}
                        valueExpr="id"
                        displayExpr={displayExpr(['name'])}
                        searchEnabled
                        searchMode="contains"
                        showClearButton
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>
                  <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-2 xl:max-w-screen-2xl">
                    {/* Số quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalNumber"
                        htmlFor="approvalNumber"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalNumber')}
                      </FormLabel>
                      <FormField
                        id="approvalNumber"
                        name="approvalNumber"
                        className="min-w-0 flex-1"
                        label={t('fields.approvalNumber')}
                      >
                        <TextBox placeholder={`${enterLabel} ${t('fields.approvalNumber')}`} />
                      </FormField>
                    </div>
                    {/* Ngày quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalDate"
                        htmlFor="approvalDate"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalDate')}
                      </FormLabel>
                      <FormField
                        id="approvalDate"
                        name="approvalDate"
                        className="min-w-0 flex-1"
                        type="date"
                        label={t('fields.approvalDate')}
                      >
                        <DateBox
                          placeholder={`${selectLabel} ${t('fields.approvalDate')}`}
                          pickerType="calendar"
                          focusStateEnabled={false}
                        />
                      </FormField>
                    </div>
                  </div>
                  {/* Cơ quan */}
                  <div className="flex items-center">
                    <FormLabel
                      name="agencyId"
                      htmlFor="agencyId"
                      className="hidden w-[90px] leading-4 md:block"
                    >
                      {t('fields.agencyId')}
                    </FormLabel>
                    <FormField
                      id="agencyId"
                      name="agencyId"
                      className="min-w-0 flex-1"
                      label={t('fields.agencyId')}
                    >
                      <FormCombobox<Agency>
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.agencyId')}`}
                        model="agency"
                        queryKey={[QUERIES.AGENCY]}
                        filter={item => item.agencyType === AGENCY_TYPE.ISSUE}
                      />
                    </FormField>
                  </div>
                  {/* Nội dung quyết định */}
                  <div className="flex items-center">
                    <FormLabel
                      name="approvalContent"
                      htmlFor="approvalContent"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.approvalContent')}
                    </FormLabel>
                    <FormField
                      id="approvalContent"
                      name="approvalContent"
                      className="min-w-0 flex-1"
                      label={t('fields.approvalContent')}
                    >
                      <TextArea
                        autoResizeEnabled={true}
                        placeholder={`${enterLabel} ${t('fields.approvalContent')}`}
                      />
                    </FormField>
                  </div>

                  {/* Diên giải */}
                  <div className="flex items-center">
                    <FormLabel name="note" htmlFor="note" className="hidden w-[90px] md:block">
                      {t('fields.note')}
                    </FormLabel>
                    <FormField
                      id="note"
                      name="note"
                      className="min-w-0 flex-1"
                      label={t('fields.note')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Cột 3 */}
              <div className="col-span-1">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Ngày hết hạn Lập hồ sơ quyết toán*/}
                  <div className="col-span-1 flex items-center">
                    <FormLabel
                      name="finalizationDossierDeadline"
                      htmlFor="finalizationDossierDeadline"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.finalizationDossierDeadline')}
                    </FormLabel>
                    <FormField
                      id="finalizationDossierDeadline"
                      name="finalizationDossierDeadline"
                      className="min-w-0 flex-1"
                      type="date"
                      label={t('fields.finalizationDossierDeadline')}
                    >
                      <DateBox
                        disabled={true}
                        placeholder={`${selectLabel} ${t('fields.finalizationDossierDeadline')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>
                  {/* Ngày hết hạn bảo hành */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel
                      name="warrantyExpirationDate"
                      htmlFor="warrantyExpirationDate"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.warrantyExpirationDate')}
                    </FormLabel>
                    <FormField
                      id="warrantyExpirationDate"
                      name="warrantyExpirationDate"
                      className="min-w-0 flex-1"
                      type="date"
                      label={t('fields.warrantyExpirationDate')}
                    >
                      <DateBox
                        disabled={true}
                        placeholder={`${selectLabel} ${t('fields.warrantyExpirationDate')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Ngày lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="completionAcceptanceNoticeTime"
                      htmlFor="completionAcceptanceNoticeTime"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.completionAcceptanceNoticeTime')}
                    </FormLabel>
                    <FormField
                      id="completionAcceptanceNoticeTime"
                      name="completionAcceptanceNoticeTime"
                      className="min-w-0 flex-1"
                      type="date"
                      onChange={e => {
                        onTimeChange(e.target.value);
                      }}
                      label={t('fields.completionAcceptanceNoticeTime')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.completionAcceptanceNoticeTime')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Mã phiếu */}
                  <div className="flex items-center">
                    <FormLabel name="code" htmlFor="code" className="hidden w-[90px] md:block">
                      {t('fields.code')}
                    </FormLabel>
                    <FormField
                      id="code"
                      name="code"
                      className="min-w-0 flex-1"
                      label={t('fields.code')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly={true} />
                    </FormField>
                  </div>

                  {/* Người lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="userCreatedId"
                      htmlFor="userCreatedId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.userCreatedId')}
                    </FormLabel>
                    <FormField
                      id="userCreatedId"
                      name="userCreatedId"
                      className="min-w-0 flex-1"
                      label={t('fields.userCreatedId')}
                    >
                      <FormCombobox
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                        model="user"
                        queryKey={[QUERIES.USERS]}
                        disabled
                      />
                    </FormField>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="attachment">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="attachment">{t('page.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="completion-acceptance-notice"
                    profession={PROFESSIONS.COMPLETION_ACCEPTANCE_NOTICE}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
