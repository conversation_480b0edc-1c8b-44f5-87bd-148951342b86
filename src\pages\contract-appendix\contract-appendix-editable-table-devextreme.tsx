import { ErrorMessage } from '@/components/ui/error-message';
import {
  DEFAULT_DECIMAL_SCALE,
  downloadTemplateLabel,
  PROFESSIONS,
  QUERIES,
  TABLES,
} from '@/constant';
import {
  BiddingMethod,
  BiddingSector,
  ConstructionTask,
  ContractAppendix,
  ContractAppendixDetail,
  Contractor,
  CostItem,
  defaultValuesContractAppendix,
  IUserPermission,
  TenderType,
  Unit,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { BasicDialog } from '@/components/basic-dialog';

import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { useBoolean, useEntity, useScreenSize } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { Button } from 'devextreme-react';
import { Column } from 'devextreme/ui/data_grid';
import { customizeNumberCell, onCellPrepared } from '@/components/devex-data-grid';
import { cn } from '@/lib/utils';
import {
  DataGrid,
  IColumnProps as ColumnProps,
  Toolbar,
  Item,
  Editing,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  FilterRow,
  Scrolling,
  Paging,
  Pager,
  HeaderFilter,
  Search,
  SearchPanel,
  GroupPanel,
  DataGridTypes,
} from 'devextreme-react/data-grid';
import { useCallback } from 'react';
import { getRandomNumber } from '@/lib/number';
import { PagerPageSize } from 'devextreme/common/grids';
import { optionsPercent } from '../project';

const [defaultRow] = defaultValuesContractAppendix.contractAppendixDetails;

type ContractAppendixEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('contractAppendix');

const selectTextOnFocus = (args: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  args.element.querySelector('input.dx-texteditor-input')?.select();
};

const updateRow = (
  quantity: ContractAppendixDetail['quantity'],
  price: ContractAppendixDetail['price']
) => ({
  quantity,
  price,
  totalAmount: (quantity || 0) * (price || 0),
});

const format = ',##0.##';
const editorOptions = {
  format: format,
  showSpinButtons: false,
};
const allowPageSizes: Array<number | PagerPageSize> = [5, 10, 50, 100];

export const ContractAppendixEditableTableDevextreme = ({
  role,
  calculateForm,
}: ContractAppendixEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<ContractAppendix>();

  const [editableData, id, contractId] = useWatch({
    control,
    name: ['contractAppendixDetails', 'id', 'contractId'],
  });

  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { list: contractors } = useEntity<Contractor>({
    model: 'contractor',
    queryKey: [QUERIES.CONTRACTOR],
  });
  const { list: constructionTasks, fetch: fetchConstructionTaskItems } =
    useEntity<ConstructionTask>({
      model: 'construction-task',
      queryKey: [QUERIES.CONSTRUCTION_TASK],
    });
  const { list: units, fetch: fetchUnits } = useEntity<Unit>({
    model: 'unit',
    queryKey: [QUERIES.UNIT],
  });
  const { fetch: fetchCostItems } = useEntity<CostItem>({
    model: 'cost-item',
    queryKey: [QUERIES.COST_ITEM],
  });
  const { fetch: fetchTenderTypes } = useEntity<TenderType>({
    model: 'tender-type',
    queryKey: [QUERIES.TENDER_TYPE],
  });
  const { fetch: fetchBiddingMethods } = useEntity<BiddingMethod>({
    model: 'bidding-method',
    queryKey: [QUERIES.BIDDING_METHOD],
  });
  const { fetch: fetchBiddingSectors } = useEntity<BiddingSector>({
    model: 'bidding-sector',
    queryKey: [QUERIES.BIDDING_SECTOR],
  });

  const columns: Column[] = [
    {
      cellTemplate: (container, options) => {
        const pageIndex = options.component.pageIndex();
        const pageSize = options.component.pageSize();
        const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
        container.textContent = serialNumber.toString();
      },
      caption: 'STT',
      fixed: true,
      fixedPosition: 'left',
      alignment: 'center',
      width: 50,
      dataField: 'serialNumber',
      allowSorting: false,
      allowFiltering: false,
      allowEditing: false,
      format: ',##0,##',
    },
    {
      dataField: 'constructionTaskId',
      caption: t('fields.constructionTaskId'),
      lookup: { dataSource: constructionTasks, displayExpr: 'name', valueExpr: 'id' },
      setCellValue: (newData, value) => {
        newData.constructionTaskId = value;

        const task = constructionTasks.find(t => t.id === value);
        newData.constructionTaskName = task?.name;

        const unit = units.find(item => item.id === task?.unitId);
        newData.unitId = unit?.id;
        newData.unitName = unit?.name || '';
      },
      validationRules: [{ type: 'required' }],
    },
    {
      dataField: 'contractorId',
      caption: t('fields.contractorId'),
      lookup: { dataSource: contractors, displayExpr: 'name', valueExpr: 'id' },
      width: 120,
      validationRules: [{ type: 'required' }],
    },
    {
      dataField: 'unitId',
      caption: t('fields.unitId'),
      lookup: { dataSource: units, displayExpr: 'name', valueExpr: 'id' },
      width: 120,
    },
    {
      dataField: 'price',
      caption: t('fields.price'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(0)({ value: e.value }),
      width: 180,
      setCellValue: (newData, value, row) => {
        newData.price = value;
        newData.totalAmount = (row.quantity || 0) * (value || 0);
      },
      editorOptions: editorOptions,
    },
    {
      dataField: 'quantity',
      caption: t('fields.quantity'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(DEFAULT_DECIMAL_SCALE)({ value: e.value }),
      width: 180,
      setCellValue: (newData, value, row) => {
        newData.quantity = value;
        newData.totalAmount = (value || 0) * (row.price || 0);
      },
      editorOptions: optionsPercent.editorOptions,
    },
    {
      dataField: 'totalAmount',
      caption: t('fields.totalAmountDetail'),
      dataType: 'number',
      format: '#,##0.00',
      customizeText: e => customizeNumberCell(0)({ value: e.value }),
      allowEditing: false,
      width: 180,
    },
    {
      dataField: 'note',
      caption: t('fields.note'),
      dataType: 'string',
      width: 150,
    },
  ];

  const columnsForImportConfig = [...columns].map(column => {
    return {
      field: column.dataField,
      header: column.caption as string,
    };
  });

  const { height } = useScreenSize();
  const onRowInserting = useCallback((e: DataGridTypes.RowInsertingEvent) => {
    e.data.id = -getRandomNumber();
  }, []);
  const calculateFormWrapper = () => {
    setValue('contractAppendixDetails', [...editableData]);
    calculateForm?.();
  };

  return (
    <div>
      <DataGrid
        keyExpr={'id'}
        id={TABLES.CONTRACT_APPENDIX_DETAIL}
        dataSource={editableData}
        columnAutoWidth
        allowColumnResizing
        columnResizingMode="widget"
        allowColumnReordering
        showBorders
        showColumnLines
        showRowLines
        wordWrapEnabled
        hoverStateEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        repaintChangesOnly
        remoteOperations={false}
        className={cn(
          'column-header-wrap',
          'max-h-[calc(100vh-9.8rem-32.8px)]',
          height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
        )}
        onInitNewRow={e => {
          e.data = { ...defaultRow };
        }}
        onRowInserting={onRowInserting}
        onRowRemoved={calculateFormWrapper}
        onRowInserted={calculateFormWrapper}
        onRowUpdated={calculateFormWrapper}
        columns={columns}
        onEditorPreparing={e => {
          if (e.parentType !== 'dataRow') {
            return;
          }

          switch (e.dataField) {
            case 'unitId': {
              const rowData = e.row?.data as ContractAppendixDetail;
              const constructionTask = constructionTasks.find(
                item => item.id === rowData.constructionTaskId
              );
              e.editorOptions.dataSource = units.filter(
                item => item.id === constructionTask?.unitId
              );
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
            }
            case 'constructionTaskId':
            case 'contractorId':
            case 'price':
            case 'quantity':
            case 'note':
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
          }
          e.editorOptions.step = 0;
        }}
        onCellPrepared={e => {
          onCellPrepared(e);
        }}
      >
        <Editing
          mode="cell"
          allowUpdating={role?.isCreate || role?.isUpdate}
          allowDeleting={role?.isCreate || role?.isUpdate}
          allowAdding={role?.isCreate || role?.isUpdate}
          confirmDelete={false}
          useIcons
          newRowPosition="last"
        />
        <ColumnChooser enabled mode="select" height="45rem">
          <ColumnChooserSearch enabled />
          <ColumnChooserSelection allowSelectAll selectByClick recursive />
        </ColumnChooser>
        <FilterRow visible showOperationChooser />
        <Scrolling mode="standard" rowRenderingMode="standard" />
        <Paging enabled defaultPageSize={10} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={allowPageSizes}
        />
        <HeaderFilter visible>
          <Search enabled mode="contains" />
        </HeaderFilter>
        <SearchPanel visible />
        <GroupPanel visible />
        <Toolbar>
          {errors.contractAppendixDetails?.message && (
            <ErrorMessage message={errors.contractAppendixDetails?.message} />
          )}
          <Item location="before">
            <Button
              stylingMode="text"
              icon="download"
              text={downloadTemplateLabel}
              type="default"
              onClick={() => {
                window.open(`/templates/mau_import_phu_luc_hop_dong.xlsx`);
              }}
            />
          </Item>
          <Item location="before" locateInMenu="auto">
            <Button
              stylingMode="text"
              icon="upload"
              text="Import Excel"
              type="default"
              onClick={toggleImportForm}
            />
          </Item>

          <Item location="after" name="addRowButton" />
          {(role?.isCreate || role?.isUpdate) && (
            <Item location="after">
              <Button
                icon="trash"
                hint={'Xóa tất cả'}
                onClick={() => {
                  setValue('contractAppendixDetails', []);
                  setValue('totalAmount', 0);
                }}
              />
            </Item>
          )}
          <Item location="after" name="columnChooserButton" />
        </Toolbar>
      </DataGrid>

      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ContractAppendixDetail>
          onApply={data => {
            setValue(
              'contractAppendixDetails',
              data.map(item => ({
                ...defaultRow,
                ...item,
                ...updateRow(item.quantity || 0, item.price || 0),
                contractAppendixId: id,
                contractDetailId: item.contractDetailId,
              }))
            );
            calculateForm?.();
            toggleImportForm();
          }}
          importModel="contract-appendix"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.CONTRACT_APPENDIX}
          useDevDataGrid={true}
          devDataGridColumns={columns
            .filter(col => col.dataField !== 'serialNumber')
            .map(col => {
              return { ...col } as ColumnProps;
            })}
          professionColumns={columnsForImportConfig}
          onImported={() => {
            fetchConstructionTaskItems({});
            fetchUnits({});
            fetchCostItems({});
            fetchTenderTypes({});
            fetchBiddingMethods({});
            fetchBiddingSectors({});
          }}
          additionalFormValues={[{ key: 'refId', value: contractId?.toString() || '' }]}
        />
      </BasicDialog>
    </div>
  );
};
