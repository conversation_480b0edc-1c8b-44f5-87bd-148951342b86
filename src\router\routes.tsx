import {
  BarChartHorizontal,
  Building,
  CalendarCheck2,
  LucideIcon,
  PlaneLandingIcon,
  Settings2,
  SettingsIcon,
  LineChart,
} from 'lucide-react';
import { Outlet, RouteObject, createBrowserRouter } from 'react-router-dom';

import Error403 from '@/403';
import ErrorPage from '@/components/error-page';
import { PATHS, PERMISSIONS, PROJECT_STATUS, PROJECT_STATUS_COMPLETED } from '@/constant';

import { AgencyPage } from '@/pages/agency';
import { BiddingMethodPage } from '@/pages/bidding-method';
import { BiddingSectorPage } from '@/pages/bidding-sector';
import {
  BoardOfDirectorsWorkScheduleDataTable,
  BoardOfDirectorsWorkScheduleForm,
} from '@/pages/board-of-directors-work-schedule';
import { BudgetFundPage } from '@/pages/budget-fund';
import { CareerTrainingPage } from '@/pages/career-training';
import { Company } from '@/pages/company';
import { ConstructionItemPage } from '@/pages/construction-item';
import { ConstructionTaskPage } from '@/pages/construction-task';
import { ContractTypePage } from '@/pages/contract-type';
import { ContractorPage } from '@/pages/contractor';
import { ContractorTypePage } from '@/pages/contractor-type';
import { CorrespondenceTypePage } from '@/pages/correspondence-type';
import { CostItemPage } from '@/pages/cost-item';
import { CostItemTypePage } from '@/pages/cost-item-type';
import { Dashboard } from '@/pages/dashboard/dashboard';
import { DepartmentPage } from '@/pages/department';
import { DeploymentPhasePage } from '@/pages/deployment-phase';
import { DirectiveContentDataTable, DirectiveContentForm } from '@/pages/directive-content';
import { Directives } from '@/pages/directives';
import { DistrictPage } from '@/pages/district';
import { DomainCheck } from '@/pages/domain-check';
import { EmployeeDataTable } from '@/pages/employee';
import { EmployeeTypePage } from '@/pages/employee-type';
import { EmployeeForm } from '@/pages/employee/employee-form';
import { EvaluationResultPage } from '@/pages/evaluation-result';
import { ExpertisePage } from '@/pages/expertise';
import { FileTypePage } from '@/pages/file-type';
import { ForeignLanguagePage } from '@/pages/foreign-language';
import { GenderTypePage } from '@/pages/gender-type';
import { InventoryItemPage } from '@/pages/inventory-item';
import { InventoryItemTypePage } from '@/pages/inventory-item-type/inventory-item-type';
import { ItCoursePage } from '@/pages/it-course';
import { LeaveDataTable, LeaveForm } from '@/pages/leave';
import { Login } from '@/pages/login';
import { MajorPage } from '@/pages/major';
import { OvertimeRegistrationDataTable } from '@/pages/overtime-registration/overtime-registration-data-table';
import { OvertimeRegistrationForm } from '@/pages/overtime-registration/overtime-registration-form';
import { PoliticsPage } from '@/pages/politics';
import { PositionPage } from '@/pages/position';
import { ProjectGroupPage } from '@/pages/project-group';
import { ProjectManagementTypePage } from '@/pages/project-management-type';
import { ProjectOwnerPage } from '@/pages/project-owner';
import { ProjectStatusPage } from '@/pages/project-status';
import { TenderTypePage } from '@/pages/tender-type';
import { TrainingInstitutionPage } from '@/pages/training-institution';
import { UserProfile } from '@/pages/user-profile';
import { WardPage } from '@/pages/ward';
import { WorkPositionPage } from '@/pages/work-position';
import { AuthProvider, SignalRProvider } from '@/provider';
import { DomainProvider } from '@/provider/domain-provider';

import { AssetPage } from '@/pages/asset';
import { AssetTypePage } from '@/pages/asset-type';
import { BudgetSourceCodeDataTable } from '@/pages/budget-source-code';
import {
  CompletionAcceptanceDataTable,
  CompletionAcceptanceForm,
} from '@/pages/completion-acceptance';
import { ContractDataTable, ContractForm } from '@/pages/contract';
import { ContractAppendixDataTable, ContractAppendixForm } from '@/pages/contract-appendix';
import { ContractAppendixTypePage } from '@/pages/contract-appendix-type';
import {
  ContractTaskManagementDataTable,
  ContractTaskManagementForm,
} from '@/pages/contract-task-management';
import {
  ContractorSelectionPlanDataTable,
  ContractorSelectionPlanForm,
} from '@/pages/contractor-selection-plan';

import {
  ABAdjustmentSettlementDataTable,
  ABAdjustmentSettlementForm,
} from '@/pages/a-b-adjustment-settlement';
import { ABSettlementDataTable, ABSettlementForm } from '@/pages/a-b-settlement';
import {
  AdjustedCostEstimationDataTable,
  AdjustedCostEstimationForm,
} from '@/pages/adjusted-cost-estimation';
import { AdjustedInvestmentForm } from '@/pages/adjusted-investment';
import { AdjustedInvestmentDataTable } from '@/pages/adjusted-investment/adjusted-investment-data-table';
import { AdvancePaymentDataTable, AdvancePaymentForm } from '@/pages/advance-payment';
import { RptAnnualTaskListStatisticsTemplateDataTable } from '@/pages/annual-task-list-statistics-report/annual-task-list-statistics-report-data-table';
import { ApprovalProcessDataTable, ApprovalProcessForm } from '@/pages/approval-process';
import { AssetIncrementDataTable, AssetIncrementForm } from '@/pages/asset-increment';
import {
  BacklogProjectManagementDataTable,
  BacklogProjectManagementForm,
} from '@/pages/backlog-project-management';
import { BorrowDocumentDataTable, BorrowDocumentForm } from '@/pages/borrow-document';
import { BudgetItemCodePage } from '@/pages/budget-item-code';
import {
  CapitalIncreasePlanDataTable,
  CapitalIncreasePlanForm,
} from '@/pages/capital-increase-plan';
import { CapitalPlanDisbursementProgressReportDataTable2 } from '@/pages/capital-plan-disbursement-progress-report/capital-plan-disbursement-progress-report-data-table-2';
import { ConstructionTypeDataTable } from '@/pages/construction-type';
import { ContractSettlementDataTable, ContractSettlementForm } from '@/pages/contract-settlement';
import { ContractorParticipationTenderPackageListReportDataTable } from '@/pages/contractor-participation-tender-package-list-report';
import {
  ContractorSelectionResultDataTable,
  ContractorSelectionResultForm,
} from '@/pages/contractor-selection-result';
import { ContractorSelectionResultByManagingDirectorReportDataTable } from '@/pages/contractor-selection-result-by-managing-director-report';
import { ContractorSelectionResultByYearReportDataTable } from '@/pages/contractor-selection-result-by-year-report';
import {
  DataReconciliationTableDataTable,
  DataReconciliationTableForm,
} from '@/pages/data-reconciliation-table';
import {
  DesignTaskManagementDataTable,
  DesignTaskManagementForm,
} from '@/pages/design-task-management';
import { DirectiveTaskDataTable, DirectiveTaskForm } from '@/pages/directive-task';
import { DocumentDecisionDataTable, DocumentDecisionForm } from '@/pages/document-decision';
import { DocumentFormEntryDataTable, DocumentFormEntryForm } from '@/pages/document-form-entry';
import { DocumentTypePage } from '@/pages/document-type';
import { EmployeeAnnualEvaluationResultReportDataTable } from '@/pages/employee-annual-evaluation-result-report';
import { EmployeePayrollReportDataTable } from '@/pages/employee-payroll-report';
import {
  FormDocumentManagerDataTable,
  FormDocumentManagerForm,
} from '@/pages/form-document-manager';
import { FundingProgramCodeDataTable } from '@/pages/funding-program-code';
import {
  GuaranteeLetterTrackingDataTable,
  GuaranteeLetterTrackingForm,
} from '@/pages/guarantee-letter-tracking';
import { HistoryActionDataTable } from '@/pages/history-action';
import { InsuranceContributionReportDataTable } from '@/pages/insurance-contribution-report';
import { InvestmentFormDataTable } from '@/pages/investment-form';
import { InvestmentTypeDataTable } from '@/pages/investment-type';
import {
  OutstandingEquipmentDataTable,
  OutstandingEquipmentForm,
} from '@/pages/outstanding-equipment';
import { OvertimeAttendanceTrackingDataTable } from '@/pages/overtime-attendance-tracking/overtime-attendance-tracking-data-table';
import { PaymentBeneficiaryReportDataTable } from '@/pages/payment-beneficiary-report';
import { PaymentReceiptDataTable, PaymentReceiptForm } from '@/pages/payment-receipt';
import { PermissionGroupsPage } from '@/pages/permission-groups';
import { ProjectDebtStatusStatisticsReportDataTable } from '@/pages/project-debt-status-statistics-report';
import { ProjectDepartmentDisbursementProgressReportDataTable } from '@/pages/project-department-disbursement-progress-report';
import {
  ProjectDisbursementDataTable,
  ProjectDisbursementForm,
} from '@/pages/project-disbursement';
import {
  ProjectScheduleSetupDataTable,
  ProjectScheduleSetupForm,
} from '@/pages/project-schedule-setup';
import { ProjectDataTable } from '@/pages/project/project-data-table';
import { ProjectForm } from '@/pages/project/project-form';
import {
  ProposedSettlementInvestmentCostDataTable,
  ProposedSettlementInvestmentCostForm,
} from '@/pages/proposed-settlement-investment-cost';
import {
  ReportAnnex3aFinanceDataTable,
  ReportAnnex3aFinanceForm,
} from '@/pages/report-annex-3-a-finance';
import {
  ReportAnnex3aProjectManagementDataTable,
  ReportAnnex3aProjectManagementForm,
} from '@/pages/report-annex-3-a-project-management';
import {
  ReportPublicInvestmentSettlementDataTable,
  ReportPublicInvestmentSettlementForm,
} from '@/pages/report-public-investment-settlement';
import {
  ReportSerialManagementDataTable,
  ReportSerialManagementForm,
} from '@/pages/report-serial-management';
import { ReportTemplateDataTable, ReportTemplateForm } from '@/pages/report-template';
import { RptReportOnImplementationOfDirectivesTemplateDataTable } from '@/pages/rpt-report-on-implementation-of-directives';
import { SalarySheetDataTable, SalarySheetForm } from '@/pages/salary-sheet';
import { SalarySheetForLaborContractReportDataTable } from '@/pages/salary-sheet-for-labor-contract-report/salary-sheet-for-labor-contract-report-data-table';
import { SavingInBiddingReportDataTable } from '@/pages/saving-in-bidding-report/saving-in-bidding-report-data-table';
import { SavingRateContractorSelectionPlanReportDataTable } from '@/pages/saving-rate-contractor-selection-plan-report';
import { SectorCodeDataTable } from '@/pages/sector-code';
import { SetupAnnualHolidayDataTable, SetupAnnualHolidayForm } from '@/pages/setup-annual-holiday';
import { SpendingCommitmentDataTable, SpendingCommitmentForm } from '@/pages/spending-commitment';
import { StateManagementPage } from '@/pages/state-management';
import { StatusPage } from '@/pages/status';
import { RptSummaryTargetsTasksFirst6MonthsTemplateDataTable } from '@/pages/summary-targets-tasks-first-6-months-report';
import { RptSummaryTargetsTasksLast6MonthsTemplateDataTable } from '@/pages/summary-targets-tasks-last-6-months-report';
import { TargetDataTable, TargetForm } from '@/pages/target';
import { TargetsAndTasksDirectiveContent } from '@/pages/targets-and-tasks-directive-content';
import { TenderPackageDataTable, TenderPackageForm } from '@/pages/tender-package';
import { TrainingManagementDataTable, TrainingManagementForm } from '@/pages/training-management';
import { TypeCodePage } from '@/pages/type-code';
import { UIGenerateTool } from '@/pages/ui-generate-tool';
import { UnitPage } from '@/pages/unit';
import { UsersPage } from '@/pages/users';
import { WorkManagement } from '@/pages/work-management';
import {
  WorkManagementDesignBidEstimationDataTable,
  WorkManagementDesignBidEstimationForm,
} from '@/pages/work-management-design-bid-estimation';
import {
  WorkManagementDirectiveContentDataTable,
  WorkManagementDirectiveContentForm,
} from '@/pages/work-management-directive-content';
import {
  WorkManagementOtherDataTable,
  WorkManagementOtherForm,
} from '@/pages/work-management-other';
import {
  WorkManagementTargetDataTable,
  WorkManagementTargetForm,
} from '@/pages/work-management-target';
import { WorkManagementTaskDataTable, WorkManagementTaskForm } from '@/pages/work-management-task';
import { RptYearlySummaryTemplateDataTable } from '@/pages/yearly-summary-report/yearly-summary-report-data-table';

import { ContractorProjectStaffReportDataTable } from '@/pages/contractor-project-staff-report/contractor-project-staff-report-data-table';
import { AnnualBiddingSummaryReportDataTable } from '@/pages/contractor-selection-result-summary-by-year-report';
import { OvertimeAttendanceTrackingForm } from '@/pages/overtime-attendance-tracking/overtime-attendance-tracking-form';
import {
  WeeklyProjectSchedulePlanDataTable,
  WeeklyProjectSchedulePlanForm,
} from '@/pages/weekly-project-schedule-plan';

import Layout from '@/layout';
import { DirectiveImplementationDataTable } from '@/pages/directive-implementation/directive-implementation-data-table';
import { DirectiveImplementationForm } from '@/pages/directive-implementation/directive-implementation-form';
import {
  DisbursementProgressByFundingSourceDataTable,
  DisbursementProgressByFundingSourceForm,
} from '@/pages/disbursement-progress-by-funding-source';
import {
  DisbursementProgressSummaryDataTable,
  DisbursementProgressSummaryForm,
} from '@/pages/disbursement-progress-summary';
import { DocumentGroupPage } from '@/pages/document-group';
import {
  FinancialSettlementReportDataTable,
  FinancialSettlementReportForm,
} from '@/pages/financial-settlement-report';
import {
  MonthlyPlannedDisbursementDataTable,
  MonthlyPlannedDisbursementForm,
} from '@/pages/monthly-planned-disbursement';
import { ProjectDeploymentStatusReportDataTable } from '@/pages/project-deployment-status-report';
import { RptReportOnImplementationOfDirectivesByUserTemplateDataTable } from '@/pages/rpt-report-on-implementation-of-directives-by-user';
import { ProjectStatus } from '@/types';
import { LandAcquisitionAndCompensationProgressReportDataTable } from '@/pages/land-acquisition-and-compensation-progress';
import { BoardOfDirectorPage } from '@/pages/board-of-director';
import {
  TemplateStatisticReportDataTable,
  TemplateStatisticsReport,
} from '@/pages/template-statistics-report';
import { BankDataTable } from '@/pages/bank';
import {
  AdjustedCapitalIncreasePlanDataTable,
  AdjustedCapitalIncreasePlanForm,
} from '@/pages/adjusted-capital-increase-plan';
import {
  CompletionAcceptanceNoticeDataTable,
  CompletionAcceptanceNoticeForm,
} from '@/pages/completion-acceptance-notice';
import { ProjectSettlementDataTable, ProjectSettlementForm } from '@/pages/project-settlement';
import { TrainingTrackingDataTable, TrainingTrackingForm } from '@/pages/training-tracking';
import { FileSystemPage } from '@/pages/file-system';
import { ContractorParticipationInProjectReportDataTable } from '@/pages/contractor-participation-in-project-report';
import { AIWarningOfConstructionProgressDataTable } from '@/pages/ai-warning-of-construction-progress';
import {
  ContractorSelectionPlanTaskManagementDataTable,
  ContractorSelectionPlanTaskManagementForm,
} from '@/pages/contractor-selection-plan-task-management';
import { ProjectInformationSummaryReportDataTable } from '@/pages/project-information-summary-report/project-information-summary-report-data-table';
import { CompletionReportDataTable, CompletionReportForm } from '@/pages/completion-report';
import {
  EmploymentAcceptanceNoticeDataTable,
  EmploymentAcceptanceNoticeForm,
} from '@/pages/employment-acceptance-notice';
import {
  ComprehensiveInvestmentReportDataTable,
  ComprehensiveInvestmentReportForm,
} from '@/pages/comprehensive-investment-report';

type RouteObjectWithoutChildren = Omit<RouteObject, 'children'>;

export type CustomRouteType = RouteObjectWithoutChildren & {
  titleKey?: string;
  icon?: LucideIcon;
  hide?: boolean;
  permissions?: number[];
  children?: (RouteObjectWithoutChildren & CustomRouteType)[];
};

const routeConfig: CustomRouteType[] = [
  {
    element: (
      <DomainProvider>
        <AuthProvider>
          <Outlet />
        </AuthProvider>
      </DomainProvider>
    ),
    children: [
      {
        element: (
          <SignalRProvider hubUrl={`${import.meta.env.VITE_SIGNALR_HUB_URL}`}>
            <Outlet />
          </SignalRProvider>
        ),
        children: [
          {
            element: <Layout />,
            children: [
              {
                path: '/',
                titleKey: 'home',
                permissions: [],
                element: <Dashboard />,
                hide: true,
              },
              {
                path: '/user-profile',
                element: <UserProfile />,
                titleKey: 'userProfile',
                // permissions: [PERMISSIONS.USER_PROFILE],
                hide: true,
              },

              //báo cáo tổng hợp
              {
                path: '/summary-report',
                titleKey: 'summaryReport.name',
                permissions: [
                  PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR,
                  PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR,
                  PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR,
                  PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT_DIRECTOR,
                  PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR,
                  PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT_DIRECTOR,
                  PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR,
                  PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DIRECTOR,
                  PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT_DIRECTOR,
                  PERMISSIONS.SAVING_IN_BIDDING_REPORT_DIRECTOR,
                  PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT_DIRECTOR,
                ],
                icon: LineChart,
                children: [
                  //Báo cáo tiến độ giải ngân KH vốn
                  {
                    path: '/summary-report/capital-plan-disbursement-progress-report',
                    titleKey: 'capitalPlanDisbursementProgressReport',
                    permissions: [PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/capital-plan-disbursement-progress-report',
                        permissions: [
                          PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR,
                        ],
                        element: <CapitalPlanDisbursementProgressReportDataTable2 />,
                        hide: true,
                      },
                    ],
                  },
                  //  Bảng tiến độ giải ngân theo phòng QLDA
                  {
                    path: '/summary-report/project-department-disbursement-progress-report',
                    titleKey: 'projectDepartmentDisbursementProgressReport',
                    permissions: [
                      PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR,
                    ],
                    children: [
                      {
                        path: '/summary-report/project-department-disbursement-progress-report',
                        permissions: [
                          PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT_DIRECTOR,
                        ],
                        element: <ProjectDepartmentDisbursementProgressReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Bảng tóm tắt Tiến độ giải ngân
                  {
                    path: '/summary-report/disbursement-progress-summary',
                    titleKey: 'disbursementProgressBySummary',
                    permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/disbursement-progress-summary',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR],
                        element: (
                          <DisbursementProgressSummaryDataTable
                            path={PATHS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR}
                            permission={PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/summary-report/disbursement-progress-summary/:id',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR],
                        element: (
                          <DisbursementProgressSummaryForm
                            path={PATHS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR}
                            permission={PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR}
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  //Thống kê DA
                  {
                    path: '/summary-report/project-deployment-status-report',
                    titleKey: 'projectDeploymentStatusReport',
                    permissions: [PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/project-deployment-status-report',
                        permissions: [PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT_DIRECTOR],
                        element: <ProjectDeploymentStatusReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Tiến độ giải ngân theo Nguồn ngân sách
                  {
                    path: '/summary-report/disbursement-progress-by-funding-source',
                    titleKey: 'disbursementProgressByFundingSource',
                    permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/disbursement-progress-by-funding-source',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR],
                        element: (
                          <DisbursementProgressByFundingSourceDataTable
                            path={PATHS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR}
                            permission={
                              PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR
                            }
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/summary-report/disbursement-progress-by-funding-source/:id',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR],
                        element: (
                          <DisbursementProgressByFundingSourceForm
                            path={PATHS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR}
                            permission={
                              PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR
                            }
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  // Báo cáo tiến độ thực hiện BTGPMB
                  {
                    path: '/summary-report/land-acquisition-and-compensation-progress',
                    titleKey: 'landAcquisitionAndCompensationProgressReport',
                    permissions: [
                      PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT_DIRECTOR,
                    ],
                    children: [
                      {
                        path: '/summary-report/land-acquisition-and-compensation-progress',
                        permissions: [
                          PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT_DIRECTOR,
                        ],
                        element: <LandAcquisitionAndCompensationProgressReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  //Quản lý dự án tồn đọng
                  {
                    path: '/summary-report/backlog-project-management',
                    titleKey: 'backlogProjectManagement',
                    permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/backlog-project-management',
                        permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR],
                        element: (
                          <BacklogProjectManagementDataTable
                            path={PATHS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR}
                            permission={PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/summary-report/backlog-project-management/:id',
                        permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR],
                        element: (
                          <BacklogProjectManagementForm
                            path={PATHS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR}
                            permission={PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR}
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  // Tỷ lệ tiết kiệm trong công tác LCNT
                  {
                    path: '/summary-report/saving-rate-contractor-selection-plan-report',
                    titleKey: 'savingRateContractorSelectionPlanReport',
                    permissions: [
                      PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DIRECTOR,
                    ],
                    children: [
                      {
                        path: '/summary-report/saving-rate-contractor-selection-plan-report',
                        permissions: [
                          PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DIRECTOR,
                        ],
                        element: <SavingRateContractorSelectionPlanReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Danh sách nhà thầu tham gia thực hiện gói thầu
                  {
                    path: '/summary-report/contractor-participation-tender-package-list-report',
                    titleKey: 'contractorParticipationTenderPackageListReport',
                    permissions: [
                      PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT_DIRECTOR,
                    ],
                    children: [
                      {
                        path: '/summary-report/contractor-participation-tender-package-list-report',
                        permissions: [
                          PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT_DIRECTOR,
                        ],
                        element: <ContractorParticipationTenderPackageListReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Báo cáo tiết kiệm trong đấu thầu
                  {
                    path: '/summary-report/saving-in-bidding-report',
                    titleKey: 'savingInBiddingReport',
                    permissions: [PERMISSIONS.SAVING_IN_BIDDING_REPORT_DIRECTOR],
                    children: [
                      {
                        path: '/summary-report/saving-in-bidding-report',
                        permissions: [PERMISSIONS.SAVING_IN_BIDDING_REPORT_DIRECTOR],
                        element: <SavingInBiddingReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Báo cáo Tổng hợp đấu thầu hằng năm
                  {
                    path: '/summary-report/contractor-selection-result-summary-by-year-report',
                    titleKey: 'contractorSelectionResultByDepartmentReport',
                    permissions: [
                      PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT_DIRECTOR,
                    ],
                    children: [
                      {
                        path: '/summary-report/contractor-selection-result-summary-by-year-report',
                        permissions: [
                          PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT_DIRECTOR,
                        ],
                        element: <AnnualBiddingSummaryReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  // Tóm tắt thông tin dự án
                  {
                    path: '/summary-report/project-information-summary-report',
                    titleKey: 'projectInformationSummaryReport',
                    permissions: [PERMISSIONS.PROJECT_INFORMATION_SUMMARY_REPORT],
                    children: [
                      {
                        path: '/summary-report/project-information-summary-report',
                        permissions: [PERMISSIONS.PROJECT_INFORMATION_SUMMARY_REPORT],
                        element: <ProjectInformationSummaryReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                ],
              },
              //quản lý dự án
              {
                path: '/project-management',
                titleKey: 'projectManagement.name',
                permissions: [
                  PERMISSIONS.PROJECT,
                  PERMISSIONS.CONTRACTOR_SELECTION_PLAN,
                  PERMISSIONS.TENDER_PACKAGE,
                  PERMISSIONS.CONTRACT,
                  PERMISSIONS.CONTRACT_APPENDIX,
                  PERMISSIONS.COMPLETION_ACCEPTANCE,
                  PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE,
                  PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE,
                  PERMISSIONS.COMPLETION_REPORT,
                  PERMISSIONS.PROJECT_SETTLEMENT,
                  PERMISSIONS.ADJUSTED_COST_ESTIMATION,
                  PERMISSIONS.ADJUSTED_INVESTMENT,
                  PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN,
                  PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT,
                  PERMISSIONS.CONTRACTOR_SELECTION_RESULT,
                  PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT,
                  PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_YEAR_REPORT,
                  PERMISSIONS.CONTRACTOR_PROJECT_STAFF_REPORT,
                  PERMISSIONS.MONTHLY_PLANNED_DISBURSEMENT,
                  PERMISSIONS.PROJECT_DISBURSEMENT,
                  PERMISSIONS.CONTRACTOR_PARTICIPATION_IN_PROJECT_REPORT,
                ],
                icon: Building,
                children: [
                  {
                    // Dự án
                    path: '/project-management/project',
                    permissions: [
                      PERMISSIONS.PROJECT,
                      PERMISSIONS.CONTRACTOR_SELECTION_PLAN,
                      PERMISSIONS.TENDER_PACKAGE,
                      PERMISSIONS.CONTRACT,
                      PERMISSIONS.CONTRACT_APPENDIX,
                      PERMISSIONS.COMPLETION_ACCEPTANCE,
                      PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE,
                      PERMISSIONS.COMPLETION_REPORT,
                      PERMISSIONS.PROJECT_SETTLEMENT,
                      PERMISSIONS.ADJUSTED_COST_ESTIMATION,
                      PERMISSIONS.ADJUSTED_INVESTMENT,
                      PERMISSIONS.AI_WARNING_OF_CONSTRUCTION_PROGRESS,
                    ],
                    titleKey: 'projectManagement.project',
                    children: [
                      {
                        index: true,
                        path: '/project-management/project/info',
                        permissions: [PERMISSIONS.PROJECT],
                        titleKey: 'projectManagement.projectInfo',
                        element: <ProjectDataTable />,
                      },
                      {
                        path: '/project-management/project/info/:id',
                        permissions: [PERMISSIONS.PROJECT],
                        element: <ProjectForm />,
                        hide: true,
                      },

                      ...PROJECT_STATUS.map((item: ProjectStatus) => {
                        let permissions = [
                          PERMISSIONS.TENDER_PACKAGE,
                          PERMISSIONS.CONTRACT,
                          PERMISSIONS.CONTRACT_APPENDIX,
                          PERMISSIONS.COMPLETION_ACCEPTANCE,
                        ];
                        if (item.code !== PROJECT_STATUS_COMPLETED.code) {
                          permissions = [...permissions, PERMISSIONS.CONTRACTOR_SELECTION_PLAN];
                        }
                        if (item.code === PROJECT_STATUS_COMPLETED.code) {
                          permissions = [
                            ...permissions,
                            PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE,
                            PERMISSIONS.COMPLETION_REPORT,
                            PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE,
                            PERMISSIONS.PROJECT_SETTLEMENT,
                          ];
                        }
                        return {
                          path: `/project-management/project/${item.code}`,
                          titleKey: item.name,
                          permissions: permissions,
                          children: [
                            ...(item.code === PROJECT_STATUS_COMPLETED.code
                              ? []
                              : [
                                  {
                                    path: `/project-management/project/${item.code}/contractor-selection-plan`,
                                    titleKey: 'operations.project.contractorSelectionPlan',
                                    permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN],
                                    children: [
                                      {
                                        path: `/project-management/project/${item.code}/contractor-selection-plan`,
                                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN],
                                        element: (
                                          <ContractorSelectionPlanDataTable projectStatus={item} />
                                        ),
                                        hide: true,
                                      },

                                      {
                                        path: `/project-management/project/${item.code}/contractor-selection-plan/:id`,
                                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN],
                                        element: (
                                          <ContractorSelectionPlanForm projectStatus={item} />
                                        ),
                                        hide: true,
                                      },
                                    ],
                                  },
                                ]),
                            {
                              path: `/project-management/project/${item.code}/tender-package`,
                              titleKey: 'operations.project.tenderPackage',
                              permissions: [PERMISSIONS.TENDER_PACKAGE],
                              children: [
                                {
                                  path: `/project-management/project/${item.code}/tender-package`,
                                  permissions: [PERMISSIONS.TENDER_PACKAGE],
                                  element: <TenderPackageDataTable projectStatus={item} />,
                                  hide: true,
                                },
                                {
                                  path: `/project-management/project/${item.code}/tender-package/:id`,
                                  permissions: [PERMISSIONS.TENDER_PACKAGE],
                                  element: <TenderPackageForm projectStatus={item} />,
                                  hide: true,
                                },
                              ],
                            },
                            {
                              path: `/project-management/project/${item.code}/contract`,
                              titleKey: 'operations.project.contract',
                              permissions: [PERMISSIONS.CONTRACT],
                              children: [
                                {
                                  path: `/project-management/project/${item.code}/contract`,
                                  permissions: [PERMISSIONS.CONTRACT],
                                  element: <ContractDataTable projectStatus={item} />,
                                  hide: true,
                                },
                                {
                                  path: `/project-management/project/${item.code}/contract/:id`,
                                  permissions: [PERMISSIONS.CONTRACT],
                                  element: <ContractForm projectStatus={item} />,
                                  hide: true,
                                },
                              ],
                            },
                            {
                              path: `/project-management/project/${item.code}/contract-appendix`,
                              titleKey: 'operations.project.contractAppendix',
                              permissions: [PERMISSIONS.CONTRACT_APPENDIX],
                              children: [
                                {
                                  path: `/project-management/project/${item.code}/contract-appendix`,
                                  permissions: [PERMISSIONS.CONTRACT_APPENDIX],
                                  element: <ContractAppendixDataTable projectStatus={item} />,
                                  hide: true,
                                },
                                {
                                  path: `/project-management/project/${item.code}/contract-appendix/:id`,
                                  permissions: [PERMISSIONS.CONTRACT_APPENDIX],
                                  element: <ContractAppendixForm projectStatus={item} />,
                                  hide: true,
                                },
                              ],
                            },
                            {
                              path: `/project-management/project/${item.code}/completion-acceptance`,
                              titleKey: 'operations.project.completionAcceptance',
                              permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE],
                              children: [
                                {
                                  path: `/project-management/project/${item.code}/completion-acceptance`,
                                  permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE],
                                  element: <CompletionAcceptanceDataTable projectStatus={item} />,
                                  hide: true,
                                },
                                {
                                  path: `/project-management/project/${item.code}/completion-acceptance/:id`,
                                  permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE],
                                  element: <CompletionAcceptanceForm projectStatus={item} />,
                                  hide: true,
                                },
                              ],
                            },
                            ...(item.code === PROJECT_STATUS_COMPLETED.code
                              ? [
                                  {
                                    path: `/project-management/project/${item.code}/report`,
                                    titleKey: 'operations.project.completionAcceptanceNotice',
                                    permissions: [
                                      PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE,
                                      PERMISSIONS.COMPLETION_REPORT,
                                      PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE,
                                    ],
                                    children: [
                                      {
                                        path: `/project-management/project/${item.code}/report/completion-acceptance-notice`,
                                        titleKey: 'operations.project.completionAcceptanceNotice',
                                        permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE],
                                        children: [
                                          {
                                            path: `/project-management/project/${item.code}/report/completion-acceptance-notice`,
                                            permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE],
                                            element: (
                                              <CompletionAcceptanceNoticeDataTable
                                                projectStatus={item}
                                              />
                                            ),
                                            hide: true,
                                          },

                                          {
                                            path: `/project-management/project/${item.code}/report/completion-acceptance-notice/:id`,
                                            permissions: [PERMISSIONS.COMPLETION_ACCEPTANCE_NOTICE],
                                            element: (
                                              <CompletionAcceptanceNoticeForm
                                                projectStatus={item}
                                              />
                                            ),
                                            hide: true,
                                          },
                                        ],
                                      },
                                      {
                                        path: `/project-management/project/${item.code}/report/completion-report`,
                                        titleKey: 'operations.project.completionReport',
                                        permissions: [PERMISSIONS.COMPLETION_REPORT],
                                        children: [
                                          {
                                            path: `/project-management/project/${item.code}/report/completion-report`,
                                            permissions: [PERMISSIONS.COMPLETION_REPORT],
                                            element: (
                                              <CompletionReportDataTable projectStatus={item} />
                                            ),
                                            hide: true,
                                          },

                                          {
                                            path: `/project-management/project/${item.code}/report/completion-report/:id`,
                                            permissions: [PERMISSIONS.COMPLETION_REPORT],
                                            element: <CompletionReportForm projectStatus={item} />,
                                            hide: true,
                                          },
                                        ],
                                      },
                                      {
                                        path: `/project-management/project/${item.code}/report/employment-acceptance-notice`,
                                        titleKey: 'operations.project.employmentAcceptanceNotice',
                                        permissions: [PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE],
                                        children: [
                                          {
                                            path: `/project-management/project/${item.code}/report/employment-acceptance-notice`,
                                            permissions: [PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE],
                                            element: (
                                              <EmploymentAcceptanceNoticeDataTable
                                                projectStatus={item}
                                              />
                                            ),
                                            hide: true,
                                          },

                                          {
                                            path: `/project-management/project/${item.code}/report/employment-acceptance-notice/:id`,
                                            permissions: [PERMISSIONS.EMPLOYMENT_ACCEPTANCE_NOTICE],
                                            element: (
                                              <EmploymentAcceptanceNoticeForm
                                                projectStatus={item}
                                              />
                                            ),
                                            hide: true,
                                          },
                                        ],
                                      },
                                    ],
                                  },

                                  {
                                    path: `/project-management/project/${item.code}/project-settlement`,
                                    titleKey: 'operations.project.projectSettlement',
                                    permissions: [PERMISSIONS.PROJECT_SETTLEMENT],
                                    children: [
                                      {
                                        path: `/project-management/project/${item.code}/project-settlement`,
                                        permissions: [PERMISSIONS.PROJECT_SETTLEMENT],
                                        element: (
                                          <ProjectSettlementDataTable projectStatus={item} />
                                        ),
                                        hide: true,
                                      },

                                      {
                                        path: `/project-management/project/${item.code}/project-settlement/:id`,
                                        permissions: [PERMISSIONS.PROJECT_SETTLEMENT],
                                        element: <ProjectSettlementForm projectStatus={item} />,
                                        hide: true,
                                      },
                                    ],
                                  },
                                ]
                              : []),
                          ],
                        };
                      }),
                      // Dự toán điều chỉnh
                      {
                        path: '/project-management/project/adjusted-cost-estimation',
                        titleKey: 'projectManagement.adjustedCostEstimation',
                        permissions: [PERMISSIONS.ADJUSTED_COST_ESTIMATION],
                        children: [
                          {
                            path: '/project-management/project/adjusted-cost-estimation',
                            titleKey: 'adjustedCostEstimation',
                            permissions: [PERMISSIONS.ADJUSTED_COST_ESTIMATION],
                            element: <AdjustedCostEstimationDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/project/adjusted-cost-estimation/:id',
                            titleKey: 'adjustedCostEstimation',
                            icon: BarChartHorizontal,
                            permissions: [PERMISSIONS.ADJUSTED_COST_ESTIMATION],
                            element: <AdjustedCostEstimationForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Tổng mức đầu tư điều chỉnh
                      {
                        path: '/project-management/project/adjusted-investment',
                        titleKey: 'projectManagement.adjustedInvestment',
                        permissions: [PERMISSIONS.ADJUSTED_INVESTMENT],
                        children: [
                          {
                            path: '/project-management/project/adjusted-investment',
                            titleKey: 'adjustedInvestment',
                            permissions: [PERMISSIONS.ADJUSTED_INVESTMENT],
                            element: <AdjustedInvestmentDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/project/adjusted-investment/:id',
                            titleKey: 'adjustedInvestment',
                            permissions: [PERMISSIONS.ADJUSTED_INVESTMENT],
                            element: <AdjustedInvestmentForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Quản lý tài liệu
                      {
                        path: '/project-management/project/file-system',
                        titleKey: 'fileManager',
                        permissions: [], // không cần quyền
                        element: <FileSystemPage />,
                      },
                      // Dự báo cảnh báo tiến độ thi công
                      {
                        path: '/project-management/project/ai-warning-of-construction-progress',
                        titleKey: 'projectManagement.aiWarningOfConstructionProgress',
                        permissions: [], // không cần quyền
                        element: <AIWarningOfConstructionProgressDataTable />,
                      },
                    ],
                  },
                  {
                    // Báo cáo
                    path: '/project-management/report',
                    permissions: [
                      PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN,
                      PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT,
                      PERMISSIONS.CONTRACTOR_SELECTION_RESULT,
                      PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT,
                      PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_YEAR_REPORT,
                      PERMISSIONS.CONTRACTOR_PROJECT_STAFF_REPORT,
                      PERMISSIONS.MONTHLY_PLANNED_DISBURSEMENT,
                      PERMISSIONS.PROJECT_DISBURSEMENT,
                      PERMISSIONS.CONTRACTOR_PARTICIPATION_IN_PROJECT_REPORT,
                    ],
                    titleKey: 'projectManagement.report',
                    children: [
                      // Báo cáo tiến độ DA hàng tuần
                      {
                        path: '/project-management/report/weekly-project-schedule-plan',
                        titleKey: 'weeklyProjectSchedulePlan',
                        permissions: [PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN],
                        children: [
                          {
                            path: '/project-management/report/weekly-project-schedule-plan',
                            permissions: [PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN],
                            element: <WeeklyProjectSchedulePlanDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/report/weekly-project-schedule-plan/:id',
                            permissions: [PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN],
                            element: <WeeklyProjectSchedulePlanForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Báo cáo phụ lục 3A (QLDA)
                      {
                        path: '/project-management/report/report-annex-3a-project-management',
                        titleKey: 'generalFinance.reportAnnex3a',
                        permissions: [PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
                        children: [
                          {
                            path: '/project-management/report/report-annex-3a-project-management',
                            permissions: [PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
                            element: <ReportAnnex3aProjectManagementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/report/report-annex-3a-project-management/:id',
                            permissions: [PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT],
                            element: <ReportAnnex3aProjectManagementForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Tổng hợp KQLCNT
                      {
                        path: '/project-management/report/contractor-selection-result',
                        titleKey: 'contractorSelectionResult',
                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_RESULT],
                        children: [
                          {
                            path: '/project-management/report/contractor-selection-result',
                            permissions: [PERMISSIONS.CONTRACTOR_SELECTION_RESULT],
                            element: <ContractorSelectionResultDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/report/contractor-selection-result/:id',
                            permissions: [PERMISSIONS.CONTRACTOR_SELECTION_RESULT],
                            element: <ContractorSelectionResultForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Báo cáo tổng hợp KQLCNT
                      {
                        path: '/project-management/report/contractor-selection-result-by-managing-director-report',
                        titleKey: 'contractorSelectionResultByManagingDirectorReport',
                        permissions: [
                          PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT,
                        ],
                        children: [
                          {
                            path: '/project-management/report/contractor-selection-result-by-managing-director-report',
                            permissions: [
                              PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT,
                            ],
                            element: <ContractorSelectionResultByManagingDirectorReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Báo cáo đấu thầu hằng năm
                      {
                        path: '/project-management/report/contractor-selection-result-by-year-report',
                        titleKey: 'contractorSelectionResultByYearReport',
                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_YEAR_REPORT],
                        children: [
                          {
                            path: '/project-management/report/contractor-selection-result-by-year-report',
                            permissions: [PERMISSIONS.CONTRACTOR_SELECTION_RESULT_BY_YEAR_REPORT],
                            element: <ContractorSelectionResultByYearReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Nhân sự Nhà thầu tham dự
                      {
                        path: '/project-management/report/contractor-project-staff-report',
                        titleKey: 'hr.contractorProjectStaffReport',
                        permissions: [PERMISSIONS.CONTRACTOR_PROJECT_STAFF_REPORT],
                        element: <ContractorProjectStaffReportDataTable />,
                      },
                      //Dự kiến giải ngân theo tháng
                      {
                        path: '/project-management/report/monthly-planned-disbursement',
                        titleKey: 'generalFinance.monthlyPlannedDisbursement',
                        permissions: [PERMISSIONS.MONTHLY_PLANNED_DISBURSEMENT],
                        children: [
                          {
                            path: '/project-management/report/monthly-planned-disbursement',
                            permissions: [PERMISSIONS.MONTHLY_PLANNED_DISBURSEMENT],
                            element: <MonthlyPlannedDisbursementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/report/monthly-planned-disbursement/:id',
                            permissions: [PERMISSIONS.MONTHLY_PLANNED_DISBURSEMENT],
                            element: <MonthlyPlannedDisbursementForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Báo cáo giải ngân theo dự án
                      {
                        path: '/project-management/report/project-disbursement',
                        titleKey: 'projectDisbursement',
                        permissions: [PERMISSIONS.PROJECT_DISBURSEMENT],
                        children: [
                          {
                            path: '/project-management/report/project-disbursement',
                            permissions: [PERMISSIONS.PROJECT_DISBURSEMENT],
                            element: <ProjectDisbursementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/project-management/report/project-disbursement/:id',
                            permissions: [PERMISSIONS.PROJECT_DISBURSEMENT],
                            element: <ProjectDisbursementForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Thống kê nhà thầu tham gia dự án
                      {
                        path: '/project-management/report/contractor-participation-in-project-report',
                        titleKey: 'contractorParticipationInProjectReport',
                        permissions: [PERMISSIONS.CONTRACTOR_PARTICIPATION_IN_PROJECT_REPORT],
                        element: <ContractorParticipationInProjectReportDataTable />,
                      },
                    ],
                  },
                ],
              },

              //Tài chính tổng hợp
              {
                path: '/general-finance',
                titleKey: 'generalFinance.name',
                icon: BarChartHorizontal,
                permissions: [
                  PERMISSIONS.CAPITAL_INCREASE_PLAN,
                  PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN,
                  PERMISSIONS.REPORT_ANNEX_3A_FINANCE,
                  PERMISSIONS.SPENDING_COMMITMENT,
                  PERMISSIONS.PAYMENT_RECEIPT,
                  PERMISSIONS.GUARANTEE_LETTER_TRACKING,
                  PERMISSIONS.A_B_SETTLEMENT,
                  PERMISSIONS.A_B_ADJUSTMENT_SETTLEMENT,
                  PERMISSIONS.CONTRACT_SETTLEMENT,
                  PERMISSIONS.SALARY_SHEET,
                  PERMISSIONS.EMPLOYEE_PAYROLL_REPORT,
                  PERMISSIONS.SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT,
                  PERMISSIONS.INSURANCE_CONTRIBUTION_REPORT,
                  PERMISSIONS.PAYMENT_BENEFICIARY_REPORT,
                  PERMISSIONS.ADVANCE_PAYMENT,
                  PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT,
                  PERMISSIONS.REPORT_PUBLIC_INVESTMENT_SETTLEMENT,
                  PERMISSIONS.ASSET_INCREMENT,
                  PERMISSIONS.OUTSTANDING_EQUIPMENT,
                  PERMISSIONS.DATA_RECONCILIATION_TABLE,
                  PERMISSIONS.PROJECT_DEBT_STATUS_STATISTICS_REPORT,
                  PERMISSIONS.PROPOSED_SETTLEMENT_INVESTMENT_COST,
                ],
                children: [
                  // Kế hoạch vốn
                  {
                    path: '/general-finance/capital-plan',
                    titleKey: 'capitalPlan',
                    permissions: [
                      PERMISSIONS.CAPITAL_INCREASE_PLAN,
                      PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN,
                    ],
                    children: [
                      // Kế hoạch giao vốn
                      {
                        path: '/general-finance/capital-plan/capital-increase-plan',
                        titleKey: 'capitalIncreasePlan',
                        permissions: [PERMISSIONS.CAPITAL_INCREASE_PLAN],
                        children: [
                          {
                            path: '/general-finance/capital-plan/capital-increase-plan',
                            permissions: [PERMISSIONS.CAPITAL_INCREASE_PLAN],
                            element: <CapitalIncreasePlanDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/capital-plan/capital-increase-plan/:id',
                            permissions: [PERMISSIONS.CAPITAL_INCREASE_PLAN],
                            element: <CapitalIncreasePlanForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Kế hoạch giao vốn điều chỉnh
                      {
                        path: '/general-finance/capital-plan/adjusted-capital-increase-plan',
                        titleKey: 'adjustedCapitalIncreasePlan',
                        permissions: [PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN],
                        children: [
                          {
                            path: '/general-finance/capital-plan/adjusted-capital-increase-plan',
                            permissions: [PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN],
                            element: <AdjustedCapitalIncreasePlanDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/capital-plan/adjusted-capital-increase-plan/:id',
                            permissions: [PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN],
                            element: <AdjustedCapitalIncreasePlanForm />,
                            hide: true,
                          },
                        ],
                      },
                    ],
                  },

                  // Dự án
                  {
                    path: '/general-finance/project',
                    titleKey: 'generalFinance.project',
                    permissions: [
                      PERMISSIONS.REPORT_ANNEX_3A_FINANCE,
                      PERMISSIONS.SPENDING_COMMITMENT,
                      PERMISSIONS.PAYMENT_RECEIPT,
                      PERMISSIONS.GUARANTEE_LETTER_TRACKING,
                      PERMISSIONS.A_B_SETTLEMENT,
                      PERMISSIONS.A_B_ADJUSTMENT_SETTLEMENT,
                      PERMISSIONS.CONTRACT_SETTLEMENT,
                    ],
                    children: [
                      //Báo cáo phụ lục 3A (Kế toán)
                      {
                        path: '/general-finance/project/report-annex-3a-finance',
                        titleKey: 'generalFinance.reportAnnex3a',
                        permissions: [PERMISSIONS.REPORT_ANNEX_3A_FINANCE],
                        children: [
                          {
                            path: '/general-finance/project/report-annex-3a-finance',
                            permissions: [PERMISSIONS.REPORT_ANNEX_3A_FINANCE],
                            element: <ReportAnnex3aFinanceDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/report-annex-3a-finance/:id',
                            permissions: [PERMISSIONS.REPORT_ANNEX_3A_FINANCE],
                            element: <ReportAnnex3aFinanceForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Cam kết chi
                      {
                        path: '/general-finance/project/spending-commitment',
                        titleKey: 'spendingCommitment',
                        permissions: [PERMISSIONS.SPENDING_COMMITMENT],
                        children: [
                          {
                            path: '/general-finance/project/spending-commitment',
                            permissions: [PERMISSIONS.SPENDING_COMMITMENT],
                            element: <SpendingCommitmentDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/spending-commitment/:id',
                            permissions: [PERMISSIONS.SPENDING_COMMITMENT],
                            element: <SpendingCommitmentForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Chứng từ thanh toán
                      {
                        path: '/general-finance/project/payment-receipt',
                        titleKey: 'paymentReceipt',
                        permissions: [PERMISSIONS.PAYMENT_RECEIPT],
                        children: [
                          {
                            path: '/general-finance/project/payment-receipt',
                            permissions: [PERMISSIONS.PAYMENT_RECEIPT],
                            element: <PaymentReceiptDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/payment-receipt/:id',
                            permissions: [PERMISSIONS.PAYMENT_RECEIPT],
                            element: <PaymentReceiptForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Theo dõi thư bảo lãnh
                      {
                        path: '/general-finance/project/guarantee-letter-tracking',
                        titleKey: 'guaranteeLetterTracking',
                        permissions: [PERMISSIONS.GUARANTEE_LETTER_TRACKING],
                        children: [
                          {
                            path: '/general-finance/project/guarantee-letter-tracking',
                            titleKey: 'guaranteeLetterTracking',
                            permissions: [PERMISSIONS.GUARANTEE_LETTER_TRACKING],
                            element: <GuaranteeLetterTrackingDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/guarantee-letter-tracking/:id',
                            titleKey: 'guaranteeLetterTracking',
                            permissions: [PERMISSIONS.GUARANTEE_LETTER_TRACKING],
                            element: <GuaranteeLetterTrackingForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Quyết toán A - B
                      {
                        path: '/general-finance/project/a-b-settlement',
                        titleKey: 'generalFinance.aBSettlement',
                        permissions: [PERMISSIONS.A_B_SETTLEMENT],
                        children: [
                          {
                            path: '/general-finance/project/a-b-settlement',
                            permissions: [PERMISSIONS.A_B_SETTLEMENT],
                            element: <ABSettlementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/a-b-settlement/:id',
                            permissions: [PERMISSIONS.A_B_SETTLEMENT],
                            element: <ABSettlementForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Điều chỉnh khối lượng QT
                      {
                        path: '/general-finance/project/a-b-adjustment-settlement',
                        titleKey: 'generalFinance.aBAdjustmentSettlement',
                        permissions: [PERMISSIONS.A_B_ADJUSTMENT_SETTLEMENT],
                        children: [
                          {
                            path: '/general-finance/project/a-b-adjustment-settlement',
                            permissions: [PERMISSIONS.A_B_ADJUSTMENT_SETTLEMENT],
                            element: <ABAdjustmentSettlementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/a-b-adjustment-settlement/:id',
                            permissions: [PERMISSIONS.A_B_ADJUSTMENT_SETTLEMENT],
                            element: <ABAdjustmentSettlementForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Thanh lý hợp đồng
                      {
                        path: '/general-finance/project/contract-settlement',
                        titleKey: 'generalFinance.contractSettlement',
                        permissions: [PERMISSIONS.CONTRACT_SETTLEMENT],
                        children: [
                          {
                            path: '/general-finance/project/contract-settlement',
                            permissions: [PERMISSIONS.CONTRACT_SETTLEMENT],
                            element: <ContractSettlementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/project/contract-settlement/:id',
                            permissions: [PERMISSIONS.CONTRACT_SETTLEMENT],
                            element: <ContractSettlementForm />,
                            hide: true,
                          },
                        ],
                      },
                    ],
                  },

                  // Bảng lương VC, NLĐ
                  {
                    path: '/general-finance/payroll',
                    titleKey: 'generalFinance.payroll',
                    permissions: [
                      PERMISSIONS.SALARY_SHEET,
                      PERMISSIONS.EMPLOYEE_PAYROLL_REPORT,
                      PERMISSIONS.SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT,
                      PERMISSIONS.INSURANCE_CONTRIBUTION_REPORT,
                      PERMISSIONS.PAYMENT_BENEFICIARY_REPORT,
                    ],
                    children: [
                      // Bảng lương
                      {
                        path: '/general-finance/payroll/salary-sheet',
                        titleKey: 'hr.salarySheet',
                        permissions: [PERMISSIONS.SALARY_SHEET],
                        children: [
                          {
                            path: '/general-finance/payroll/salary-sheet',
                            permissions: [PERMISSIONS.SALARY_SHEET],
                            element: <SalarySheetDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/payroll/salary-sheet/:id',
                            permissions: [PERMISSIONS.SALARY_SHEET],
                            element: <SalarySheetForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Bảng lương Viên chức
                      {
                        path: '/general-finance/payroll/employee-payroll-report',
                        titleKey: 'hr.employeePayrollReport',
                        permissions: [PERMISSIONS.EMPLOYEE_PAYROLL_REPORT],
                        element: <EmployeePayrollReportDataTable />,
                      },
                      {
                        // Bảng lương Người lao động
                        path: '/general-finance/payroll/salary-sheet-for-labor-contract',
                        titleKey: 'hr.salarySheetForLaborContractReport',
                        permissions: [PERMISSIONS.SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT],
                        element: <SalarySheetForLaborContractReportDataTable />,
                      },
                      {
                        // Danh sách nộp bảo hiểm
                        path: '/general-finance/payroll/insurance-contribution-report',
                        titleKey: 'hr.insuranceContributionReport',
                        permissions: [PERMISSIONS.INSURANCE_CONTRIBUTION_REPORT],
                        element: <InsuranceContributionReportDataTable />,
                      },
                      {
                        // Bảng thanh toán cho đối tượng thụ hưởng
                        path: '/general-finance/payroll/payment-beneficiary-report',
                        titleKey: 'hr.paymentBeneficiaryReport',
                        permissions: [PERMISSIONS.PAYMENT_BENEFICIARY_REPORT],
                        element: <PaymentBeneficiaryReportDataTable />,
                      },
                    ],
                  },

                  // Báo cáo P. TCTH
                  {
                    path: '/general-finance/reports',
                    titleKey: 'generalFinance.reports',
                    permissions: [
                      PERMISSIONS.ADVANCE_PAYMENT,
                      PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT,
                      PERMISSIONS.REPORT_PUBLIC_INVESTMENT_SETTLEMENT,
                    ],
                    children: [
                      // Báo cáo tình hình số dư tạm ứng
                      {
                        path: '/general-finance/reports/advance-payment',
                        titleKey: 'advancePayment',
                        permissions: [PERMISSIONS.ADVANCE_PAYMENT],
                        children: [
                          {
                            path: '/general-finance/reports/advance-payment',
                            permissions: [PERMISSIONS.ADVANCE_PAYMENT],
                            element: <AdvancePaymentDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/reports/advance-payment/:id',
                            permissions: [PERMISSIONS.ADVANCE_PAYMENT],
                            element: <AdvancePaymentForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Báo cáo quyết toán dự án
                      {
                        path: '/general-finance/reports/financial-settlement-report',
                        titleKey: 'generalFinance.financialSettlementReport',
                        permissions: [PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT],
                        children: [
                          {
                            path: '/general-finance/reports/financial-settlement-report',
                            permissions: [PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT],
                            element: <FinancialSettlementReportDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/reports/financial-settlement-report/:id',
                            permissions: [PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT],
                            element: <FinancialSettlementReportForm />,
                            hide: true,
                          },
                        ],
                      },
                      //Báo cáo Quyết toán NSNN theo năm
                      {
                        path: '/general-finance/reports/report-public-investment-settlement',
                        titleKey: 'generalFinance.reportPublicInvestmentSettlement',
                        permissions: [PERMISSIONS.REPORT_PUBLIC_INVESTMENT_SETTLEMENT],
                        children: [
                          {
                            path: '/general-finance/reports/report-public-investment-settlement',
                            permissions: [PERMISSIONS.REPORT_PUBLIC_INVESTMENT_SETTLEMENT],
                            element: <ReportPublicInvestmentSettlementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/general-finance/reports/report-public-investment-settlement/:id',
                            permissions: [PERMISSIONS.REPORT_PUBLIC_INVESTMENT_SETTLEMENT],
                            element: <ReportPublicInvestmentSettlementForm />,
                            hide: true,
                          },
                        ],
                      },
                    ],
                  },

                  // Ghi tăng tài sản
                  {
                    path: '/general-finance/asset-increment',
                    titleKey: 'assetIncrement',
                    permissions: [PERMISSIONS.ASSET_INCREMENT],
                    children: [
                      {
                        path: '/general-finance/asset-increment',
                        titleKey: 'assetIncrement',
                        permissions: [PERMISSIONS.ASSET_INCREMENT],
                        element: <AssetIncrementDataTable />,
                        hide: true,
                      },
                      {
                        path: '/general-finance/asset-increment/:id',
                        titleKey: 'assetIncrement',
                        permissions: [PERMISSIONS.ASSET_INCREMENT],
                        element: <AssetIncrementForm />,
                        hide: true,
                      },
                    ],
                  },

                  //Thiết bị tồn đọng
                  {
                    path: '/general-finance/outstanding-equipment',
                    titleKey: 'outstandingEquipment',
                    permissions: [PERMISSIONS.OUTSTANDING_EQUIPMENT],
                    children: [
                      {
                        path: '/general-finance/outstanding-equipment',
                        titleKey: 'outstandingEquipment',
                        permissions: [PERMISSIONS.OUTSTANDING_EQUIPMENT],
                        element: <OutstandingEquipmentDataTable />,
                        hide: true,
                      },
                      {
                        path: '/general-finance/outstanding-equipment/:id',
                        titleKey: 'outstandingEquipment',
                        permissions: [PERMISSIONS.OUTSTANDING_EQUIPMENT],
                        element: <OutstandingEquipmentForm />,
                        hide: true,
                      },
                    ],
                  },
                  // Bảng đối chiều số liệu
                  {
                    path: '/general-finance/data-reconciliation-table',
                    titleKey: 'dataReconciliationTable',
                    permissions: [PERMISSIONS.DATA_RECONCILIATION_TABLE],
                    children: [
                      {
                        path: '/general-finance/data-reconciliation-table',
                        permissions: [PERMISSIONS.DATA_RECONCILIATION_TABLE],
                        element: <DataReconciliationTableDataTable />,
                        hide: true,
                      },
                      {
                        path: '/general-finance/data-reconciliation-table/:id',
                        permissions: [PERMISSIONS.DATA_RECONCILIATION_TABLE],
                        element: <DataReconciliationTableForm />,
                        hide: true,
                      },
                    ],
                  },

                  // Thống kê tình hình công nợ dự án
                  {
                    path: '/general-finance/project-debt-status-statistics-report',
                    titleKey: 'projectDebtStatusStatisticsReport',
                    permissions: [PERMISSIONS.PROJECT_DEBT_STATUS_STATISTICS_REPORT],
                    children: [
                      {
                        path: '/general-finance/project-debt-status-statistics-report',
                        permissions: [PERMISSIONS.PROJECT_DEBT_STATUS_STATISTICS_REPORT],
                        element: <ProjectDebtStatusStatisticsReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  //Chi phí đầu tư đề nghị quyết toán
                  {
                    path: '/general-finance/proposed-settlement-investment-cost',
                    titleKey: 'generalFinance.proposedSettlementInvestmentCost',
                    permissions: [PERMISSIONS.PROPOSED_SETTLEMENT_INVESTMENT_COST],
                    children: [
                      {
                        path: '/general-finance/proposed-settlement-investment-cost',
                        permissions: [PERMISSIONS.PROPOSED_SETTLEMENT_INVESTMENT_COST],
                        element: <ProposedSettlementInvestmentCostDataTable />,
                        hide: true,
                      },
                      {
                        path: '/general-finance/proposed-settlement-investment-cost/:id',
                        permissions: [PERMISSIONS.PROPOSED_SETTLEMENT_INVESTMENT_COST],
                        element: <ProposedSettlementInvestmentCostForm />,
                        hide: true,
                      },
                    ],
                  },
                ],
              },

              //Kế hoạch chất lượng
              {
                path: '/planning',
                titleKey: 'planning.name',
                permissions: [
                  PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT,
                  PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT,
                  PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE,
                  PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT,

                  PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT,

                  PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT,

                  PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT,
                  PERMISSIONS.SAVING_IN_BIDDING_REPORT,
                  PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT,
                  PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT,

                  PERMISSIONS.REPORT_SERIAL_MANAGEMENT,
                  PERMISSIONS.DIRECTIVE_IMPLEMENTATION_BY_USER_PLANNING,

                  PERMISSIONS.REPORT_TEMPLATE,

                  PERMISSIONS.DOCUMENT_FORM_ENTRY,

                  PERMISSIONS.TEMPLATE_STATISTICS_REPORT,

                  PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY,

                  PERMISSIONS.DIRECTIVE_CONTENT_PLANNING,
                  PERMISSIONS.TARGET_PLANNING,
                  PERMISSIONS.TASK_PLANNING,

                  PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING,
                  PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING,
                  PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING,
                  PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING,
                  PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING,
                ],
                icon: PlaneLandingIcon,
                children: [
                  // Báo cáo tiến độ thực hiện các dự án
                  {
                    path: '/planning/project-implement-reports',
                    titleKey: 'planning.projectImplementReports',
                    permissions: [
                      PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT,
                      PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT,
                      PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE,
                      PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT,
                    ],
                    children: [
                      // Thống kê DA
                      {
                        path: '/planning/project-implement-reports/project-deployment-status-report',
                        titleKey: 'projectDeploymentStatusReport',
                        permissions: [PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT],
                        children: [
                          {
                            path: '/planning/project-implement-reports/project-deployment-status-report',
                            permissions: [PERMISSIONS.PROJECT_DEPLOYMENT_STATUS_REPORT],
                            element: <ProjectDeploymentStatusReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Báo cáo tiến độ giải ngân KH vốn
                      {
                        path: '/planning/project-implement-reports/capital-plan-disbursement-progress-report-2',
                        titleKey: 'capitalPlanDisbursementProgressReport',
                        permissions: [PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT],
                        children: [
                          {
                            path: '/planning/project-implement-reports/capital-plan-disbursement-progress-report-2',
                            permissions: [PERMISSIONS.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT],
                            element: <CapitalPlanDisbursementProgressReportDataTable2 />,
                            hide: true,
                          },
                        ],
                      },
                      // Tiến độ giải ngân theo Nguồn ngân sách
                      {
                        path: '/planning/project-implement-reports/disbursement-progress-by-funding-source',
                        titleKey: 'disbursementProgressByFundingSource',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
                        children: [
                          {
                            path: '/planning/project-implement-reports/disbursement-progress-by-funding-source',
                            permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
                            element: (
                              <DisbursementProgressByFundingSourceDataTable
                                path={PATHS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
                                permission={PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
                              />
                            ),
                            hide: true,
                          },
                          {
                            path: '/planning/project-implement-reports/disbursement-progress-by-funding-source/:id',
                            permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
                            element: (
                              <DisbursementProgressByFundingSourceForm
                                path={PATHS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
                                permission={PERMISSIONS.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
                              />
                            ),
                            hide: true,
                          },
                        ],
                      },
                      //  Bảng tiến độ giải ngân theo phòng QLDA
                      {
                        path: '/planning/project-implement-reports/project-department-disbursement-progress-report',
                        titleKey: 'projectDepartmentDisbursementProgressReport',
                        permissions: [PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT],
                        children: [
                          {
                            path: '/planning/project-implement-reports/project-department-disbursement-progress-report',
                            permissions: [
                              PERMISSIONS.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT,
                            ],
                            element: <ProjectDepartmentDisbursementProgressReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                    ],
                  },
                  // Báo cáo tiến độ thực hiện BTGPMB
                  {
                    path: '/planning/land-acquisition-and-compensation-progress',
                    titleKey: 'landAcquisitionAndCompensationProgressReport',
                    permissions: [PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT],
                    children: [
                      {
                        path: '/planning/land-acquisition-and-compensation-progress',
                        permissions: [
                          PERMISSIONS.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT,
                        ],
                        element: <LandAcquisitionAndCompensationProgressReportDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  //Quản lý dự án tồn đọng
                  {
                    path: '/planning/backlog-project-management',
                    titleKey: 'backlogProjectManagement',
                    permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT],
                    children: [
                      {
                        path: '/planning/backlog-project-management',
                        permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT],
                        element: (
                          <BacklogProjectManagementDataTable
                            path={PATHS.BACKLOG_PROJECT_MANAGEMENT}
                            permission={PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/backlog-project-management/:id',
                        permissions: [PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT],
                        element: (
                          <BacklogProjectManagementForm
                            path={PATHS.BACKLOG_PROJECT_MANAGEMENT}
                            permission={PERMISSIONS.BACKLOG_PROJECT_MANAGEMENT}
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  // Báo cáo đấu thầu
                  {
                    path: '/planning/bidding-reports',
                    titleKey: 'planning.biddingReports',
                    permissions: [
                      PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT,
                      PERMISSIONS.SAVING_IN_BIDDING_REPORT,
                      PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT,
                      PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT,
                    ],
                    children: [
                      // Báo cáo Tổng hợp đấu thầu hằng năm
                      {
                        path: '/planning/bidding-reports/contractor-selection-result-summary-by-year-report',
                        titleKey: 'contractorSelectionResultByDepartmentReport',
                        permissions: [
                          PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT,
                        ],
                        children: [
                          {
                            path: '/planning/bidding-reports/contractor-selection-result-summary-by-year-report',
                            permissions: [
                              PERMISSIONS.CONTRACTOR_SELECTION_RESULT_SUMMARY_BY_YEAR_REPORT,
                            ],
                            element: <AnnualBiddingSummaryReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Báo cáo tiết kiệm trong đấu thầu
                      {
                        path: '/planning/bidding-reports/saving-in-bidding-report',
                        titleKey: 'savingInBiddingReport',
                        permissions: [PERMISSIONS.SAVING_IN_BIDDING_REPORT],
                        children: [
                          {
                            path: '/planning/bidding-reports/saving-in-bidding-report',
                            permissions: [PERMISSIONS.SAVING_IN_BIDDING_REPORT],
                            element: <SavingInBiddingReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Tỷ lệ tiết kiệm trong công tác LCNT
                      {
                        path: '/planning/bidding-reports/saving-rate-contractor-selection-plan-report',
                        titleKey: 'savingRateContractorSelectionPlanReport',
                        permissions: [PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT],
                        children: [
                          {
                            path: '/planning/bidding-reports/saving-rate-contractor-selection-plan-report',
                            permissions: [PERMISSIONS.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT],
                            element: <SavingRateContractorSelectionPlanReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                      // Danh sách nhà thầu tham gia thực hiện gói thầu
                      {
                        path: '/planning/bidding-reports/contractor-participation-tender-package-list-report',
                        titleKey: 'contractorParticipationTenderPackageListReport',
                        permissions: [
                          PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT,
                        ],
                        children: [
                          {
                            path: '/planning/bidding-reports/contractor-participation-tender-package-list-report',
                            permissions: [
                              PERMISSIONS.CONTRACTOR_PARTICIPATION_TENDER_PACKAGE_LIST_REPORT,
                            ],
                            element: <ContractorParticipationTenderPackageListReportDataTable />,
                            hide: true,
                          },
                        ],
                      },
                    ],
                  },
                  // Nội bộ P. KH-CL
                  {
                    path: '/planning/internal-planning',
                    titleKey: 'planning.internalPlanning',
                    permissions: [
                      PERMISSIONS.REPORT_SERIAL_MANAGEMENT,
                      PERMISSIONS.DIRECTIVE_IMPLEMENTATION_BY_USER_PLANNING,
                    ],
                    children: [
                      //Quản lý cho số báo cáo
                      {
                        path: '/planning/internal-planning/report-serial-management',
                        titleKey: 'reportSerialManagement',
                        permissions: [PERMISSIONS.REPORT_SERIAL_MANAGEMENT],
                        children: [
                          {
                            path: '/planning/internal-planning/report-serial-management',
                            titleKey: 'reportSerialManagement',
                            permissions: [PERMISSIONS.REPORT_SERIAL_MANAGEMENT],
                            element: <ReportSerialManagementDataTable />,
                            hide: true,
                          },
                          {
                            path: '/planning/internal-planning/report-serial-management/:id',
                            titleKey: 'reportSerialManagement',
                            permissions: [PERMISSIONS.REPORT_SERIAL_MANAGEMENT],
                            element: <ReportSerialManagementForm />,
                            hide: true,
                          },
                        ],
                      },
                      // Đánh giá chỉ tiêu theo VC, NLĐ (TC-TH)
                      {
                        path: '/planning/internal-planning/implementation-of-directives-by-user',
                        titleKey: 'report.directiveImplementationReportByUser',
                        permissions: [PERMISSIONS.DIRECTIVE_IMPLEMENTATION_BY_USER_PLANNING],
                        element: <RptReportOnImplementationOfDirectivesByUserTemplateDataTable />,
                      },
                    ],
                  },

                  //Thiết lập biểu mẫu báo cáo
                  {
                    path: '/planning/report-template',
                    titleKey: 'reportTemplate',
                    permissions: [PERMISSIONS.REPORT_TEMPLATE],
                    children: [
                      {
                        path: '/planning/report-template',
                        permissions: [PERMISSIONS.REPORT_TEMPLATE],
                        element: <ReportTemplateDataTable />,
                        hide: true,
                      },
                      {
                        path: '/planning/report-template/:id',
                        permissions: [PERMISSIONS.REPORT_TEMPLATE],
                        element: <ReportTemplateForm />,
                        hide: true,
                      },
                    ],
                  },
                  //Nhập dữ liệu theo biểu mẫu chung
                  {
                    path: '/planning/document-form-entry',
                    titleKey: 'documentFormEntry',
                    permissions: [PERMISSIONS.DOCUMENT_FORM_ENTRY],
                    children: [
                      {
                        path: '/planning/document-form-entry',
                        permissions: [PERMISSIONS.DOCUMENT_FORM_ENTRY],
                        element: <DocumentFormEntryDataTable />,
                        hide: true,
                      },
                      {
                        path: '/planning/document-form-entry/:id',
                        permissions: [PERMISSIONS.DOCUMENT_FORM_ENTRY],
                        element: <DocumentFormEntryForm />,
                        hide: true,
                      },
                    ],
                  },
                  //Thống kê theo biểu mẫu báo cáo
                  {
                    path: '/planning/template-statistics-report',
                    titleKey: 'templateStatisticsReport',
                    permissions: [PERMISSIONS.TEMPLATE_STATISTICS_REPORT],
                    children: [
                      {
                        path: '/planning/template-statistics-report',
                        permissions: [PERMISSIONS.TEMPLATE_STATISTICS_REPORT],
                        element: <TemplateStatisticReportDataTable />,
                        hide: true,
                      },
                      {
                        path: '/planning/template-statistics-report/:id',
                        permissions: [PERMISSIONS.TEMPLATE_STATISTICS_REPORT],
                        element: <TemplateStatisticsReport />,
                        hide: true,
                      },
                    ],
                  },

                  // Bảng tóm tắt Tiến độ giải ngân
                  {
                    path: '/planning/disbursement-progress-summary',
                    titleKey: 'disbursementProgressBySummary',
                    permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY],
                    children: [
                      {
                        path: '/planning/disbursement-progress-summary',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY],
                        element: (
                          <DisbursementProgressSummaryDataTable
                            path={PATHS.DISBURSEMENT_PROGRESS_SUMMARY}
                            permission={PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/disbursement-progress-summary/:id',
                        permissions: [PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY],
                        element: (
                          <DisbursementProgressSummaryForm
                            path={PATHS.DISBURSEMENT_PROGRESS_SUMMARY}
                            permission={PERMISSIONS.DISBURSEMENT_PROGRESS_SUMMARY}
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },

                  // Tách riêng từ quản lý công việc
                  // Nội dung chỉ đạo, chỉ tiêu, nhiệm vụ
                  {
                    path: '/planning/directives',
                    titleKey: 'qualityPlanDirectives',
                    permissions: [
                      PERMISSIONS.DIRECTIVE_CONTENT_PLANNING,
                      PERMISSIONS.TARGET_PLANNING,
                      PERMISSIONS.TASK_PLANNING,
                    ],
                    element: <Directives />,
                    children: [
                      {
                        path: '/planning/directives/content',
                        permissions: [PERMISSIONS.DIRECTIVE_CONTENT_PLANNING],
                        element: (
                          <DirectiveContentDataTable
                            permission={PERMISSIONS.DIRECTIVE_CONTENT_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/directives/content/:id',
                        permissions: [PERMISSIONS.DIRECTIVE_CONTENT_PLANNING],
                        element: (
                          <DirectiveContentForm
                            permission={PERMISSIONS.DIRECTIVE_CONTENT_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/directives/target',
                        permissions: [PERMISSIONS.TARGET_PLANNING],
                        element: <TargetDataTable permission={PERMISSIONS.TARGET_PLANNING} />,
                        hide: true,
                      },
                      {
                        path: '/planning/directives/target/:id',
                        permissions: [PERMISSIONS.TARGET_PLANNING],
                        element: <TargetForm permission={PERMISSIONS.TARGET_PLANNING} />,
                        hide: true,
                      },
                      {
                        path: '/planning/directives/task',
                        permissions: [PERMISSIONS.TASK_PLANNING],
                        element: <DirectiveTaskDataTable permission={PERMISSIONS.TASK_PLANNING} />,
                        hide: true,
                      },
                      {
                        path: '/planning/directives/task/:id',
                        permissions: [PERMISSIONS.TASK_PLANNING],
                        element: <DirectiveTaskForm permission={PERMISSIONS.TASK_PLANNING} />,
                        hide: true,
                      },
                    ],
                  },
                  //quản lý công việc
                  {
                    path: '/planning/work',
                    titleKey: 'workManagement.quantityPlanName',
                    permissions: [
                      PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING,
                      PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING,
                      PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING,
                      PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING,
                      PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING,
                    ],
                    element: <WorkManagement />,
                    children: [
                      {
                        path: '/planning/work/directive-content',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING],
                        element: (
                          <WorkManagementDirectiveContentDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/work/directive-content/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING],
                        element: (
                          <WorkManagementDirectiveContentForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-chỉ tiêu
                      {
                        path: '/planning/work/target',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING],
                        element: (
                          <WorkManagementTargetDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/work/target/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING],
                        element: (
                          <WorkManagementTargetForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_TARGET_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-nhiệm vu
                      {
                        path: '/planning/work/task',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementTaskDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/work/task/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementTaskForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_TASK_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-thiết kế dự toán gói thầu
                      {
                        path: '/planning/work/design-bid-estimation',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementDesignBidEstimationDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/work/design-bid-estimation/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementDesignBidEstimationForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-khác
                      {
                        path: '/planning/work/other',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementOtherDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/planning/work/other/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING], //sửa lại quyền
                        element: (
                          <WorkManagementOtherForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_OTHER_PLANNING}
                          />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  // Báo cáo tiến độ giải ngân KH vốn
                  {
                    path: '/planning/project-implement-reports/comprehensive-investment-report',
                    titleKey: 'comprehensiveInvestmentReport',
                    permissions: [PERMISSIONS.COMPREHENSIVE_INVESTMENT_REPORT],
                    children: [
                      {
                        path: '/planning/project-implement-reports/comprehensive-investment-report',
                        permissions: [PERMISSIONS.COMPREHENSIVE_INVESTMENT_REPORT],
                        element: <ComprehensiveInvestmentReportDataTable />,
                        hide: true,
                      },
                      {
                        path: '/planning/project-implement-reports/comprehensive-investment-report/:id',
                        permissions: [PERMISSIONS.COMPREHENSIVE_INVESTMENT_REPORT],
                        element: <ComprehensiveInvestmentReportForm />,
                        hide: true,
                      },
                    ],
                  },
                ],
              },

              //quản lý công việc
              {
                path: '/work-management',
                titleKey: 'workManagement.name',
                permissions: [
                  PERMISSIONS.BOARD_OF_DIRECTORS_WORK_SCHEDULE,

                  PERMISSIONS.DIRECTIVE_CONTENT,
                  PERMISSIONS.TARGET,
                  PERMISSIONS.TASK,

                  PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT,
                  PERMISSIONS.WORK_MANAGEMENT_TARGET,
                  PERMISSIONS.WORK_MANAGEMENT_TASK,
                  PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION,
                  PERMISSIONS.WORK_MANAGEMENT_OTHER,

                  PERMISSIONS.RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES,

                  PERMISSIONS.RPT_ANNUAL_TASK_LIST_STATISTICS,
                  PERMISSIONS.RPT_YEARLY_SUMMARY,
                  PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS,
                  PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS,

                  PERMISSIONS.DIRECTIVE_IMPLEMENTATION_BY_USER,

                  PERMISSIONS.CONTRACT_TASK_MANAGEMENT,

                  PERMISSIONS.DESIGN_TASK_MANAGEMENT,
                  PERMISSIONS.CONTRACTOR_SELECTION_PLAN_TASK_MANAGEMENT,

                  PERMISSIONS.DIRECTIVE_IMPLEMENTATION,
                ],
                icon: CalendarCheck2,
                children: [
                  //Lịch làm việc của giám đốc
                  {
                    path: '/work-management/board-of-directors-work-schedule',
                    titleKey: 'boardOfDirectorsWorkSchedule',
                    permissions: [PERMISSIONS.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
                    children: [
                      {
                        path: '/work-management/board-of-directors-work-schedule',
                        permissions: [PERMISSIONS.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
                        element: <BoardOfDirectorsWorkScheduleDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/board-of-directors-work-schedule/:id',
                        permissions: [PERMISSIONS.BOARD_OF_DIRECTORS_WORK_SCHEDULE],
                        element: <BoardOfDirectorsWorkScheduleForm />,
                        hide: true,
                      },
                    ],
                  },
                  // Nội dung chỉ đạo, chỉ tiêu, nhiệm vụ
                  {
                    path: '/work-management/directives',
                    titleKey: 'directives',
                    permissions: [
                      PERMISSIONS.DIRECTIVE_CONTENT,
                      PERMISSIONS.TARGET,
                      PERMISSIONS.TASK,
                    ],
                    element: <Directives />,
                    children: [
                      {
                        path: '/work-management/directives/content',
                        permissions: [PERMISSIONS.DIRECTIVE_CONTENT],
                        element: (
                          <DirectiveContentDataTable permission={PERMISSIONS.DIRECTIVE_CONTENT} />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/directives/content/:id',
                        permissions: [PERMISSIONS.DIRECTIVE_CONTENT],
                        element: (
                          <DirectiveContentForm permission={PERMISSIONS.DIRECTIVE_CONTENT} />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/directives/target',
                        permissions: [PERMISSIONS.TARGET],
                        element: <TargetDataTable permission={PERMISSIONS.TARGET} />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives/target/:id',
                        permissions: [PERMISSIONS.TARGET],
                        element: <TargetForm permission={PERMISSIONS.TARGET} />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives/task',
                        permissions: [PERMISSIONS.TASK],
                        element: <DirectiveTaskDataTable permission={PERMISSIONS.TASK} />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives/task/:id',
                        permissions: [PERMISSIONS.TASK],
                        element: <DirectiveTaskForm permission={PERMISSIONS.TASK} />,
                        hide: true,
                      },
                    ],
                  },
                  //quản lý công việc
                  {
                    path: '/work-management/work',
                    titleKey: 'workManagement.name',
                    permissions: [
                      PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT,
                      PERMISSIONS.WORK_MANAGEMENT_TARGET,
                      PERMISSIONS.WORK_MANAGEMENT_TASK,
                      PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION,
                      PERMISSIONS.WORK_MANAGEMENT_OTHER,
                    ],
                    element: <WorkManagement />,
                    children: [
                      {
                        path: '/work-management/work/directive-content',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT],
                        element: (
                          <WorkManagementDirectiveContentDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/work/directive-content/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT],
                        element: (
                          <WorkManagementDirectiveContentForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_DIRECTIVE_CONTENT}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-chỉ tiêu
                      {
                        path: '/work-management/work/target',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TARGET],
                        element: (
                          <WorkManagementTargetDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_TARGET}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/work/target/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TARGET],
                        element: (
                          <WorkManagementTargetForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_TARGET}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-nhiệm vu
                      {
                        path: '/work-management/work/task',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TASK], //sửa lại quyền
                        element: (
                          <WorkManagementTaskDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_TASK}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/work/task/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_TASK], //sửa lại quyền
                        element: (
                          <WorkManagementTaskForm permission={PERMISSIONS.WORK_MANAGEMENT_TASK} />
                        ),
                        hide: true,
                      },
                      //QLCV-thiết kế dự toán gói thầu
                      {
                        path: '/work-management/work/design-bid-estimation',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION], //sửa lại quyền
                        element: (
                          <WorkManagementDesignBidEstimationDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/work/design-bid-estimation/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION], //sửa lại quyền
                        element: (
                          <WorkManagementDesignBidEstimationForm
                            permission={PERMISSIONS.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION}
                          />
                        ),
                        hide: true,
                      },
                      //QLCV-khác
                      {
                        path: '/work-management/work/other',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_OTHER], //sửa lại quyền
                        element: (
                          <WorkManagementOtherDataTable
                            permission={PERMISSIONS.WORK_MANAGEMENT_OTHER}
                          />
                        ),
                        hide: true,
                      },
                      {
                        path: '/work-management/work/other/:id',
                        permissions: [PERMISSIONS.WORK_MANAGEMENT_OTHER], //sửa lại quyền
                        element: (
                          <WorkManagementOtherForm permission={PERMISSIONS.WORK_MANAGEMENT_OTHER} />
                        ),
                        hide: true,
                      },
                    ],
                  },
                  //Thống kê kết quả thực hiện (TC-TH)
                  {
                    path: '/work-management/implementation-of-directives-report',
                    titleKey: 'report.rptReportOnImplementationOfDirectives',
                    permissions: [PERMISSIONS.RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES],
                    element: <RptReportOnImplementationOfDirectivesTemplateDataTable />,
                  },
                  //Báo cáo chỉ tiêu, nhiệm vụ - nội dung chỉ đạo (TC-TH)
                  {
                    path: '/work-management/directives-report',
                    titleKey: 'directivesReport',
                    permissions: [
                      PERMISSIONS.RPT_ANNUAL_TASK_LIST_STATISTICS,
                      PERMISSIONS.RPT_YEARLY_SUMMARY,
                      PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS,
                      PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS,
                    ],
                    element: <TargetsAndTasksDirectiveContent />,
                    children: [
                      {
                        path: '/work-management/directives-report/annual-task-list-statistics',
                        permissions: [PERMISSIONS.RPT_ANNUAL_TASK_LIST_STATISTICS],
                        element: <RptAnnualTaskListStatisticsTemplateDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives-report/yearly-summary',
                        permissions: [PERMISSIONS.RPT_YEARLY_SUMMARY],
                        element: <RptYearlySummaryTemplateDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives-report/summary-targets-tasks-first-six-months',
                        permissions: [PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS],
                        element: <RptSummaryTargetsTasksFirst6MonthsTemplateDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directives-report/summary-targets-tasks-last-six-months',
                        permissions: [PERMISSIONS.RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS],
                        element: <RptSummaryTargetsTasksLast6MonthsTemplateDataTable />,
                        hide: true,
                      },
                    ],
                  },
                  //  Đánh giá chỉ tiêu theo VC, NLĐ (TC-TH)
                  {
                    path: '/work-management/implementation-of-directives-by-user',
                    titleKey: 'report.directiveImplementationReportByUserFinance',
                    permissions: [PERMISSIONS.DIRECTIVE_IMPLEMENTATION_BY_USER],
                    element: (
                      <RptReportOnImplementationOfDirectivesByUserTemplateDataTable
                        isForFinance={true}
                      />
                    ),
                  },
                  //quản lý công việc hợp đồng
                  {
                    path: '/work-management/contract-task-management',
                    titleKey: 'projectManagement.contractTaskManagement',
                    permissions: [PERMISSIONS.CONTRACT_TASK_MANAGEMENT],
                    children: [
                      {
                        path: '/work-management/contract-task-management',
                        permissions: [PERMISSIONS.CONTRACT_TASK_MANAGEMENT],
                        element: <ContractTaskManagementDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/contract-task-management/:id',
                        permissions: [PERMISSIONS.CONTRACT_TASK_MANAGEMENT],
                        element: <ContractTaskManagementForm />,
                        hide: true,
                      },
                    ],
                  },
                  //quản lý công việc thiết kế
                  {
                    path: '/work-management/design-task-management',
                    titleKey: 'projectManagement.designTaskManagement',
                    permissions: [PERMISSIONS.DESIGN_TASK_MANAGEMENT],
                    children: [
                      {
                        path: '/work-management/design-task-management',
                        permissions: [PERMISSIONS.DESIGN_TASK_MANAGEMENT],
                        element: <DesignTaskManagementDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/design-task-management/:id',
                        permissions: [PERMISSIONS.DESIGN_TASK_MANAGEMENT],
                        element: <DesignTaskManagementForm />,
                        hide: true,
                      },
                    ],
                  },
                  //quản lý công việc KHLCNT
                  {
                    path: '/work-management/contractor-selection-plan-task-management',
                    titleKey: 'projectManagement.contractorSelectionPlanTaskManagement',
                    permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN_TASK_MANAGEMENT],
                    children: [
                      {
                        path: '/work-management/contractor-selection-plan-task-management',
                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN_TASK_MANAGEMENT],
                        element: <ContractorSelectionPlanTaskManagementDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/contractor-selection-plan-task-management/:id',
                        permissions: [PERMISSIONS.CONTRACTOR_SELECTION_PLAN_TASK_MANAGEMENT],
                        element: <ContractorSelectionPlanTaskManagementForm />,
                        hide: true,
                      },
                    ],
                  },

                  // Báo cáo kết quả thực hiện chỉ đạo
                  {
                    path: '/work-management/directive-implementation-report',
                    titleKey: 'report.directiveImplementationReport',
                    permissions: [PERMISSIONS.DIRECTIVE_IMPLEMENTATION],
                    children: [
                      {
                        path: '/work-management/directive-implementation-report',
                        permissions: [PERMISSIONS.DIRECTIVE_IMPLEMENTATION],
                        element: <DirectiveImplementationDataTable />,
                        hide: true,
                      },
                      {
                        path: '/work-management/directive-implementation-report/:id',
                        permissions: [PERMISSIONS.DIRECTIVE_IMPLEMENTATION],
                        element: <DirectiveImplementationForm />,
                        hide: true,
                      },
                    ],
                  },
                ],
              },

              /// nhân sự
              {
                path: '/hr',
                titleKey: 'hr.name',
                permissions: [
                  PERMISSIONS.EMPLOYEE,
                  PERMISSIONS.OVERTIME_REGISTRATION,
                  PERMISSIONS.BORROW_DOCUMENT,
                  PERMISSIONS.LEAVE,
                  PERMISSIONS.TRAINING_MANAGEMENT,
                  PERMISSIONS.TRAINING_TRACKING,
                  PERMISSIONS.OVERTIME_ATTENDANCE_TRACKING,
                  PERMISSIONS.EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT,
                ],
                icon: Building,
                children: [
                  {
                    path: '/hr/employee',
                    titleKey: 'hr.employee',
                    permissions: [PERMISSIONS.EMPLOYEE],
                    children: [
                      {
                        path: '/hr/employee',
                        permissions: [PERMISSIONS.EMPLOYEE],
                        element: <EmployeeDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/employee/:id',
                        permissions: [PERMISSIONS.EMPLOYEE],
                        element: <EmployeeForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    //phiếu đăng ký làm thêm
                    path: '/hr/overtime-registration',
                    titleKey: 'hr.overtimeRegistration',
                    permissions: [PERMISSIONS.OVERTIME_REGISTRATION],
                    children: [
                      {
                        path: '/hr/overtime-registration',
                        permissions: [PERMISSIONS.OVERTIME_REGISTRATION],
                        element: <OvertimeRegistrationDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/overtime-registration/:id',
                        permissions: [PERMISSIONS.OVERTIME_REGISTRATION],
                        element: <OvertimeRegistrationForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    // Theo dõi cho mượn hồ sơ
                    path: '/hr/borrow-document',
                    titleKey: 'hr.borrowDocument',
                    permissions: [PERMISSIONS.BORROW_DOCUMENT],
                    children: [
                      {
                        path: '/hr/borrow-document',
                        permissions: [PERMISSIONS.BORROW_DOCUMENT],
                        element: <BorrowDocumentDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/borrow-document/:id',
                        permissions: [PERMISSIONS.BORROW_DOCUMENT],
                        element: <BorrowDocumentForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    // Nghỉ phép
                    path: '/hr/leave',
                    titleKey: 'hr.leave',
                    permissions: [PERMISSIONS.LEAVE],
                    children: [
                      {
                        path: '/hr/leave',
                        permissions: [PERMISSIONS.LEAVE],
                        element: <LeaveDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/leave/:id',
                        permissions: [PERMISSIONS.LEAVE],
                        element: <LeaveForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    // Theo dõi đào tạo
                    path: '/hr/training-management',
                    titleKey: 'hr.trainingManagement',
                    permissions: [PERMISSIONS.TRAINING_MANAGEMENT],
                    children: [
                      {
                        path: '/hr/training-management',
                        permissions: [PERMISSIONS.TRAINING_MANAGEMENT],
                        element: <TrainingManagementDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/training-management/:id',
                        permissions: [PERMISSIONS.TRAINING_MANAGEMENT],
                        element: <TrainingManagementForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    // Bồi dưỡng năm
                    path: '/hr/training-tracking',
                    titleKey: 'hr.trainingTracking',
                    permissions: [PERMISSIONS.TRAINING_TRACKING],
                    children: [
                      {
                        path: '/hr/training-tracking',
                        permissions: [PERMISSIONS.TRAINING_TRACKING],
                        element: <TrainingTrackingDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/training-tracking/:id',
                        permissions: [PERMISSIONS.TRAINING_TRACKING],
                        element: <TrainingTrackingForm />,
                        hide: true,
                      },
                    ],
                  },
                  {
                    // Bảng chấm công làm thêm giờ
                    path: '/hr/overtime-attendance-tracking',
                    titleKey: 'hr.overtimeAttendanceTracking',
                    permissions: [PERMISSIONS.OVERTIME_ATTENDANCE_TRACKING],
                    children: [
                      {
                        path: '/hr/overtime-attendance-tracking',
                        permissions: [PERMISSIONS.OVERTIME_ATTENDANCE_TRACKING],
                        element: <OvertimeAttendanceTrackingDataTable />,
                        hide: true,
                      },
                      {
                        path: '/hr/overtime-attendance-tracking/:id',
                        permissions: [PERMISSIONS.OVERTIME_ATTENDANCE_TRACKING],
                        element: <OvertimeAttendanceTrackingForm />,
                        hide: true,
                      },
                    ],
                  },
                  //Thống kê kết quả đánh giá
                  {
                    // path: '/work-management/employee-annual-evaluation-result-report',
                    path: '/hr/employee-annual-evaluation-result-report',
                    titleKey: 'report.employeeAnnualEvaluationResultReport',
                    permissions: [PERMISSIONS.EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT],
                    element: <EmployeeAnnualEvaluationResultReportDataTable />,
                  },
                ],
              },

              //dữ liệu cơ sở
              {
                path: '/base',
                titleKey: 'base.name',
                icon: Settings2,
                permissions: [
                  PERMISSIONS.COST_ITEM_TYPE,
                  PERMISSIONS.COST_ITEM,
                  PERMISSIONS.CONSTRUCTION_ITEM,
                  PERMISSIONS.CONSTRUCTION_TASK,
                  PERMISSIONS.PROJECT_GROUP,
                  PERMISSIONS.PROJECT_OWNER,
                  PERMISSIONS.AGENCY,
                  PERMISSIONS.DEPLOYMENT_PHASE,
                  PERMISSIONS.PROJECT_MANAGEMENT_TYPE,
                  PERMISSIONS.PROJECT_STATUS,
                  PERMISSIONS.INVESTMENT_FORM,
                  PERMISSIONS.INVESTMENT_TYPE,
                  PERMISSIONS.CONSTRUCTION_TYPE,
                  PERMISSIONS.DISTRICT,
                  PERMISSIONS.WARD,
                  PERMISSIONS.DOCUMENT_GROUP,
                  PERMISSIONS.DOCUMENT_TYPE,
                  PERMISSIONS.FILE_TYPE,

                  PERMISSIONS.CONTRACTOR_TYPE,
                  PERMISSIONS.CONTRACTOR,
                  PERMISSIONS.TENDER_TYPE,
                  PERMISSIONS.BIDDING_SECTOR,
                  PERMISSIONS.BIDDING_METHOD,
                  PERMISSIONS.CONTRACT_TYPE,
                  PERMISSIONS.CONTRACT_APPENDIX_TYPE,
                  PERMISSIONS.DOCUMENT_DECISION,
                  PERMISSIONS.BANK,

                  PERMISSIONS.WORK_POSITION,
                  PERMISSIONS.EVALUATION_RESULT,
                  PERMISSIONS.EXPERTISE,
                  PERMISSIONS.MAJOR,
                  PERMISSIONS.POLITICS,
                  PERMISSIONS.CAREER_TRAINING,
                  PERMISSIONS.IT_COURSE,
                  PERMISSIONS.FOREIGN_LANGUAGE,
                  PERMISSIONS.GENDER_TYPE,
                  PERMISSIONS.STATE_MANAGEMENT,
                  PERMISSIONS.TRAINING_INSTITUTION,
                  PERMISSIONS.EMPLOYEE_TYPE,

                  PERMISSIONS.BUDGET_SOURCE_CODE,
                  PERMISSIONS.SECTOR_CODE,
                  PERMISSIONS.FUNDING_PROGRAM_CODE,
                  PERMISSIONS.TYPE_CODE,
                  PERMISSIONS.BUDGET_ITEM_CODE,
                  PERMISSIONS.BUDGET_FUND,
                  PERMISSIONS.INVENTORY_ITEM_TYPE,
                  PERMISSIONS.CORRESPONDENCE_TYPE,
                  PERMISSIONS.INVENTORY_ITEM,
                  PERMISSIONS.ASSET_TYPE,
                  PERMISSIONS.ASSET,
                  PERMISSIONS.UNIT,
                ],
                children: [
                  {
                    path: '/base/project',
                    titleKey: 'base.project.name',
                    permissions: [
                      PERMISSIONS.COST_ITEM_TYPE,
                      PERMISSIONS.COST_ITEM,
                      PERMISSIONS.CONSTRUCTION_ITEM,
                      PERMISSIONS.CONSTRUCTION_TASK,
                      PERMISSIONS.PROJECT_GROUP,
                      PERMISSIONS.PROJECT_OWNER,
                      PERMISSIONS.AGENCY,
                      PERMISSIONS.DEPLOYMENT_PHASE,
                      PERMISSIONS.PROJECT_MANAGEMENT_TYPE,
                      PERMISSIONS.PROJECT_STATUS,
                      PERMISSIONS.INVESTMENT_FORM,
                      PERMISSIONS.INVESTMENT_TYPE,
                      PERMISSIONS.CONSTRUCTION_TYPE,
                      PERMISSIONS.DISTRICT,
                      PERMISSIONS.WARD,
                      PERMISSIONS.DOCUMENT_GROUP,
                      PERMISSIONS.DOCUMENT_TYPE,
                      PERMISSIONS.FILE_TYPE,
                    ],
                    children: [
                      {
                        path: '/base/project/cost-item-type',
                        titleKey: 'base.costItemType.name',
                        permissions: [PERMISSIONS.COST_ITEM_TYPE],
                        element: <CostItemTypePage />,
                      },
                      {
                        path: '/base/project/cost-item',
                        titleKey: 'base.costItem.name',
                        permissions: [PERMISSIONS.COST_ITEM],
                        element: <CostItemPage />,
                      },
                      {
                        path: '/base/project/construction-item',
                        titleKey: 'base.constructionItem.name',
                        permissions: [PERMISSIONS.CONSTRUCTION_ITEM],
                        element: <ConstructionItemPage />,
                      },
                      {
                        path: '/base/project/construction-task',
                        titleKey: 'base.constructionTask.name',
                        permissions: [PERMISSIONS.CONSTRUCTION_TASK],
                        element: <ConstructionTaskPage />,
                      },
                      {
                        path: '/base/project/project-group',
                        titleKey: 'base.projectGroup.name',
                        permissions: [PERMISSIONS.PROJECT_GROUP],
                        element: <ProjectGroupPage />,
                      },
                      {
                        path: '/base/project/project-owner',
                        titleKey: 'base.projectOwner.name',
                        permissions: [PERMISSIONS.PROJECT_OWNER],
                        element: <ProjectOwnerPage />,
                      },
                      {
                        path: '/base/project/agency',
                        titleKey: 'base.agency.name',
                        permissions: [PERMISSIONS.AGENCY],
                        element: <AgencyPage />,
                      },
                      {
                        path: '/base/project/deployment-phase',
                        titleKey: 'base.deploymentPhase.name',
                        permissions: [PERMISSIONS.DEPLOYMENT_PHASE],
                        element: <DeploymentPhasePage />,
                      },
                      {
                        path: '/base/project/project-management-type',
                        titleKey: 'base.projectManagementType.name',
                        permissions: [PERMISSIONS.PROJECT_MANAGEMENT_TYPE],
                        element: <ProjectManagementTypePage />,
                      },
                      {
                        path: '/base/project/project-status',
                        titleKey: 'base.projectStatus.name',
                        permissions: [PERMISSIONS.PROJECT_STATUS],
                        element: <ProjectStatusPage />,
                      }, // Hình thức đầu tư
                      {
                        path: '/base/project/investment-form',
                        titleKey: 'investmentForm',
                        permissions: [PERMISSIONS.INVESTMENT_FORM],
                        element: <InvestmentFormDataTable />,
                      },
                      // Loại đầu tư
                      {
                        path: '/base/project/investment-type',
                        titleKey: 'investmentType',
                        permissions: [PERMISSIONS.INVESTMENT_TYPE],
                        element: <InvestmentTypeDataTable />,
                      },
                      // Loại công trình
                      {
                        path: '/base/project/construction-type',
                        titleKey: 'constructionType',
                        permissions: [PERMISSIONS.CONSTRUCTION_TYPE],
                        element: <ConstructionTypeDataTable />,
                      },
                      {
                        path: '/base/project/district',
                        titleKey: 'base.district.name',
                        permissions: [PERMISSIONS.DISTRICT],
                        element: <DistrictPage />,
                      },
                      {
                        path: '/base/project/ward',
                        titleKey: 'base.ward.name',
                        permissions: [PERMISSIONS.WARD],
                        element: <WardPage />,
                      },
                      {
                        path: '/base/project/document-group',
                        titleKey: 'base.documentGroup.name',
                        permissions: [PERMISSIONS.DOCUMENT_GROUP],
                        element: <DocumentGroupPage />,
                      },
                      {
                        path: '/base/project/document-type',
                        titleKey: 'base.documentType.name',
                        permissions: [PERMISSIONS.DOCUMENT_TYPE],
                        element: <DocumentTypePage />,
                      },
                      {
                        path: '/base/project/file-type',
                        titleKey: 'base.fileType.name',
                        permissions: [PERMISSIONS.FILE_TYPE],
                        element: <FileTypePage />,
                      },
                    ],
                  },
                  {
                    path: '/base/contractor',
                    titleKey: 'base.contractor.name',
                    permissions: [
                      PERMISSIONS.CONTRACTOR_TYPE,
                      PERMISSIONS.CONTRACTOR,
                      PERMISSIONS.TENDER_TYPE,
                      PERMISSIONS.BIDDING_SECTOR,
                      PERMISSIONS.BIDDING_METHOD,
                      PERMISSIONS.CONTRACT_TYPE,
                      PERMISSIONS.CONTRACT_APPENDIX_TYPE,
                      PERMISSIONS.DOCUMENT_DECISION,
                      PERMISSIONS.BANK,
                    ],
                    children: [
                      {
                        path: '/base/contractor/contractor-type',
                        titleKey: 'base.contractor.type',
                        permissions: [PERMISSIONS.CONTRACTOR_TYPE],
                        element: <ContractorTypePage />,
                      },
                      {
                        path: '/base/contractor/list',
                        titleKey: 'base.contractor.list',
                        permissions: [PERMISSIONS.CONTRACTOR],
                        element: <ContractorPage />,
                      },
                      {
                        path: '/base/contractor/tender-type',
                        titleKey: 'base.tenderType.name',
                        permissions: [PERMISSIONS.TENDER_TYPE],
                        element: <TenderTypePage />,
                      },
                      {
                        path: '/base/contractor/bidding-sector',
                        titleKey: 'base.biddingSector.name',
                        permissions: [PERMISSIONS.BIDDING_SECTOR],
                        element: <BiddingSectorPage />,
                      },
                      {
                        path: '/base/contractor/bidding-method',
                        titleKey: 'base.biddingMethod.name',
                        permissions: [PERMISSIONS.BIDDING_METHOD],
                        element: <BiddingMethodPage />,
                      },
                      {
                        path: '/base/contractor/contract-type',
                        titleKey: 'base.contractType.name',
                        permissions: [PERMISSIONS.CONTRACT_TYPE],
                        element: <ContractTypePage />,
                      },
                      {
                        path: '/base/contractor/contract-appendix-type',
                        titleKey: 'base.contractAppendixType.name',
                        permissions: [PERMISSIONS.CONTRACT_APPENDIX_TYPE],
                        element: <ContractAppendixTypePage />,
                      },
                      {
                        path: '/base/contractor/document-decision',
                        titleKey: 'documentDecision',
                        permissions: [PERMISSIONS.DOCUMENT_DECISION],
                        element: <DocumentDecisionDataTable />,
                      },
                      {
                        path: '/base/contractor/document-decision/:id',
                        permissions: [PERMISSIONS.DOCUMENT_DECISION],
                        element: <DocumentDecisionForm />,
                        hide: true,
                      },
                      {
                        path: '/base/contractor/bank',
                        titleKey: 'base.bank.name',
                        permissions: [PERMISSIONS.BANK],
                        element: <BankDataTable />,
                      },
                    ],
                  },
                  {
                    path: '/base/hr',
                    titleKey: 'base.hr.name',
                    permissions: [
                      PERMISSIONS.WORK_POSITION,
                      PERMISSIONS.EVALUATION_RESULT,
                      PERMISSIONS.EXPERTISE,
                      PERMISSIONS.MAJOR,
                      PERMISSIONS.POLITICS,
                      PERMISSIONS.CAREER_TRAINING,
                      PERMISSIONS.IT_COURSE,
                      PERMISSIONS.FOREIGN_LANGUAGE,
                      PERMISSIONS.GENDER_TYPE,
                      PERMISSIONS.STATE_MANAGEMENT,
                      PERMISSIONS.TRAINING_INSTITUTION,
                      PERMISSIONS.EMPLOYEE_TYPE,
                    ],
                    children: [
                      {
                        path: '/base/hr/work-position',
                        titleKey: 'base.workPosition.name',
                        permissions: [PERMISSIONS.WORK_POSITION],
                        element: <WorkPositionPage />,
                      },
                      {
                        path: '/base/hr/evaluation-result',
                        titleKey: 'base.evaluationResult.name',
                        permissions: [PERMISSIONS.EVALUATION_RESULT],
                        element: <EvaluationResultPage />,
                      },
                      {
                        path: '/base/hr/expertise',
                        titleKey: 'base.expertise.name',
                        permissions: [PERMISSIONS.EXPERTISE],
                        element: <ExpertisePage />,
                      },
                      {
                        path: '/base/hr/major',
                        titleKey: 'base.major.name',
                        permissions: [PERMISSIONS.MAJOR],
                        element: <MajorPage />,
                      },
                      {
                        path: '/base/hr/politics',
                        titleKey: 'base.politics.name',
                        permissions: [PERMISSIONS.POLITICS],
                        element: <PoliticsPage />,
                      },

                      {
                        path: '/base/hr/career-training',
                        titleKey: 'base.careerTraining.name',
                        permissions: [PERMISSIONS.CAREER_TRAINING],
                        element: <CareerTrainingPage />,
                      },
                      {
                        path: '/base/hr/it-course',
                        titleKey: 'base.itCourse.name',
                        permissions: [PERMISSIONS.IT_COURSE],
                        element: <ItCoursePage />,
                      },
                      {
                        path: '/base/hr/foreign-language',
                        titleKey: 'base.foreignLanguage.name',
                        permissions: [PERMISSIONS.FOREIGN_LANGUAGE],
                        element: <ForeignLanguagePage />,
                      },

                      {
                        path: '/base/hr/gender-type',
                        titleKey: 'base.genderType.name',
                        permissions: [PERMISSIONS.GENDER_TYPE],
                        element: <GenderTypePage />,
                      },
                      {
                        path: '/base/hr/state-management',
                        titleKey: 'base.stateManagement.name',
                        permissions: [PERMISSIONS.STATE_MANAGEMENT],
                        element: <StateManagementPage />,
                      },
                      {
                        path: '/base/hr/training-institution',
                        titleKey: 'base.trainingInstitution.name',
                        permissions: [PERMISSIONS.TRAINING_INSTITUTION],
                        element: <TrainingInstitutionPage />,
                      },
                      {
                        path: '/base/hr/employee-type',
                        titleKey: 'base.employeeType.name',
                        permissions: [PERMISSIONS.EMPLOYEE_TYPE],
                        element: <EmployeeTypePage />,
                      },
                    ],
                  },

                  {
                    path: '/base/finance',
                    titleKey: 'base.finance.name',
                    permissions: [
                      PERMISSIONS.BUDGET_SOURCE_CODE,
                      PERMISSIONS.SECTOR_CODE,
                      PERMISSIONS.FUNDING_PROGRAM_CODE,
                      PERMISSIONS.TYPE_CODE,
                      PERMISSIONS.BUDGET_ITEM_CODE,
                      PERMISSIONS.BUDGET_FUND,
                      PERMISSIONS.INVENTORY_ITEM_TYPE,
                      PERMISSIONS.CORRESPONDENCE_TYPE,
                      PERMISSIONS.INVENTORY_ITEM,
                      PERMISSIONS.ASSET_TYPE,
                      PERMISSIONS.ASSET,
                      PERMISSIONS.UNIT,
                    ],
                    children: [
                      {
                        path: '/base/finance/budget-source-code',
                        titleKey: 'base.budgetSourceCode.name',
                        permissions: [PERMISSIONS.BUDGET_SOURCE_CODE],
                        element: <BudgetSourceCodeDataTable />,
                      },
                      // Mã NDKT
                      {
                        path: '/base/finance/sector-code',
                        titleKey: 'base.sectorCode.name',
                        permissions: [PERMISSIONS.SECTOR_CODE],
                        element: <SectorCodeDataTable />,
                      },
                      // mã chương
                      {
                        path: '/base/finance/funding-program-code',
                        titleKey: 'base.fundingProgramCode.name',
                        permissions: [PERMISSIONS.FUNDING_PROGRAM_CODE],
                        element: <FundingProgramCodeDataTable />,
                      },
                      {
                        path: '/base/finance/type-code',
                        titleKey: 'base.typeCode.name',
                        permissions: [PERMISSIONS.TYPE_CODE],
                        element: <TypeCodePage />,
                      },
                      {
                        path: '/base/finance/budget-item-code',
                        titleKey: 'base.budgetItemCode.name',
                        permissions: [PERMISSIONS.BUDGET_ITEM_CODE],
                        element: <BudgetItemCodePage />,
                      },
                      {
                        path: '/base/finance/budget-fund',
                        titleKey: 'base.budgetFund.name',
                        permissions: [PERMISSIONS.BUDGET_FUND],
                        element: <BudgetFundPage />,
                      },
                      {
                        path: '/base/finance/inventory-item-type',
                        titleKey: 'base.inventoryItemType.name',
                        permissions: [PERMISSIONS.INVENTORY_ITEM_TYPE],
                        element: <InventoryItemTypePage />,
                      },
                      {
                        path: '/base/finance/correspondence-type',
                        titleKey: 'base.correspondenceType.name',
                        permissions: [PERMISSIONS.CORRESPONDENCE_TYPE],
                        element: <CorrespondenceTypePage />,
                      },
                      {
                        path: '/base/finance/inventory-item',
                        titleKey: 'base.inventoryItem.name',
                        permissions: [PERMISSIONS.INVENTORY_ITEM],
                        element: <InventoryItemPage />,
                      },
                      {
                        path: '/base/finance/asset-type',
                        titleKey: 'base.assetType.name',
                        permissions: [PERMISSIONS.ASSET_TYPE],
                        element: <AssetTypePage />,
                      },
                      {
                        path: '/base/finance/asset',
                        titleKey: 'base.asset.name',
                        permissions: [PERMISSIONS.ASSET],
                        element: <AssetPage />,
                      },
                      {
                        path: '/base/finance/unit',
                        titleKey: 'base.unit.name',
                        permissions: [PERMISSIONS.UNIT],
                        element: <UnitPage />,
                      },
                    ],
                  },
                ],
              },
              //quản trị hệ thống
              {
                path: '/system',
                titleKey: 'system.name',
                icon: SettingsIcon,
                permissions: [
                  PERMISSIONS.COMPANY,
                  PERMISSIONS.PERMISSION_GROUP,
                  PERMISSIONS.USER,
                  PERMISSIONS.BOARD_OF_DIRECTOR,
                  PERMISSIONS.DEPARTMENT,
                  PERMISSIONS.POSITION,
                  PERMISSIONS.STATUS,
                  PERMISSIONS.FORM_DOCUMENT_MANAGER,
                  PERMISSIONS.APPROVAL_PROCESS,
                  PERMISSIONS.SETUP_ANNUAL_HOLIDAY,
                  PERMISSIONS.PROJECT_SCHEDULE_SETUP,
                  PERMISSIONS.HISTORY_ACTION,
                ],
                children: [
                  {
                    titleKey: 'system.company',
                    path: '/system/company',
                    permissions: [PERMISSIONS.COMPANY],
                    element: <Company />,
                  },
                  {
                    path: '/system/permission-group',
                    titleKey: 'system.users.permissionGroups',
                    permissions: [PERMISSIONS.PERMISSION_GROUP],
                    element: <PermissionGroupsPage />,
                  },
                  {
                    titleKey: 'system.users.list',
                    path: '/system/users',
                    permissions: [PERMISSIONS.USER],
                    element: <UsersPage />,
                  },
                  {
                    path: '/system/board-of-director',
                    titleKey: 'base.boardOfDirector.name',
                    permissions: [PERMISSIONS.BOARD_OF_DIRECTOR],
                    element: <BoardOfDirectorPage />,
                  },
                  {
                    path: '/system/department',
                    titleKey: 'base.department.name',
                    permissions: [PERMISSIONS.DEPARTMENT],
                    element: <DepartmentPage />,
                  },
                  {
                    path: '/system/position',
                    titleKey: 'base.position.name',
                    permissions: [PERMISSIONS.POSITION],
                    element: <PositionPage />,
                  },
                  {
                    path: '/system/status',
                    titleKey: 'base.status.name',
                    permissions: [PERMISSIONS.STATUS],
                    element: <StatusPage />,
                  },
                  //quản lý biểu mẫu
                  {
                    path: '/system/form-document-manager',
                    titleKey: 'formDocumentManager',
                    permissions: [PERMISSIONS.FORM_DOCUMENT_MANAGER],
                    element: <FormDocumentManagerDataTable />,
                  },
                  {
                    path: '/system/form-document-manager/:id',
                    titleKey: 'formDocumentManager',
                    permissions: [PERMISSIONS.FORM_DOCUMENT_MANAGER],
                    element: <FormDocumentManagerForm />,
                    hide: true,
                  },
                  {
                    path: '/system/approval-process',
                    titleKey: 'base.approvalProcess',
                    permissions: [PERMISSIONS.APPROVAL_PROCESS],
                    children: [
                      {
                        path: '/system/approval-process',
                        permissions: [PERMISSIONS.APPROVAL_PROCESS],
                        element: <ApprovalProcessDataTable />,
                        hide: true,
                      },
                      {
                        path: '/system/approval-process/:id',
                        permissions: [PERMISSIONS.APPROVAL_PROCESS],
                        element: <ApprovalProcessForm />,
                        hide: true,
                      },
                    ],
                  }, // Thiết lập ngày nghỉ trong năm
                  {
                    path: '/system/setup-annual-holiday',
                    titleKey: 'setupAnnualHoliday',
                    permissions: [PERMISSIONS.SETUP_ANNUAL_HOLIDAY],
                    children: [
                      {
                        path: '/system/setup-annual-holiday',
                        permissions: [PERMISSIONS.SETUP_ANNUAL_HOLIDAY],
                        element: <SetupAnnualHolidayDataTable />,
                        hide: true,
                      },
                      {
                        path: '/system/setup-annual-holiday/:id',
                        permissions: [PERMISSIONS.SETUP_ANNUAL_HOLIDAY],
                        element: <SetupAnnualHolidayForm />,
                        hide: true,
                      },
                    ],
                  },
                  // Thiết lập các Quy trình thực hiện dự án
                  {
                    path: '/system/project-schedule-setup',
                    titleKey: 'operations.project.projectScheduleSetup',
                    permissions: [PERMISSIONS.PROJECT_SCHEDULE_SETUP],
                    children: [
                      {
                        path: '/system/project-schedule-setup',
                        permissions: [PERMISSIONS.PROJECT_SCHEDULE_SETUP],
                        element: <ProjectScheduleSetupDataTable />,
                        hide: true,
                      },
                      {
                        path: '/system/project-schedule-setup/:id',
                        permissions: [PERMISSIONS.PROJECT_SCHEDULE_SETUP],
                        element: <ProjectScheduleSetupForm />,
                        hide: true,
                      },
                    ],
                  },
                  // Lịch sử thao tác
                  {
                    path: '/system/history-action',
                    titleKey: 'historyAction',
                    permissions: [PERMISSIONS.HISTORY_ACTION],
                    element: <HistoryActionDataTable />,
                  },
                ],
              },

              {
                path: '/ui-generate-tool',
                titleKey: 'uiGenerateTool',
                icon: BarChartHorizontal,
                permissions: [PERMISSIONS.REPORT_SERIAL_MANAGEMENT], // dùng quyền tạm
                element: <UIGenerateTool />,
                hide: true,
              },
            ],
          },
        ],
      },
      {
        path: '/sign-in',
        titleKey: 'login',
        element: <Login />,
        hide: true,
      },
      {
        path: '/check-domain/:type',
        titleKey: 'checkDomain',
        element: <DomainCheck />,
        hide: true,
      },
      {
        path: '/403',
        element: <Error403 />,
      },
      {
        path: '*',
        element: <ErrorPage />,
      },
    ],
  },
];

const routes = createBrowserRouter(routeConfig as RouteObject[]);

export default routes;
