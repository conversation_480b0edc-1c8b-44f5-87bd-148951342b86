import { FormCombobox } from '@/components/ui/form';
import { QUERIES } from '@/constant';
import { useEntity } from '@/hooks';
import { ConstructionTask, Unit } from '@/types';

export const ContractEditCellComboBox = () => {
  const { list: constructionTasks } = useEntity<ConstructionTask>({
    model: 'construction-task',
    queryKey: [QUERIES.CONSTRUCTION_TASK],
  });

  return (
    <>
      <FormCombobox
        model="construction-task"
        queryKey={[QUERIES.CONSTRUCTION_TASK]}
        showFields={['code', 'name']}
        options={constructionTasks}
      />
    </>
  );
};

export const ContractorEditCellComboBox = () => {
  const { list: contractors } = useEntity<ConstructionTask>({
    model: 'contractor',
    queryKey: [QUERIES.CONTRACTOR],
  });

  return (
    <>
      <FormCombobox
        model="construction-task"
        queryKey={[QUERIES.CONTRACTOR]}
        showFields={['code', 'name']}
        options={contractors}
      />
    </>
  );
};

export const UnitEditCellComboBox = () => {
  const { list: units } = useEntity<Unit>({
    model: 'unit',
    queryKey: [QUERIES.UNIT],
  });

  return (
    <>
      <FormCombobox
        model="unit"
        queryKey={[QUERIES.UNIT]}
        showFields={['code', 'name']}
        options={units}
      />
    </>
  );
};
