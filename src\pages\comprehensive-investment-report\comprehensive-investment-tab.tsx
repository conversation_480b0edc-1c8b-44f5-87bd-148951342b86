import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ComprehensiveInvestmentReportDetail } from '@/types';
import { ComprehensiveInvestmentCell } from './comprehensive-investment-cell';

export type ComprehensiveInvestmentTabType = {
  code: string;
  name: string;
  children?: ComprehensiveInvestmentTabType[];
};
export const ComprehensiveInvestmentTab = ({
  item,
  details,
}: {
  item: ComprehensiveInvestmentTabType;
  details: ComprehensiveInvestmentReportDetail[];
}) => {
  if (item.children) {
    return (
      <>
        <TabsContent value={`${item.code}`}>
          <Tabs defaultValue={item.children[0].code}>
            <div className="w-full">
              <TabsList>
                {item.children.map(child => (
                  <TabsTrigger key={child.code} value={child.code}>
                    {child.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            {item.children.map(child => (
              <TabsContent key={child.code} value={child.code}>
                <ComprehensiveInvestmentTab item={child} details={details} />
              </TabsContent>
            ))}
          </Tabs>
        </TabsContent>
      </>
    );
  }
  return (
    <TabsContent value={`${item.code}`} className="flex flex-wrap gap-x-2 gap-y-4">
      {details
        .filter(d => d.tabName === item.code)
        .map((item, index) => (
          <ComprehensiveInvestmentCell detail={item} index={index} key={item.id} />
        ))}
    </TabsContent>
  );
};
// {
