import { apply<PERSON>abel, close<PERSON>abe<PERSON>, TABLES } from '@/constant';
import { useAuth } from '@/hooks';
import { Payload } from '@/services';
import { Button } from 'devextreme-react';
import { useTranslation } from 'react-i18next';
import {
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  Editing,
  Toolbar,
  Item,
  FilterRow,
  Scrolling,
  Paging,
  Pager,
  HeaderFilter,
  Search,
  IColumnProps,
  Column,
} from 'devextreme-react/data-grid';
import { DevexDataGrid } from '../devex-data-grid';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';
import { getRandomNumber } from '@/lib/number';

type Props<T> = {
  onClose: () => void;
  onImportExcel: () => void;
  isImporting: boolean;
  professionType: number;
  professionColumns: IColumnProps[];
  data: T[];
  onApply: () => void;
  // setData;
};

const serialNumberColumn: IColumnProps = {
  dataField: 'serialNumber',
  caption: 'STT',
  dataType: 'number',
  format: ',##0,##',
  alignment: 'center',
  width: 60,
  allowEditing: false,
  allowFiltering: false,
  allowSorting: false,
  fixed: true,
  fixedPosition: 'left',
  cellRender: (cellInfo: {
    column: dxDataGridColumn;
    columnIndex: number;
    component: dxDataGrid;
    data: Record<string, any>;
    displayValue: any;
    oldValue: any;
    row: dxDataGridRowObject;
    rowIndex: number;
    rowType: string;
    text: string;
    value: any;
    watch: () => void;
  }) => {
    if (cellInfo.rowType === 'data') {
      const pageIndex = cellInfo.component.pageIndex();
      const pageSize = cellInfo.component.pageSize();
      const visibleRowIndex = cellInfo.component
        .getVisibleRows()
        .filter(item => item.rowType === 'data')
        .indexOf(cellInfo.row);
      return pageIndex * pageSize + visibleRowIndex + 1;
    }
  },
};

export const ImportExcelEditableData = <T extends Payload>({
  onClose,
  onImportExcel,
  isImporting,
  professionType,
  professionColumns,
  data,
  onApply,
  // setData,
}: Props<T>) => {
  const { t } = useTranslation('import');
  const { user } = useAuth();

  return (
    <div className="w-full overflow-scroll">
      <div className="grid grid-cols-24">
        <div className="col-span-24">
          <DevexDataGrid
            id={`${TABLES.IMPORT_CONFIGURATION_PRESENTATION}_${professionType}_${user?.userId}`}
            keyExpr="id"
            dataSource={data}
            columnAutoWidth
            allowColumnResizing
            columnResizingMode="widget"
            allowColumnReordering
            showBorders
            showColumnLines
            showRowLines
            wordWrapEnabled
            hoverStateEnabled
            focusedRowEnabled
            autoNavigateToFocusedRow
            hideSerialNumber
            // columns={professionColumns as ColumnProps[]}
          >
            <Editing
              mode="cell"
              allowUpdating={false}
              allowDeleting
              allowAdding={false}
              confirmDelete={false}
              useIcons
              newRowPosition="last"
            />
            <ColumnChooser enabled mode="select" height="45rem">
              <ColumnChooserSearch enabled />
              <ColumnChooserSelection allowSelectAll selectByClick recursive />
            </ColumnChooser>
            <FilterRow visible showOperationChooser />
            <Scrolling mode="standard" rowRenderingMode="standard" />
            <Paging enabled defaultPageSize={10} />
            <Pager
              visible
              showInfo
              showNavigationButtons
              showPageSizeSelector
              displayMode="adaptive"
              allowedPageSizes={[5, 10, 50, 100]}
            />
            <HeaderFilter visible>
              <Search enabled mode="contains" />
            </HeaderFilter>

            {/* Tạo cột thứ tự */}
            {professionColumns.length > 0 && (
              <Column key={-getRandomNumber()} {...serialNumberColumn} />
            )}
            {/* Tạo các cột nghiệp vụ */}
            {professionColumns.length > 0 &&
              professionColumns.map(props => <Column key={-getRandomNumber()} {...props} />)}

            <Toolbar>
              <Item location="before">
                <Button
                  stylingMode="contained"
                  type="default"
                  icon="download"
                  text={isImporting ? t('page.isImporting') : t('page.performImport')}
                  onClick={onImportExcel}
                />
              </Item>
              <Item location="after" name="columnChooserButton" />
            </Toolbar>
          </DevexDataGrid>
        </div>
      </div>
      <div className="mt-4 flex justify-end gap-x-2 bg-white py-1">
        <Button
          text={applyLabel}
          className="uppercase"
          icon="check"
          stylingMode="contained"
          type="success"
          //onClick={handleApply}
          onClick={onApply}
        />
        <Button
          text={closeLabel}
          className="uppercase"
          stylingMode="outlined"
          type="default"
          icon="close"
          onClick={onClose}
        />
      </div>
    </div>
  );
};
