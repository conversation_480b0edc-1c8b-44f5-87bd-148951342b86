/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { AGENCY_TYPE, MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useAuth, useDataTable, useEntity, usePermission, useSyncProjectFilter } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { SelectedProjectContext } from '@/provider/selected-project-context';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { Agency, ProjectStatus } from '@/types';
import { ProjectSettlement } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('projectSettlement');

const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const ProjectSettlementDataTable = ({ projectStatus }: { projectStatus: ProjectStatus }) => {
  const path = PATHS.PROJECT_SETTLEMENT(projectStatus.code);
  const navigate = useNavigate();
  const { t } = useTranslation('projectSettlement');

  const role = usePermission(PERMISSIONS.PROJECT_SETTLEMENT);
  const { user, projects } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: allAgencies } = useEntity<Agency>({ queryKey: [QUERIES.AGENCY], model: 'agency' });
  const agencies = allAgencies.filter(item => item.agencyType === AGENCY_TYPE.ISSUE);
  const { selectedProject } = useContext(SelectedProjectContext);
  const getTargetAlias = (target: ProjectSettlement | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<ProjectSettlement, PeriodFilter>({
    queryRangeName: 'projectSettlementTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<ProjectSettlement>('project-settlement'),
    deleteKey: [MUTATE.DELETE_PROJECT_SETTLEMENT],
    invalidateKey: [QUERIES.PROJECT_SETTLEMENT],
    initialQuery: {
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${selectedProject?.id || projectIds || 0})`,
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.PROJECT_SETTLEMENT],
    queryFn: () => {
      return createQueryPaginationFn<ProjectSettlement>('project-settlement')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'ProjectSettlementTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<ProjectSettlement>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<ProjectSettlement>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const { isUpdate, isDelete } = role || {};

  useSyncProjectFilter({
    queryListParams,
    queryListMethods,
    onSyncedParams: () => {
      callbackWithTimeout(refetch);
    },
  });

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn('projectSettlementTime', from!, to!);
          }

          callbackWithTimeout(refetch);
        }}
      >
        {/* <div className="flex w-full flex-col sm:flex-row sm:items-center">
          <FormLabel className="text-nowrap sm:w-[45px]">{t('fields.projectId')}</FormLabel>

          <FormField id="projectId" className="min-w-0 flex-1" name={'projectId'}>
            <SelectBox
              items={projects}
              searchExpr={['name', 'code']}
              valueExpr="id"
              onFocusIn={e => {
                const input = e.element.querySelector(
                  'input.dx-texteditor-input'
                ) as HTMLInputElement;
                if (input) input.select();
              }}
              searchEnabled
              searchMode="contains"
              displayExpr={displayExpr(['name'])}
              showClearButton
              focusStateEnabled={false}
            />
          </FormField>
        </div> */}
      </PeriodFilterForm>
      <DevexDataGrid
        id={TABLES.PROJECT_SETTLEMENT}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="projectSettlementTime"
          caption={t('fields.projectSettlementTime')}
          dataType="date"
          alignment="left"
        />

        <Column dataField="code" caption={t('fields.code')} alignment="left" />

        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="projectId" caption={t('fields.projectId')}>
          <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="agencyId" caption={t('fields.agencyId')}>
          <Lookup dataSource={agencies} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>

        <Column dataField="approvalNumber" caption={t('fields.approvalNumber')} alignment="left" />
        <Column
          dataField="approvalDate"
          caption={t('fields.approvalDate')}
          dataType="date"
          alignment="left"
        />
        <Column
          dataField="approvalContent"
          caption={t('fields.approvalContent')}
          alignment="left"
        />

        <Column dataField="note" caption={t('fields.note')} alignment="left" />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="projectSettlement"
      />
    </PageLayout>
  );
};
