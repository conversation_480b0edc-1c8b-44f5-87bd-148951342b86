import { BasicDialog } from '@/components/basic-dialog';
import { IUserPermission, RecordAttachment } from '@/types';
import { useTranslation } from 'react-i18next';
import { RecordFormFileSystem } from './record-form';

type UploadFileFormProps = {
  isRecordFormOpen: boolean;
  handleClose: () => void;
  handleOk: (updatedRow: RecordAttachment) => void;
  role?: IUserPermission;
  folder?: string;
};

export const UploadFileForm = ({
  isRecordFormOpen,
  handleClose,
  handleOk,
  role,
  folder,
}: UploadFileFormProps) => {
  const { t } = useTranslation('recordsAttachment');
  return (
    <BasicDialog
      open={isRecordFormOpen}
      toggle={() => {
        handleClose();
      }}
      title={t('page.form.files.title')}
      className="max-w-[100vw] md:max-w-[90vw]"
    >
      <RecordFormFileSystem role={role} onClose={handleClose} onOk={handleOk} folder={folder} />
    </BasicDialog>
  );
};
