import { BoardOfDirectorsWorkScheduleDetail } from '@/types';

export * from './board-of-directors-work-schedule-data-table';
export * from './board-of-directors-work-schedule-editable-table';
export * from './board-of-directors-work-schedule-form';
export * from './template/board-of-directors-work-schedule-print-template';
export * from './helper/array-group';

export const sortDetails = (data: BoardOfDirectorsWorkScheduleDetail[]) => {
  const rowsWithData = data.filter(row => row.workDate);
  const emptyRows = data.filter(row => !row.workDate);

  const sortedRows = [...rowsWithData].sort((a, b) => {
    const dateA = a.workDate ? new Date(a.workDate).getTime() : 0;
    const dateB = b.workDate ? new Date(b.workDate).getTime() : 0;
    if (dateA !== dateB) return dateA - dateB;

    const sessionA = a.session || 0;
    const sessionB = b.session || 0;
    if (sessionA !== sessionB) return sessionA - sessionB; // Giữ nguyên vì 1 < 2

    const timeA = a.workTime ? new Date(a.workTime).getTime() : 0;
    const timeB = b.workTime ? new Date(b.workTime).getTime() : 0;
    return timeA - timeB;
  });

  return [...sortedRows, ...emptyRows];
};
