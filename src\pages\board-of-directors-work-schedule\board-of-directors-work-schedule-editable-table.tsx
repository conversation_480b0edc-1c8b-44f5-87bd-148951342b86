import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  BoardOfDirectorsWorkSchedule,
  BoardOfDirectorsWorkScheduleDetail,
  defaultValuesBoardOfDirectorsWorkSchedule,
  Department,
  IUserPermission,
  User,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import { Combobox } from '@/components/combobox';
import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableTextArea,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { useMemo, useCallback } from 'react';
import { useEntity } from '@/hooks';
import { groupBoardOfDirectorsSchedule, sortDetails } from '.';
import { Button } from 'devextreme-react';
import { Workbook } from 'exceljs';
import { updateCellValue } from '../financial-settlement-report';
import { formatCustomTime, formatVietnameseWorkDate } from '@/lib/print';

const [defaultRow] = defaultValuesBoardOfDirectorsWorkSchedule.boardOfDirectorsWorkScheduleDetails;

type BoardOfDirectorsWorkScheduleEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  params: {
    fromDate: string;
    toDate: string;
    outputFileName: string;
    weekNumber: string;
    note: string;
  };
};

const t = translationWithNamespace('boardOfDirectorsWorkSchedule');
const sessions = [
  {
    id: 1,
    name: t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.morning'),
  },
  {
    id: 2,
    name: t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.afternoon'),
  },
];

export const BoardOfDirectorsWorkScheduleEditableTable = ({
  role,
  calculateForm,
  params = {
    fromDate: '1/1',
    toDate: '7/1',
    outputFileName: 'outputFile.xlsx',
    weekNumber: '1',
    note: '',
  },
}: BoardOfDirectorsWorkScheduleEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<BoardOfDirectorsWorkSchedule>();

  const [editableData] = useWatch({
    control,
    name: ['boardOfDirectorsWorkScheduleDetails'],
  });

  const { list: users } = useEntity<User>({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: departments } = useEntity<Department>({
    queryKey: [QUERIES.DEPARTMENT],
    model: 'department',
  });

  // Hàm xử lý khi có thay đổi trong workDate, session hoặc workTime
  const handleDateTimeChange = useCallback(
    (updatedData: BoardOfDirectorsWorkScheduleDetail[]) => {
      // Sắp xếp dữ liệu
      const sortedData = sortDetails(updatedData);

      // Cập nhật form với dữ liệu đã sắp xếp
      setValue('boardOfDirectorsWorkScheduleDetails', sortedData);

      // Gọi hàm tính toán nếu cần
      calculateForm?.();
    },
    [setValue, calculateForm]
  );

  const boardOfDirectorsWorkScheduleEditableColumns: ColumnDef<BoardOfDirectorsWorkScheduleDetail>[] =
    useMemo(
      () => [
        {
          id: 'workDate',
          accessorKey: 'workDate',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.workDate'),
          cell: props => (
            <EditableDatePickerCell
              {...props}
              onValueChanged={e => {
                const data = [...props.table.options.data];
                data[props.row.index] = {
                  ...props.row.original,
                  [props.column.id]: e.value,
                };

                if (data[data.length - 1].workDate !== null) {
                  data.push({ ...defaultRow, id: -getRandomNumber() });
                }

                handleDateTimeChange(data);
              }}
            />
          ),
        },
        {
          id: 'session',
          accessorKey: 'session',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.session'),
          cell: props => (
            <Combobox
              options={sessions}
              value={props.getValue<number>()}
              className="rounded-none border-none"
              onChange={value => {
                // Cập nhật giá trị session
                const data = [...props.table.options.data];
                data[props.row.index] = {
                  ...props.row.original,
                  session: value as number,
                };

                // Sắp xếp lại dữ liệu
                handleDateTimeChange(data);
              }}
            />
          ),
        },
        {
          id: 'workTime',
          accessorKey: 'workTime',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.workTime'),
          cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
            <EditableDatePickerCell
              {...props}
              type="datetime"
              displayFormat={'shortTime'}
              calendarOptions={{
                visible: false,
              }}
              onValueChanged={e => {
                // Cập nhật giá trị workTime
                const data = [...props.table.options.data];
                data[props.row.index] = {
                  ...props.row.original,
                  workTime: e.value,
                };

                // Sắp xếp lại dữ liệu
                handleDateTimeChange(data);
              }}
            />
          ),
        },
        {
          id: 'content',
          accessorKey: 'content',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.content'),
          cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
            <EditableTextArea {...props} className="max-h-[300px]" />
          ),
        },
        {
          id: 'chairperson',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.chairperson'),
          columns: [
            {
              id: 'chairpersonId',
              accessorKey: 'chairpersonId',
              header: t('fields.boardOfDirectorsWorkScheduleDetails.chairpersonId'),
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableDropdownCell
                  {...props}
                  model="user"
                  queryKey={[QUERIES.USERS]}
                  defaultText={props.row.original.chairpersonName}
                />
              ),
            },
            {
              id: 'chairpersonOther',
              accessorKey: 'chairpersonOther',
              header: t('fields.boardOfDirectorsWorkScheduleDetails.chairpersonOther'),
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableTextArea {...props} className="max-h-[300px]" />
              ),
            },
          ],
        },
        {
          id: 'member',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.member'),
          // size: 900,
          columns: [
            {
              id: 'departmentIdArray',
              accessorKey: 'departmentIdArray',
              header: t('fields.boardOfDirectorsWorkScheduleDetails.departmentIds'),
              // size: 300,
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableDropdownCell
                  {...props}
                  multiple
                  model="department"
                  queryKey={[QUERIES.DEPARTMENT]}
                  showFields={['name']}
                  onChange={value => {
                    const {
                      row: { original },
                    } = props;
                    const selectedDepartments = value as number[];
                    const newMemberIds = users
                      .filter(user => selectedDepartments.some(v => v === user.departmentId))
                      .map(user => user.id);
                    const uniqueMemberIds = [...new Set([...newMemberIds])].filter(id => !!id);

                    const newValues = {
                      ...original,
                      departmentIdArray: selectedDepartments,
                      memberIdArray: uniqueMemberIds,
                    };

                    props.table.options.meta?.updateRowValues(newValues, props.row.index);
                  }}
                />
              ),
            },
            {
              id: 'departmentOther',
              accessorKey: 'departmentOther',
              header: t('fields.boardOfDirectorsWorkScheduleDetails.departmentOther'),
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableTextArea {...props} className="max-h-[300px]" />
              ),
            },
            {
              id: 'memberIdArray',
              accessorKey: 'memberIdArray',
              // size: 300,
              header: t('fields.boardOfDirectorsWorkScheduleDetails.memberIds'),
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableDropdownCell
                  {...props}
                  multiple
                  model="user"
                  showFields={['name']}
                  queryKey={[QUERIES.USERS]}
                  key={`member-dropdown-${props.row.id}-${JSON.stringify(props.getValue())}`}
                />
              ),
            },
            {
              id: 'memberOther',
              accessorKey: 'memberOther',
              header: t('fields.boardOfDirectorsWorkScheduleDetails.memberOther'),
              cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
                <EditableTextArea {...props} className="max-h-[300px]" />
              ),
            },
          ],
        },
        {
          id: 'contentPreparerId',
          accessorKey: 'contentPreparerId',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.contentPreparerId'),
          cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
            <EditableDropdownCell
              {...props}
              model="user"
              queryKey={[QUERIES.USERS]}
              defaultText={props.row.original.contentPreparerName}
            />
          ),
        },
        {
          id: 'contentPreparerOther',
          accessorKey: 'contentPreparerOther',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.contentPreparerOther'),
          cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
            <EditableTextArea {...props} className="max-h-[300px]" />
          ),
        },
        {
          id: 'location',
          accessorKey: 'location',
          header: t('fields.boardOfDirectorsWorkScheduleDetails.location'),
          cell: (props: CellContext<BoardOfDirectorsWorkScheduleDetail, unknown>) => (
            <EditableTextArea {...props} className="max-h-[300px]" />
          ),
        },
        {
          id: 'removeRow',
          header: '',
          size: 10,
          cell: props => {
            return (
              <DataTableRowActions
                onDelete={() => {
                  props.table.options.meta?.removeRowByIndex(props.row.index);
                }}
                canDelete={role?.isCreate || role?.isUpdate}
              />
            );
          },
        },
      ],
      [role?.isCreate, role?.isUpdate, users, handleDateTimeChange]
    );

  const exportExcelHandler = async () => {
    const groupData = groupBoardOfDirectorsSchedule(editableData, [], users, []);
    // Tạo object từ biến users, key là user.id, value là user.name
    const userNames = users.reduce(
      (acc: Record<number, string | null>, user) => {
        acc[user.id] = user.name;
        return acc;
      },
      {} as Record<number, string>
    );
    const departmentNames = departments.reduce(
      (acc: Record<number, string | null>, department) => {
        acc[department.id] = department.name;
        return acc;
      },
      {} as Record<number, string>
    );

    const response = await fetch(
      '/templates/board-of-directors-work-schedule/LichLamViecCuaBanGiamDoc_template.xlsx'
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const workbook = new Workbook();

    await workbook.xlsx.load(arrayBuffer);

    // Đổi tên đầu tiên sheet từ `from-to` thành `{fromDate}-{toDate}`
    const worksheet = workbook.getWorksheet('from-to');
    // Trường hợp không tồn tại sheet `from-to` thì ngưng xử lý
    if (!worksheet) {
      console.error('Không tìm thấy sheet "from-to" trong file mẫu.');
      return;
    }

    // Xóa ký `/` trong định dạng ngày, tên sheet có tên là: 0101-0701
    worksheet.name = `${params.fromDate.replace(/\//g, '')}-${params.toDate.replace(/\//g, '')}`;

    // Danh sách các ô cần chỉnh sửa
    const cells = ['A2'];

    const cellContents: Record<(typeof cells)[number], { [key: string]: any }> = {
      A2: {
        weekNumber: params.weekNumber,
        fromDate: params.fromDate,
        toDate: params.toDate,
      },
    };

    // Thay thế các placeholder trong file template
    cells.forEach(cell => {
      if (!cellContents[cell]) return;
      updateCellValue(worksheet, cell, cellContents[cell]);
    });

    // Dòng bắt đầu A4
    let currentRow = 4;
    const defualtDate = new Date();

    editableData.forEach(item => {
      // Tạo mảng tên thành viên
      let memberNames = (item?.memberIdArray ?? []).map(memberId =>
        userNames[memberId] ? `- ${userNames[memberId]}` : ''
      );
      let departNames = (item?.departmentIdArray ?? []).map(departmentId =>
        departmentNames[departmentId] ? `- ${departmentNames[departmentId]}` : ''
      );

      if ((item?.memberIdArray ?? []).length === 1) {
        if ((item?.memberIdArray?.[0] ?? null) === 0) memberNames = [];
      }
      if ((item?.departmentIdArray ?? []).length === 1) {
        if ((item?.departmentIdArray?.[0] ?? null) === 0) departNames = [];
      }

      const row = worksheet.getRow(currentRow);
      // Ngày
      row.getCell('A').value = formatVietnameseWorkDate(
        item.workDate?.toDateString() ?? defualtDate.toString()
      );
      // Thời gian
      row.getCell('B').value = item.session === 1 ? 'Sáng' : 'Chiều';
      row.getCell('C').value = formatCustomTime(
        item.workTime?.toString() ?? defualtDate.toString()
      );
      // Nội dung
      row.getCell('D').value = item.content;
      // Người chủ trì
      const chairpersonNames: string[] = [];
      if (item.chairpersonName) chairpersonNames.push(`- ${item.chairpersonName}`);
      if (item.chairpersonOther) chairpersonNames.push(item.chairpersonOther);
      row.getCell('E').value = chairpersonNames.join('\n');

      // Thành phần tham dự
      let memberNamesText;

      if (departNames.length > 0) {
        departNames.push(item.departmentOther ?? '');
        departNames.push(item.memberOther ?? '');
        memberNamesText = departNames.join('\n');
      } else {
        if (item.departmentOther) memberNames.unshift(`${item.departmentOther}`);
        if (item.memberOther) memberNames.push(item.memberOther);
        memberNamesText = memberNames.join('\n');
      }

      row.getCell('F').value = memberNamesText;
      // Địa điểm
      row.getCell('G').value = item.location;

      // Tính chiều cao dựa trên nội dung thực tế
      const calculateRowHeight = () => {
        const baseHeight = 20; // Chiều cao tối thiểu cho 1 dòng
        const columnWidth = 25; // Độ rộng ước tính của cột (ký tự)
        const charWidth = 1.2; // Độ rộng trung bình của 1 ký tự

        // Tính số dòng cho từng nội dung
        const contentLines = Math.max(1, Math.ceil((item.content?.length || 0) / columnWidth));
        const chairpersonLines = Math.max(
          1,
          chairpersonNames.reduce((total, name) => {
            return total + Math.ceil((name.length * charWidth) / columnWidth);
          }, 0)
        );
        const memberLines = Math.max(
          1,
          memberNames.reduce((total, name) => {
            return total + Math.ceil((name.length * charWidth) / columnWidth);
          }, 0)
        );
        const locationLines = Math.max(
          1,
          Math.ceil(((item.location?.length || 0) * charWidth) / columnWidth)
        );

        // Lấy số dòng lớn nhất
        const maxLines = Math.max(contentLines, chairpersonLines, memberLines, locationLines);

        return memberNamesText.length > 1 ? Math.max(baseHeight, maxLines * baseHeight) : 40;
      };

      row.height = calculateRowHeight();
      // Set style cho các ô
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        if (colNumber > 7) return;
        cell.style = {
          ...cell.style,
          alignment: { horizontal: 'left', vertical: 'middle', wrapText: true },
        }; // Giữ lại style ban đầu
        cell.font = { bold: false, name: 'Times New Roman', size: 14 };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      // Tăng dòng
      currentRow++;
    });

    // Tạo tiêu đề ghi chú
    const noteRow = worksheet.getRow(currentRow);
    noteRow.height = 18.75;
    noteRow.getCell('A').value = 'Ghi chú:';
    noteRow.getCell('A').font = { bold: true, name: 'Times New Roman', size: 14 };

    // Tạo nội dung ghi chú
    currentRow++;
    const notes = params.note.split('\n');
    notes.forEach(note => {
      const noteContentRow = worksheet.getRow(currentRow);
      noteContentRow.height = 18.75;
      noteContentRow.getCell('A').value = note;
      noteContentRow.getCell('A').font = { bold: true, name: 'Times New Roman', size: 14 };
      noteContentRow.getCell('A').alignment = { wrapText: false };
      currentRow++;
    });

    // Tạo tiêu đề chữ ký
    currentRow++;
    const signTitleRow = worksheet.getRow(currentRow);
    signTitleRow.height = 18.75;
    signTitleRow.getCell('F').value = 'BAN QUẢN LÝ DỰ ÁN ĐẦU TƯ XÂY DỰNG KHU VỰC';
    signTitleRow.getCell('F').font = { bold: true, name: 'Times New Roman', size: 14 };
    signTitleRow.getCell('F').alignment = { wrapText: false };

    // Vị trí bắt đầu merge của cột A
    let columnAIndex = 4;
    // Vị trí bắt đầu merge của cột B
    let columnBIndex = 4;
    groupData.forEach(group => {
      // Nếu rowspan > 1 thì thực hiện merge từ vị trí  đến columnAIndex - 1
      if (group.rowspan > 1) {
        worksheet.mergeCells(`A${columnAIndex}:A${columnAIndex + group.rowspan - 1}`);
      }
      columnAIndex += group.rowspan;
      group.sessions.forEach(session => {
        if (session.rowspan > 1) {
          worksheet.mergeCells(`B${columnBIndex}:B${columnBIndex + session.rowspan - 1}`);
        }
        columnBIndex += session.rowspan;
      });
    });

    // Lưu file
    workbook.xlsx
      .writeBuffer()
      .then(buffer => {
        const blob = new Blob([buffer], { type: 'application/octet-stream' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        a.download = `${params.outputFileName}_${params.weekNumber}_(${params.fromDate}-${params.toDate}).xlsx`;
        a.click();
        window.URL.revokeObjectURL(url);
      })
      .catch(error => console.log('error:', error));
  };

  return (
    <div>
      <DataTable
        tableId={TABLES.BOARD_OF_DIRECTORS_WORK_SCHEDULE_DETAIL}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('boardOfDirectorsWorkScheduleDetails', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={boardOfDirectorsWorkScheduleEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.boardOfDirectorsWorkScheduleDetails?.message && (
                <ErrorMessage message={errors.boardOfDirectorsWorkScheduleDetails?.message} />
              )}
              <Button
                stylingMode="text"
                icon="download"
                text="Xuất Excel"
                type="default"
                onClick={() => {
                  void exportExcelHandler();
                }}
              />
            </>
          );
        }}
      />
    </div>
  );
};
