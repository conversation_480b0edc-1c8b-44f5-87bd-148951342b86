/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MONEY_FORMAT, PERCENT_FORMAT_WITHOUT_SUFFIX, QUERIES, TABLES } from '@/constant';
import { useDataTable } from '@/hooks';
import { toLocaleDate } from '@/lib/date';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout } from '@/lib/utils';
import { createQueryReport } from '@/services';
import { RptReportOnImplementationOfDirectivesByUser } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { startOfYear } from 'date-fns';
import { Column, Editing, Export, GroupItem, Summary, TotalItem } from 'devextreme-react/data-grid';
import { snakeCase } from 'lodash';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

const t = translationWithNamespace('rptReportOnImplementationOfDirectivesByUser');

const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const RptReportOnImplementationOfDirectivesByUserTemplateDataTable = ({
  isForFinance = false,
}: {
  isForFinance?: boolean;
}) => {
  const nameSpace = useMemo(() => {
    if (isForFinance) {
      return 'rptReportOnImplementationOfDirectivesByUserFinance';
    }
    return 'rptReportOnImplementationOfDirectivesByUser';
  }, [isForFinance]);
  const { t } = useTranslation(nameSpace);
  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<RptReportOnImplementationOfDirectivesByUser, PeriodFilter>({
    initialQuery: {
      fromDate: startOfYear(new Date()),
      toDate: new Date(),
      objParam: {},
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER],
    queryFn: () => {
      return createQueryReport<RptReportOnImplementationOfDirectivesByUser>(
        'implementation-of-directives-by-user-report'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          values = values as PeriodFilter & Record<string, string>;
          const [from, to] = values.range;
          queryListMethods.setQueryListParams(prev => ({
            ...prev,
            objParam: {
              fromDate: toLocaleDate(from),
              toDate: toLocaleDate(to),
            },
          }));
          callbackWithTimeout(refetch);
        }}
      ></PeriodFilterForm>
      <DevexDataGrid
        id={TABLES.RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER}
        dataSource={items}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        keyExpr={'ordinalNumber'}
      >
        <Export enabled={true} />
        <Editing allowUpdating={false} allowDeleting={false} useIcons />
        <Column dataField="executorName" caption={t('fields.executorName')} />
        <Column dataField="departmentName" caption={t('fields.departmentName')} />
        <Column
          dataField="assignedQuantity"
          caption={t('fields.assignedQuantity')}
          dataType="number"
          format={MONEY_FORMAT}
        />
        <Column
          dataField="withinDeadlineQuantity"
          caption={t('fields.withinDeadlineQuantity')}
          dataType="number"
          format={MONEY_FORMAT}
        />
        <Column
          dataField="lateQuantity"
          caption={t('fields.lateQuantity')}
          dataType="number"
          format={MONEY_FORMAT}
        />
        <Column
          dataField="aheadOfScheduleQuantity"
          caption={t('fields.aheadOfScheduleQuantity')}
          dataType="number"
          format={MONEY_FORMAT}
        />

        <Column
          dataField="onTimeQuantity"
          caption={t('fields.onTimeQuantity')}
          dataType="number"
          format={MONEY_FORMAT}
        />
        <Column
          dataField="completionRate"
          caption={t('fields.completionRate')}
          dataType="number"
          format={PERCENT_FORMAT_WITHOUT_SUFFIX}
        />
        <Column
          dataField="aheadOfScheduleCompletionRate"
          caption={t('fields.aheadOfScheduleCompletionRate')}
          dataType="number"
          format={PERCENT_FORMAT_WITHOUT_SUFFIX}
        />
        <Column dataField="rank" caption={t('fields.rank')} />
        <Summary>
          <TotalItem
            column="withinDeadlineQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
          />
          <GroupItem
            column="withinDeadlineQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="lateQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
          />
          <GroupItem
            column="lateQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="aheadOfScheduleQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
          />
          <GroupItem
            column="aheadOfScheduleQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="assignedQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
          />
          <GroupItem
            column="assignedQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="completionRate"
            summaryType="sum"
            valueFormat={PERCENT_FORMAT_WITHOUT_SUFFIX}
            displayFormat="{0}"
          />
          <GroupItem
            column="completionRate"
            summaryType="sum"
            valueFormat={PERCENT_FORMAT_WITHOUT_SUFFIX}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="aheadOfScheduleCompletionRate"
            summaryType="sum"
            valueFormat={PERCENT_FORMAT_WITHOUT_SUFFIX}
            displayFormat="{0}"
          />
          <GroupItem
            column="aheadOfScheduleCompletionRate"
            summaryType="sum"
            valueFormat={PERCENT_FORMAT_WITHOUT_SUFFIX}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />

          <TotalItem
            column="onTimeQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
          />
          <GroupItem
            column="onTimeQuantity"
            summaryType="sum"
            valueFormat={MONEY_FORMAT}
            displayFormat="{0}"
            showInGroupFooter={false}
            alignByColumn={true}
          />
        </Summary>
      </DevexDataGrid>
    </PageLayout>
  );
};
