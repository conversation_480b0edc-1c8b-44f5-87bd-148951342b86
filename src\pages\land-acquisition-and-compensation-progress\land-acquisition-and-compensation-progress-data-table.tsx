/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import {
  MONEY_FORMAT,
  PERCENT_FORMAT_WITHOUT_SUFFIX,
  QUERIES,
  TABLES,
  viewLabel,
} from '@/constant';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout } from '@/lib/utils';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { LandAcquisitionAndCompensationProgress } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { snakeCase } from 'lodash';
import { Button as ButtonDev, DateBox } from 'devextreme-react';
import { useTranslation } from 'react-i18next';
import { Label } from '@/components/ui/label';
import { useCallback, useMemo, useState } from 'react';
import axiosInstance, { request } from '@/axios-instance';
import dayjs from 'dayjs';
import type { Column } from 'devextreme/ui/data_grid';

const t = translationWithNamespace('landAcquisitionAndCompensationProgressReport');
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const LandAcquisitionAndCompensationProgressReportDataTable = () => {
  const [usageYear, setUsageYear] = useState<Date>(new Date());

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT],
    queryFn: () => {
      return request<{ items: LandAcquisitionAndCompensationProgress[] }>(
        axiosInstance.post('/land-acquisition-and-compensation-progress/get-report', {
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'OrdinalNumber',
          sortOrder: 1,
          isPage: false,
          filterColumn: [],
          objParam: { year: usageYear, landAcquisitionAndCompensationProgressId: 0 },
        })
      );
    },
  });

  const { items } = data || { items: [] };
  const { t: translator } = useTranslation('landAcquisitionAndCompensationProgressReport');
  const t = useCallback(
    (fieldName: string, options?: Record<string, string | number>) => {
      const thisYear = usageYear?.getFullYear() || new Date().getFullYear();
      const year = thisYear;
      const nextYear = thisYear + 1;
      const previousYear = thisYear - 1;

      return translator(fieldName, {
        year,
        previousYear,
        nextYear,
        ...options,
        interpolation: { escapeValue: false },
      });
    },
    [usageYear, translator]
  );
  const createWeekColumns = useMemo(
    () =>
      (weekIndex: number, year: number): Column[] => {
        const weekNum = String(weekIndex).padStart(2, '0');
        const landKey = `landWeek${weekNum}`;

        const startDate = dayjs().year(year).week(weekIndex).day(1); // day(1) là thứ Hai
        const endDate = dayjs().year(year).week(weekIndex).day(6); // day(6) là thứ Bảy
        const displayStartDate =
          startDate.year() === year ? startDate : dayjs().year(year).startOf('year');
        const displayEndDate = endDate.year() === year ? endDate : dayjs().year(year).endOf('year');

        const fromDate = displayStartDate.format('DD/MM');
        const toDate = displayEndDate.format('DD/MM');
        const dateRange = `(${fromDate}-${toDate})`;

        return [
          {
            dataField: landKey,
            caption: t('fields.disbursementProgressByFundingSourceDetails.landWeekHeader', {
              weekNum,
              dateRange,
              fromDate,
              toDate,
            }),
            allowEditing: false,
            dataType: 'number',
            format: MONEY_FORMAT,
            alignment: 'left',
          },
        ];
      },
    [t]
  );
  const { allWeekColumns } = useMemo(() => {
    const calculationYear = usageYear instanceof Date ? dayjs(usageYear).year() : dayjs().year();

    const allWeekColumns = Array.from({ length: 52 }, (_, i) =>
      createWeekColumns(i + 1, calculationYear)
    ).flat();

    const currentWeek = dayjs().week();
    let startWeek: number;
    let endWeek: number;

    if (currentWeek === 1) {
      startWeek = 1;
      endWeek = 3;
    } else if (currentWeek === 52) {
      startWeek = 50;
      endWeek = 52;
    } else {
      startWeek = currentWeek - 1;
      endWeek = currentWeek + 1;
    }

    const getWeekNumberFromId = (id: string | undefined): number | null => {
      if (!id) return null;
      const match = id.match(/(?:week|landWeek)(\d{2})$/);
      return match ? parseInt(match[1], 10) : null;
    };

    allWeekColumns.forEach(col => {
      if (col.dataField) {
        const weekNum = getWeekNumberFromId(col.dataField);
        const isVisibleByDefault = weekNum !== null && weekNum >= startWeek && weekNum <= endWeek;
        col.visible = isVisibleByDefault;
      }
    });

    return {
      allWeekColumns: [...allWeekColumns],
    };
  }, [usageYear, createWeekColumns]);
  const columns: Column[] = [
    {
      cellTemplate: (container, options) => {
        const pageIndex = options.component.pageIndex();
        const pageSize = options.component.pageSize();
        const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
        container.textContent = serialNumber.toString();
      },
      caption: 'STT',
      fixed: true,
      fixedPosition: 'left',
      alignment: 'center',
      width: 50,
      dataField: 'serialNumber',
      allowSorting: false,
      allowFiltering: false,
      allowEditing: false,
      format: ',##0,##',
    },
    {
      dataField: 'projectName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.projectId'),
      allowEditing: false,
      alignment: 'left',
    },
    {
      dataField: 'totalInvestment',
      caption: t('fields.disbursementProgressByFundingSourceDetails.totalInvestment'),
      dataType: 'number',
      format: MONEY_FORMAT,
      allowEditing: false,
      alignment: 'right',
    },
    {
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementSummary'
      ),
      alignment: 'center',
      columns: [
        {
          dataField: 'totalFundingRequirement',
          caption: t('fields.disbursementProgressByFundingSourceDetails.totalFundingRequirement'),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
        {
          dataField: 'totalFundingRequirementCompensation',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementCompensation'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
        {
          dataField: 'totalFundingRequirementConstructionConsulting',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementConstructionConsulting'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
      ],
    },
    {
      caption: t(
        'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilSummary'
      ),
      alignment: 'center',
      columns: [
        {
          dataField: 'cumulativeDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
        {
          dataField: 'compensationDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.compensationDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
        {
          dataField: 'constructionConsultingDisbursementYearToDate',
          caption: t(
            'fields.disbursementProgressByFundingSourceDetails.constructionConsultingDisbursementYearToDate'
          ),
          dataType: 'number',
          format: MONEY_FORMAT,
          allowEditing: false,
          alignment: 'right',
        },
      ],
    },
    {
      dataField: 'disbursementRateYearToDate',
      caption: t('fields.disbursementProgressByFundingSourceDetails.disbursementRateYearToDate'),
      dataType: 'number',
      format: PERCENT_FORMAT_WITHOUT_SUFFIX,
      allowEditing: false,
      alignment: 'right',
    },
    {
      dataField: 'constructionPeriod',
      caption: t('fields.disbursementProgressByFundingSourceDetails.constructionPeriod'),
      dataType: 'string',
      alignment: 'left',
    },
    //weeks
    ...allWeekColumns,
    {
      dataField: 'issuesAndRecommendations',
      caption: t('fields.disbursementProgressByFundingSourceDetails.issuesAndRecommendations'),
      dataType: 'string',
      alignment: 'left',
    },
    {
      dataField: 'locationMap',
      caption: t('fields.disbursementProgressByFundingSourceDetails.locationMap'),
      dataType: 'string',
      allowEditing: false,
      alignment: 'left',
      cellTemplate: (cellElement, cellInfo) => {
        if (cellInfo.value) {
          cellElement.innerHTML = `<a href="${cellInfo.value}" target="_blank" class="hover:text-blue-700 text-blue-600 underline">Bấm vào link</a>`;
        }
      },
    },
    {
      dataField: 'planningInformation',
      caption: t('fields.disbursementProgressByFundingSourceDetails.planningInformation'),
      dataType: 'string',
      allowEditing: false,
      alignment: 'left',
    },
    {
      dataField: 'departmentInChargeName',
      caption: t('fields.disbursementProgressByFundingSourceDetails.departmentInChargeId'),
      allowEditing: false,
      alignment: 'left',
    },
  ];

  return (
    <PageLayout header={t('page.header')}>
      <div className="flex items-center gap-x-4">
        <div className="flex items-center gap-x-4">
          <Label htmlFor="year">{t('fields.year')}</Label>
          <DateBox
            value={usageYear}
            id="year"
            type="date"
            calendarOptions={{
              maxZoomLevel: 'decade',
              minZoomLevel: 'decade',
            }}
            displayFormat={'year'}
            onValueChanged={e => {
              if (e.value) {
                const date = e.value as Date;
                setUsageYear(date);
              }
            }}
            pickerType="calendar"
            focusStateEnabled={false}
          />
        </div>
        <ButtonDev
          text={viewLabel}
          className="w-fit"
          stylingMode="contained"
          type="default"
          icon="search"
          onClick={() => {
            callbackWithTimeout(refetch);
          }}
        />
      </div>
      <DevexDataGrid
        id={TABLES.LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT}
        dataSource={items}
        keyExpr="ordinalNumber"
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        columns={columns}
        editing={{
          allowUpdating: false,
          allowAdding: false,
          allowDeleting: false,
          useIcons: true,
        }}
      />
    </PageLayout>
  );
};
