import { useContext, useEffect } from 'react';
import { SelectedProjectContext } from '@/provider/selected-project-context';
import { QueryPaginationParams } from '@/types';
import { useAuth } from './use-auth';

interface UseSyncProjectFilterProps {
  queryListParams: Partial<QueryPaginationParams>;
  queryListMethods: {
    addOrReplaceFilterColumn: (filter: {
      column: string;
      expression: string;
      keySearch: string;
    }) => void;
  };
  onSyncedParams?: () => void;
}

export function useSyncProjectFilter({
  queryListParams,
  queryListMethods,
  onSyncedParams,
}: UseSyncProjectFilterProps) {
  const { selectedProject } = useContext(SelectedProjectContext);
  const { user, projects } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;

  useEffect(() => {
    const existingColumn = queryListParams.filterColumn?.find((i: any) => i.column === 'ProjectId');

    const keySearch = `(${selectedProject?.id ? String(selectedProject?.id) : projectIds || 0})`;

    if (!existingColumn || existingColumn.keySearch !== keySearch) {
      queryListMethods.addOrReplaceFilterColumn({
        column: 'projectId',
        expression: 'IN',
        keySearch,
      });
      onSyncedParams?.();
    }
  }, [selectedProject, projectIds, queryListParams, queryListMethods, onSyncedParams]);
}
