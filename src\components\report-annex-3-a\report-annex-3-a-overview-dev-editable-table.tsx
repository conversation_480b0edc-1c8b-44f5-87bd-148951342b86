import { ErrorMessage } from '@/components/ui/error-message';
import { DEFAULT_DECIMAL, getDataLabel, QUERIES, TABLES } from '@/constant';
import {
  Contractor,
  defaultValuesReportAnnex3a,
  GetReportAnnex3aPaymentReceipt,
  IUserPermission,
  ReportAnnex3a,
  ReportAnnex3aDetail,
  ReportAnnex3aOverview,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { translationWithNamespace } from '@/lib/i18nUtils';
import notification from '@/lib/notifications';
import { convertMoneny, getRandomNumber } from '@/lib/number';
import { getSumsFromArray } from '@/lib/utils';
import { getRequest, Model } from '@/services';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  Scrolling,
  Paging,
  Pager,
  Editing,
  Toolbar,
  Item,
  type DataGridTypes,
  RequiredRule,
} from 'devextreme-react/data-grid';
import { useCallback } from 'react';
import { FormCombobox } from '../ui/form';
import { cn } from '@/lib/utils';
import { SelectBoxTypes } from 'devextreme-react/cjs/select-box';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';
import { useEntity } from '@/hooks';
import { customizeNumberCell, onCellPrepared } from '../devex-data-grid';
import { InputNumber } from '../ui/input';
import { Button, NumberBox } from 'devextreme-react';

const [defaultRow] = defaultValuesReportAnnex3a.reportAnnex3aOverviews;

type ReportAnnex3aOverviewDevEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  isForFinance?: boolean;
};

const t = translationWithNamespace('reportAnnex3A');

export const ReportAnnex3aOverviewDevEditableTable = ({
  role,
  calculateForm,
  isForFinance,
}: ReportAnnex3aOverviewDevEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    getValues,
    control,
    formState: { errors },
  } = useFormContext<ReportAnnex3a>();

  let model: Model = 'report-annex-3a';
  if (isForFinance) {
    model = 'report-annex-3a-finance';
  }

  const [editableData, reportAnnex3aDetails, acceptance] = useWatch({
    control,
    name: ['reportAnnex3aOverviews', 'reportAnnex3aDetails', 'completionAcceptanceId'],
  });

  const calculateRow = useCallback(
    (
      row: ReportAnnex3aOverview,
      column: string,
      value: number
    ): Record<string, string | number | null | undefined> => {
      const newRow = { ...row };
      let _contractValue = newRow.contractValue; /// 1.
      let _advancePaymentExecution = newRow.advancePaymentExecution; //// 3.
      let _cumulativePaymentForCompletedVolume = newRow.cumulativePaymentForCompletedVolume;
      const _advancePaymentRecovery = Number(newRow.advancePaymentRecovery);
      const _valuePaymentForCompletedVolume = Number(newRow.valuePaymentForCompletedVolume);
      if (column === 'contractValue') {
        _contractValue = value;
      }

      if (column === 'advancePaymentExecution') {
        _advancePaymentExecution = value;
        _cumulativePaymentForCompletedVolume =
          _advancePaymentExecution +
          Number(_advancePaymentRecovery) +
          Number(_valuePaymentForCompletedVolume);
      }

      return {
        ...newRow,
        contractValue: Number(_contractValue),
        advancePaymentExecution: Number(_advancePaymentExecution),
        cumulativePaymentForCompletedVolume: Number(_cumulativePaymentForCompletedVolume),
      };
    },
    []
  );

  /**
   * Handles the change of cell value in the editable table.
   * It updates the row data based on the changed value and recalculates the necessary fields
   * @param {DataGridTypes.ColumnEditCellTemplateData} cell - The cell data containing the row and column information.
   * @param {number} value - The new value to be set in the cell.
   * @return {void}
   * @description This function finds the row in the editable data that matches the cell's row
   */
  const handleCellValueChange = useCallback(
    (cell: DataGridTypes.ColumnEditCellTemplateData, value: number) => {
      if (value === undefined) return;

      const newRowValue = calculateRow(
        cell.data as ReportAnnex3aOverview,
        cell.column.dataField as string,
        value
      );

      if (cell.column.dataField) {
        (newRowValue as any)[cell.column.dataField] = value;
      }

      const allRows = getValues('reportAnnex3aOverviews');
      const updatedData = [...(allRows || [])];
      if (cell.rowIndex > -1) {
        updatedData[cell.rowIndex] = newRowValue as ReportAnnex3aOverview;
      }

      setValue('reportAnnex3aOverviews', updatedData, {
        shouldDirty: true,
        shouldTouch: true,
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      cell.setValue(value);
    },
    [calculateRow, getValues, setValue]
  );

  const { list: contractors } = useEntity<Contractor>({
    model: 'contractor',
    queryKey: [QUERIES.CONTRACTOR],
  });

  // Tự động chọn (bôi đen) toàn bộ văn bản khi người dùng nhấp vào ô
  const selectTextOnFocus = (args: any) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    args.element.querySelector('input.dx-texteditor-input')?.select();
  };

  return (
    <div>
      <DataGrid
        id={TABLES.REPORT_ANNEX_3A_OVERVIEW}
        keyExpr={'id'}
        dataSource={editableData}
        columnAutoWidth
        allowColumnResizing
        columnResizingMode="widget"
        allowColumnReordering
        showBorders
        showColumnLines
        showRowLines
        wordWrapEnabled
        hoverStateEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onInitNewRow={e => {
          e.data = { ...defaultRow, id: -getRandomNumber() };
        }}
        repaintChangesOnly
        onRowInserted={e => {
          const allRows = getValues('reportAnnex3aOverviews') || [];
          setValue('reportAnnex3aOverviews', [...allRows, e.data], { shouldDirty: true });
          calculateForm?.();
        }}
        onRowUpdated={e => {
          const allRows = getValues('reportAnnex3aOverviews') || [];
          const index = allRows.findIndex(item => item.id === e.key);
          if (index > -1) {
            allRows[index] = { ...allRows[index], ...e.data };
            setValue('reportAnnex3aOverviews', allRows, { shouldDirty: true });
            calculateForm?.();
          }
        }}
        onRowRemoved={e => {
          const allRows = getValues('reportAnnex3aOverviews') || [];
          const newRows = allRows.filter(item => item.id !== e.key);
          setValue('reportAnnex3aOverviews', newRows, { shouldDirty: true });
          calculateForm?.();
        }}
        onEditorPreparing={e => {
          if (e.parentType !== 'dataRow') {
            return;
          }

          switch (e.dataField) {
            case 'contractValue':
            case 'advancePaymentValueForThePeriod':
            case 'advancePaymentExecution':
            case 'completedVolumeThisPeriod':
            case 'advancePaymentRecovery':
            case 'valuePaymentForCompletedVolume':
            case 'valueAdvancePayment':
            case 'cumulativePaymentForCompletedVolume':
            case 'cumulativeAdvancePayment':
              e.editorOptions.onFocusIn = selectTextOnFocus;
              break;
          }
        }}
        onCellPrepared={onCellPrepared}
      >
        <Editing
          mode="cell"
          allowUpdating={role?.isCreate || role?.isUpdate}
          allowDeleting={role?.isCreate || role?.isUpdate}
          allowAdding={role?.isCreate || role?.isUpdate}
          confirmDelete={false}
          useIcons
          newRowPosition="last"
        />
        <ColumnChooser enabled mode="select" height="45rem">
          <ColumnChooserSearch enabled />
          <ColumnChooserSelection allowSelectAll selectByClick recursive />
        </ColumnChooser>
        <Scrolling mode="standard" rowRenderingMode="standard" />
        <Paging enabled defaultPageSize={10} />
        <Pager
          visible
          showInfo
          showNavigationButtons
          showPageSizeSelector
          displayMode="adaptive"
          allowedPageSizes={[5, 10, 50, 100]}
        />
        <Column
          dataField="serialNumber"
          caption="STT"
          dataType="number"
          format={',##0,##'}
          alignment="center"
          width={60}
          allowEditing={false}
          allowFiltering={false}
          allowSorting={false}
          fixedPosition="left"
          cellRender={(cellInfo: {
            column: dxDataGridColumn;
            columnIndex: number;
            component: dxDataGrid;
            data: Record<string, any>;
            displayValue: any;
            oldValue: any;
            row: dxDataGridRowObject;
            rowIndex: number;
            rowType: string;
            text: string;
            value: any;
            watch: () => void;
          }) => {
            if (cellInfo.rowType === 'data') {
              const pageIndex = cellInfo.component.pageIndex();
              const pageSize = cellInfo.component.pageSize();
              const visibleRowIndex = cellInfo.component
                .getVisibleRows()
                .filter(item => item.rowType === 'data')
                .indexOf(cellInfo.row);
              return pageIndex * pageSize + visibleRowIndex + 1;
            }
          }}
        />
        {/* Nhà thầu */}
        <Column
          dataField={'contractorId'}
          caption={t('fields.reportAnnex3aOverviews.contractorId')}
          lookup={{ dataSource: contractors, displayExpr: 'name', valueExpr: 'id' }}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            console.log('cell', cell);
            const onValueChanged = (e: SelectBoxTypes.ValueChangedEvent) => {
              // eslint-disable-next-line @typescript-eslint/no-unsafe-call
              cell.setValue(e);
            };
            return (
              <FormCombobox<Contractor>
                defaultValue={cell.value}
                value={cell.value}
                model="contractor"
                queryKey={[QUERIES.CONTRACTOR]}
                defaultText={cell.displayValue}
                className={cn('rounded-none border-none disabled:opacity-100')}
                onChange={onValueChanged}
                isWrap={true}
                isTable
              />
            );
          }}
        >
          <RequiredRule />
        </Column>
        {/* 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng) */}
        <Column
          dataField={'contractValue'}
          caption={t('fields.reportAnnex3aOverviews.contractValue')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={NumberBox}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                onChange={value => handleCellValueChange(cell, value as number)}
              />
            );
          }}
        />
        {/* 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước */}
        <Column
          dataField={'advancePaymentValueForThePeriod'}
          caption={t('fields.reportAnnex3aOverviews.advancePaymentValueForThePeriod')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={NumberBox}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước */}
        <Column
          dataField={'advancePaymentExecution'}
          caption={t('fields.reportAnnex3aOverviews.advancePaymentExecution')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                onChange={value => handleCellValueChange(cell, value as number)}
              />
            );
          }}
        />
        {/* 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này */}
        <Column
          dataField={'completedVolumeThisPeriod'}
          caption={t('fields.reportAnnex3aOverviews.completedVolumeThisPeriod')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 5. Thanh toán để thu hồi tạm ứng */}
        <Column
          dataField={'advancePaymentRecovery'}
          caption={t('fields.reportAnnex3aOverviews.advancePaymentRecovery')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành */}
        <Column
          dataField={'valuePaymentForCompletedVolume'}
          caption={t('fields.reportAnnex3aOverviews.valuePaymentForCompletedVolume')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng */}
        <Column
          dataField={'valueAdvancePayment'}
          caption={t('fields.reportAnnex3aOverviews.valueAdvancePayment')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành */}
        <Column
          dataField={'cumulativePaymentForCompletedVolume'}
          caption={t('fields.reportAnnex3aOverviews.cumulativePaymentForCompletedVolume')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng */}
        <Column
          dataField={'cumulativeAdvancePayment'}
          caption={t('fields.reportAnnex3aOverviews.cumulativeAdvancePayment')}
          customizeText={customizeNumberCell(DEFAULT_DECIMAL)}
          editCellComponent={InputNumber}
          editCellRender={(cell: DataGridTypes.ColumnEditCellTemplateData) => {
            return (
              <InputNumber
                defaultValue={cell?.value}
                value={cell?.value}
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
                onChange={value => cell.setValue(value)}
              />
            );
          }}
        />
        {/* Toolbar */}
        <Toolbar>
          {errors.reportAnnex3aOverviews?.message && (
            <ErrorMessage message={errors.reportAnnex3aOverviews?.message} />
          )}
          <Item location="before">
            <Button
              icon="search"
              type="default"
              onClick={() => {
                if (!acceptance) {
                  notification.warning(t('page.notification.warning.completionAcceptanceId'));
                  return;
                }
                getRequest<GetReportAnnex3aPaymentReceipt[]>(
                  `/${model}/get-report-annex-3-a-payment-receipt/${Number(acceptance)}`
                )
                  .then(response => {
                    if (!response) return;

                    // if (!editableData?.length) {
                    const details = reportAnnex3aDetails;
                    const groupedData = details.reduce<
                      Record<
                        string,
                        ReportAnnex3aDetail & Record<string, number | string | null | undefined>
                      >
                    >((acc, item) => {
                      const key = item.constructionTaskCode!;

                      if (!acc[key]) {
                        acc[key] = {
                          contractedOrEstimatedPaymentPrice: 0,
                          contractedorestimatedquantity: 0,
                          cumulativePreviousPeriod: 0,
                          currentPeriodExecution: 0,
                          contractedOrEstimatedQuantity: 0,
                          id: -getRandomNumber(),
                          reportAnnex3aId: 0,
                          note: '',
                          contractorId: item.contractorId,
                          constructionTaskId: item.constructionTaskId,
                          constructionTaskCode: item.constructionTaskCode,
                          constructionTaskName: item.constructionTaskName,
                          unitId: item.unitId,
                          unitName: item.unitName,
                        };
                      }

                      // Thêm các trường động theo contractorId
                      const contractorKey = item.contractorId;
                      acc[key][`contractedOrEstimatedPaymentPrice`] =
                        item.contractedOrEstimatedPaymentPrice;
                      acc[key][`contractedOrEstimatedQuantity_${contractorKey}`] =
                        item.contractedOrEstimatedQuantity;
                      acc[key][`currentPeriodExecution_${contractorKey}`] =
                        item.currentPeriodExecution;
                      acc[key][`cumulativePreviousPeriod_${contractorKey}`] =
                        item.cumulativePreviousPeriod;
                      acc[key][`accumulatedUpToTheEndOfThisPeriod_${contractorKey}`] =
                        Number(item.currentPeriodExecution) + Number(item.cumulativePreviousPeriod);

                      acc[key][`contractedOrEstimatedQuantityTotalAmount_${contractorKey}`] =
                        Number(item.contractedOrEstimatedQuantity) *
                        Number(item.contractedOrEstimatedPaymentPrice);
                      acc[key][`currentPeriodExecutionTotalAmount_${contractorKey}`] =
                        Number(item.currentPeriodExecution) *
                        Number(item.contractedOrEstimatedPaymentPrice);
                      acc[key][`cumulativePreviousPeriodTotalAmount_${contractorKey}`] =
                        Number(item.cumulativePreviousPeriod) *
                        Number(item.contractedOrEstimatedPaymentPrice);
                      acc[key][`accumulatedUpToTheEndOfThisPeriodTotalAmount_${contractorKey}`] =
                        (Number(item.currentPeriodExecution) +
                          Number(item.cumulativePreviousPeriod)) *
                        Number(item.contractedOrEstimatedPaymentPrice);

                      return acc;
                    }, {});
                    /// chỗ này có sửa lại để cho phép nhập phiếu
                    ///start fix bug
                    const dataTem = Object.values(groupedData)?.flatMap(item => {
                      return Object.keys(item)
                        .filter(key => key.startsWith('contractedOrEstimatedQuantity_'))
                        .map(key => {
                          const contractorId = key.split('_')[1]; // Lấy ID từ key
                          return {
                            unitId: item.unitId,
                            unitName: item.unitName,
                            contractorId: parseInt(contractorId), // Chuyển về số
                            contractedOrEstimatedPaymentPrice:
                              item[`contractedOrEstimatedPaymentPrice`],
                            contractedOrEstimatedQuantity: Number(
                              item[`contractedOrEstimatedQuantity_${contractorId}`]
                            ),
                            currentPeriodExecution: Number(
                              item[`currentPeriodExecution_${contractorId}`]
                            ),
                            cumulativePreviousPeriod:
                              item[`cumulativePreviousPeriod_${contractorId}`],
                            contractedOrEstimatedQuantityTotalAmount: Number(
                              item[`contractedOrEstimatedQuantityTotalAmount_${contractorId}`]
                            ),
                            currentPeriodExecutionTotalAmount: Number(
                              Number(item[`contractedOrEstimatedPaymentPrice`]) *
                                Number(item[`currentPeriodExecution_${contractorId}`])
                            ),
                            cumulativePreviousPeriodTotalAmount: Number(
                              item[`cumulativePreviousPeriodTotalAmount_${contractorId}`]
                            ),
                          };
                        });
                    });

                    const totals: Record<number, Record<string, number>> = {};

                    dataTem?.forEach(entry => {
                      const {
                        contractorId,
                        contractedOrEstimatedQuantityTotalAmount,
                        currentPeriodExecutionTotalAmount,
                      } = entry;
                      if (!totals[contractorId]) {
                        totals[contractorId] = {
                          id: 0,
                          reportAnnex3aId: 0,
                          contractorId,
                          contractValue: 0, // 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng)
                          advancePaymentValueForThePeriod: 0, // 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước
                          advancePaymentExecution: 0, // 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước
                          completedVolumeThisPeriod: 0, // 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này
                          advancePaymentRecovery: 0, // 5. Thanh toán để thu hồi tạm ứng
                          valuePaymentForCompletedVolume: 0, // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành
                          valueAdvancePayment: 0, // 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng
                          cumulativePaymentForCompletedVolume: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành
                          cumulativeAdvancePayment: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng
                        };
                      }
                      totals[contractorId].contractValue +=
                        contractedOrEstimatedQuantityTotalAmount;
                      totals[contractorId].completedVolumeThisPeriod +=
                        currentPeriodExecutionTotalAmount;
                    });
                    // Bước 3: Chuyển kết quả về dạng mảng
                    const resultOverviews = Object.values(totals);
                    const temEditableData = resultOverviews.map(item => {
                      const objectPayment = response.filter(
                        a => a.contractorId == item.contractorId
                      )[0];
                      const completedVolumeThisPeriod = convertMoneny(
                        item.completedVolumeThisPeriod ?? 0
                      );
                      const advancePaymentValueForThePeriod = Number.isNaN(objectPayment?.advance)
                        ? 0
                        : convertMoneny(objectPayment?.advance ?? 0);
                      const advancePaymentExecution = Number.isNaN(objectPayment?.payment)
                        ? 0
                        : convertMoneny(objectPayment?.payment ?? 0);

                      const _advancePaymentRecovery = convertMoneny(
                        (item.completedVolumeThisPeriod / item.contractValue) *
                          (objectPayment.advanceBase ?? 0) *
                          0.8
                      ); /// 5. Thanh toán để thu hồi tạm ứng

                      // const [valueAdvancePayment] = getSumsFromArray(editableData, ['contractValue']);
                      const _valuePaymentForCompletedVolume = convertMoneny(
                        Number(item.completedVolumeThisPeriod) * 0.95 - _advancePaymentRecovery
                      ); // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành

                      const _cumulativePaymentForCompletedVolume = convertMoneny(
                        Number(advancePaymentExecution) +
                          Number(_advancePaymentRecovery ?? 0) +
                          Number(_valuePaymentForCompletedVolume ?? 0)
                      );
                      const _cumulativeAdvancePayment = convertMoneny(
                        Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery < 0
                          ? 0
                          : Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery
                      );
                      return {
                        ...item,
                        id: -getRandomNumber(),
                        contractorId: item.contractorId,
                        reportAnnex3aId: item.reportAnnex3aId,
                        contractValue: convertMoneny(item.contractValue),
                        completedVolumeThisPeriod: completedVolumeThisPeriod,
                        valueAdvancePayment: 0,
                        advancePaymentValueForThePeriod: Number.isNaN(
                          advancePaymentValueForThePeriod
                        )
                          ? 0
                          : advancePaymentValueForThePeriod,
                        advancePaymentExecution: Number.isNaN(advancePaymentExecution)
                          ? 0
                          : advancePaymentExecution,
                        advancePaymentRecovery: Number.isNaN(_advancePaymentRecovery)
                          ? 0
                          : _advancePaymentRecovery,
                        valuePaymentForCompletedVolume: Number.isNaN(
                          _valuePaymentForCompletedVolume
                        )
                          ? 0
                          : _valuePaymentForCompletedVolume,
                        cumulativePaymentForCompletedVolume: Number.isNaN(
                          _cumulativePaymentForCompletedVolume
                        )
                          ? 0
                          : _cumulativePaymentForCompletedVolume,
                        cumulativeAdvancePayment: Number.isNaN(_cumulativeAdvancePayment)
                          ? 0
                          : _cumulativeAdvancePayment,
                      };
                    });

                    const [
                      contractValue,
                      advancePaymentValueForThePeriod,
                      advancePaymentExecution,
                      completedVolumeThisPeriod,
                      advancePaymentRecovery,
                      valuePaymentForCompletedVolume,
                      valueAdvancePayment,
                      cumulativePaymentForCompletedVolume,
                      cumulativeAdvancePayment,
                    ] = getSumsFromArray(temEditableData, [
                      'contractValue',
                      'advancePaymentValueForThePeriod',
                      'advancePaymentExecution',
                      'completedVolumeThisPeriod',
                      'advancePaymentRecovery',
                      'valuePaymentForCompletedVolume',
                      'valueAdvancePayment',
                      'cumulativePaymentForCompletedVolume',
                      'cumulativeAdvancePayment',
                    ]);

                    setValue('reportAnnex3aOverviews', temEditableData);

                    setValue('sumContractValue', contractValue);
                    setValue('sumAdvancePaymentValueForThePeriod', advancePaymentValueForThePeriod);
                    setValue('sumAdvancePaymentExecution', advancePaymentExecution);
                    setValue('sumCompletedVolumeThisPeriod', completedVolumeThisPeriod);
                    setValue('sumAdvancePaymentRecovery', advancePaymentRecovery);
                    setValue(
                      'sumDisbursementRequestValue',
                      valuePaymentForCompletedVolume + valueAdvancePayment
                    );
                    setValue(
                      'sumCumulativeDisbursedValue',
                      cumulativePaymentForCompletedVolume + cumulativeAdvancePayment
                    );
                    // }
                  })
                  .catch(error => {
                    console.error('error:', error);
                  });
              }}
              text={getDataLabel}
            />
          </Item>
          <Item location="after" name="addRowButton" />
          <Item location="after" name="columnChooserButton" />
        </Toolbar>
      </DataGrid>
    </div>
  );
};
