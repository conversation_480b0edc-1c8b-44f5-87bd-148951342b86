import { MONEY_FORMAT, PERCENT_FORMAT_WITHOUT_SUFFIX, TABLES } from '@/constant';
import { IUserPermission, MonthlyPlannedDisbursementDetail } from '@/types';
import { useFormContext } from 'react-hook-form';

import { useAuth } from '@/hooks';
import { createPostMutateCustomPathFn } from '@/services';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { Column } from 'devextreme/ui/data_grid';
import { DevexDataGrid } from '@/components/devex-data-grid';

type MonthlyPlannedDisbursementEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  budgetYearValue?: number | null | undefined;
};

const successAlert = createMutationSuccessFn('monthlyPlannedDisbursement');
const moneyFormat = MONEY_FORMAT;
const percentFormat = PERCENT_FORMAT_WITHOUT_SUFFIX;
const moneyOptions = {
  format: moneyFormat,
  showSpinButtons: false,
};
const percentOptions = {
  format: percentFormat,
  showSpinButtons: false,
};
export const MonthlyPlannedDisbursementEditableTableDevextreme = ({
  role,
  budgetYearValue,
}: MonthlyPlannedDisbursementEditableTableProps) => {
  const { watch } = useFormContext<{ items: MonthlyPlannedDisbursementDetail[] }>();
  const { projects } = useAuth();

  const editableData = watch('items');

  const { t: translator } = useTranslation('monthlyPlannedDisbursement');
  const t = useCallback(
    (fieldName: string) =>
      translator(fieldName, {
        budgetYear: budgetYearValue ? `${budgetYearValue}` : '',
        nextBudgetYear: budgetYearValue ? `${budgetYearValue + 1}` : '',
      }),
    [budgetYearValue, translator]
  );

  const monthlyPlannedDisbursementEditableColumns: Column[] = useMemo(
    (): Column[] => [
      {
        cellTemplate: (container, options) => {
          const pageIndex = options.component.pageIndex();
          const pageSize = options.component.pageSize();
          const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
          container.textContent = serialNumber.toString();
        },
        caption: 'STT',
        fixed: true,
        fixedPosition: 'left',
        alignment: 'center',
        width: 50,
        dataField: 'serialNumber',
        allowSorting: false,
        allowFiltering: false,
        allowEditing: false,
        format: ',##0,##',
      },
      {
        dataField: 'projectId', // Dự án
        caption: t('fields.monthlyPlannedDisbursementDetails.projectId'),
        lookup: { dataSource: projects, valueExpr: 'id', displayExpr: 'name' },
        allowEditing: false,
        fixedPosition: 'left',
        fixed: true,
      },

      {
        dataField: 'assignedPlanForBudgetYear', // Kế hoạch năm 2025 đã giao
        caption: t('fields.monthlyPlannedDisbursementDetails.assignedPlanForBudgetYear'),
        dataType: 'number',
        format: moneyFormat,
        allowEditing: false,
      },

      {
        dataField: 'plannedCumulativeFullYear', // Dự kiến giải ngân cả năm 2025 (từ 01/01/2025 - 31/01/2026)
        caption: t('fields.monthlyPlannedDisbursementDetails.plannedCumulativeFullYear'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'plannedCumulativeFullYearFinance', // Dự kiến giải ngân cả năm 2025 (từ 01/01/2025 - 31/01/2026) UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.plannedCumulativeFullYearFinance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month01', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month01'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month01Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month01Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month02', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 02/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month02'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month02Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 02/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month02Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month03', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 03/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month03'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month03Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 03/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month03Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'disbursementRate01', // Tỷ lệ giải ngân hết Quý I/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate01'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'disbursementRate01Finance', // Tỷ lệ giải ngân hết Quý I/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate01Finance'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'month04', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 04/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month04'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month04Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 04/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month04Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month05', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 05/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month05'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month05Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 05/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month05Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month06', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 06/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month06'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month06Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 06/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month06Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'disbursementRate02', // Tỷ lệ giải ngân hết Quý II/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate02'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'disbursementRate02Finance', // Tỷ lệ giải ngân hết Quý II/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate02Finance'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'month07', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 07/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month07'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month07Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 07/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month07Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month08', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 08/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month08'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month08Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 08/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month08Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month09', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 09/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month09'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month09Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 09/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month09Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'disbursementRate03', // Tỷ lệ giải ngân hết Quý III/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate03'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'disbursementRate03Finance', // Tỷ lệ giải ngân hết Quý III/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.disbursementRate03Finance'),
        dataType: 'number',
        format: percentFormat,
        editorOptions: percentOptions,
      },

      {
        dataField: 'month10', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month10'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month10Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month10Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month11', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month11'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month11Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month11Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month12', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025
        caption: t('fields.monthlyPlannedDisbursementDetails.month12'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },

      {
        dataField: 'month12Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
        caption: t('fields.monthlyPlannedDisbursementDetails.month12Finance'),
        dataType: 'number',
        format: moneyFormat,
        editorOptions: moneyOptions,
      },
      {
        width: 50,
        type: 'buttons',
        buttons: [
          {
            icon: 'save',
            onClick: e => {
              const row = e.row?.data as MonthlyPlannedDisbursementDetail;
              createPostMutateCustomPathFn<MonthlyPlannedDisbursementDetail>(
                `monthly-planned-disbursement/update-detail?id=${row.id}`
              )({
                ...row,
              })
                .then(() => {
                  successAlert(1);
                })
                .catch(error => {
                  console.error('Error fetching data:', error);
                });
            },
          },
        ],
      },
    ],
    [projects, t]
  );

  return (
    <div>
      <DevexDataGrid
        id={TABLES.MONTHLY_PLANNED_DISBURSEMENT_DETAIL}
        dataSource={editableData}
        columns={monthlyPlannedDisbursementEditableColumns}
        editing={{
          mode: 'cell',
          allowUpdating: role?.isUpdate,
          allowDeleting: false,
          allowAdding: false,
          useIcons: true,
        }}
        hideSerialNumber
      />
    </div>
  );
};
