/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { BasicDialog } from '@/components/basic-dialog';
import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { MUTATE, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { snakeCase } from 'lodash';
import { removeAccents } from '@/lib/text';
import { BoardOfDirectorForm } from './board-of-director-form';
import { BoardOfDirector } from '@/types/board-of-director';

//export excel
const t = translationWithNamespace('boardOfDirector');

const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const BoardOfDirectorPage = () => {
  //lấy quyền
  const role = usePermission(PERMISSIONS.BOARD_OF_DIRECTOR);
  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.BOARD_OF_DIRECTOR],
    queryFn: () =>
      createQueryPaginationFn('board-of-director')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
      }),
    // select: data => {
    //   return { ...data, items: data.items.map(item => ({ ...item, isInactive: true })) };
    // },
  });

  const { items } = data || {};

  const getTargetAlias = (target: BoardOfDirector | undefined) => {
    if (!target) {
      return '';
    }
    return `${target?.name}`;
  };

  const {
    // state
    selectedTarget,
    // setSelectedTarget,

    // delete feature
    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    // edit feature
    isEditDialogOpen,
    toggleEditDialog,
    selectTargetToEdit,
  } = useDataTable<BoardOfDirector, PeriodFilter>({
    getTargetAlias,
    deleteFn: createDeleteMutateFn<BoardOfDirector>('board-of-director'),
    deleteKey: [MUTATE.DELETE_BOARD_OF_DIRECTOR],
    invalidateKey: [QUERIES.BOARD_OF_DIRECTOR],
  });

  const selectedId = selectedTarget?.id || 0;
  const onEditClick = (e: ColumnButtonClickEvent<BoardOfDirector>) => {
    if (e.row?.data) {
      selectTargetToEdit(e.row.data);
    }
  };

  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      selectTargetToEdit(e.data as BoardOfDirector);
    }
  };

  const onAddClick = () => {
    selectTargetToEdit({ id: 0 } as BoardOfDirector);
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<BoardOfDirector>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  return (
    <PageLayout header={t('page.header')}>
      <DevexDataGrid
        id={TABLES.BOARD_OF_DIRECTOR}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => callbackWithTimeout(refetch)}
        onExporting={onExporting}
        hoverStateEnabled
        focusedRowEnabled={true}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />

        {/* thao tác */}
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>

        {/* Trường dữ liệu */}
        <Column dataField="code" caption={t('fields.code')} />
        <Column dataField="name" caption={t('fields.name')} />

        <Column dataField="departmentHeadId" caption={t('fields.departmentHeadId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="departmentDeputyId" caption={t('fields.departmentDeputyId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="departmentDeputyTwoId" caption={t('fields.departmentDeputyTwoId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="departmentDeputyThreeId" caption={t('fields.departmentDeputyThreeId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>

        <Column dataField="note" caption={t('fields.note')} />
      </DevexDataGrid>
      <BasicDialog
        className="w-full md:w-auto"
        open={isEditDialogOpen}
        toggle={toggleEditDialog}
        title={selectedId ? t('page.form.edit') : t('page.form.addNew')}
      >
        <BoardOfDirectorForm role={role} editId={selectedId} />
      </BasicDialog>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        onConfirm={deleteTarget}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        model="boardOfDirector"
        name={getTargetAlias(selectedTarget)}
      />
    </PageLayout>
  );
};
