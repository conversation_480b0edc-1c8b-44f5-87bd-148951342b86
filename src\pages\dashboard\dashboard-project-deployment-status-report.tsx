/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { ProjectDeploymentStatusReport } from '@/types';
import Chart, {
  ArgumentAxis,
  ValueAxis,
  CommonSeriesSettings,
  Series,
  Legend,
  Title,
} from 'devextreme-react/chart';
import { RowLabel } from './row-label';
import { Link } from 'react-router-dom';

export const DashboardProjectDeploymentStatusReport = ({
  items,
}: {
  items: ProjectDeploymentStatusReport[];
}) => {
  return (
    <div className="bg-white p-4">
      <div className="mb-2 text-xl font-bold">
        <Link to="/summary-report/project-deployment-status-report" className="!text-black">
          Tình trạng triển khai theo nguồn ngân sách
        </Link>
      </div>
      <Chart dataSource={items} className="bg-white">
        <CommonSeriesSettings argumentField="budgetFundName" type="bar" />

        <Series valueField="totalProjectsOneStep" name="Dự án 1 bước">
          <RowLabel />
        </Series>
        <Series valueField="totalProjectsTwoSteps" name="Dự án 2 bước">
          <RowLabel />
        </Series>

        <ArgumentAxis
          title={{ text: 'Nguồn ngân sách', font: { weight: 'bold' } }}
          label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
        />
        <ValueAxis
          title={{ text: 'Số lượng dự án', font: { weight: 'bold' } }}
          label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
        />

        <Legend verticalAlignment="bottom" horizontalAlignment="center" font={{ weight: 800 }} />
        <Title text=""></Title>
      </Chart>
    </div>
  );
};
