import { NumberBox } from 'devextreme-react';
import { ColumnCellTemplateData } from 'devextreme/ui/data_grid';

export const VatTaxCell = ({
  options,
  costItemTypesIsEditVat,
}: {
  options: ColumnCellTemplateData<any>;
  costItemTypesIsEditVat: number[];
}) => {
  let isEditVat = false;
  if (options.data.costItemCostItemTypeId) {
    isEditVat = costItemTypesIsEditVat.includes(options.data.costItemCostItemTypeId as number);
  }
  return (
    <NumberBox
      value={options.data.vatTax}
      readOnly={!isEditVat && false}
      onValueChanged={e => {
        options.component.cellValue(options.rowIndex, 'vatTax', e.value);
      }}
    />
  );
};
