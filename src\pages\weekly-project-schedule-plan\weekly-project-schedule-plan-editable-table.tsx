import { ErrorMessage } from '@/components/ui/error-message';
import { TABLES } from '@/constant';
import { IUserPermission, WeeklyProjectSchedulePlanDetail } from '@/types';
import { useFormContext } from 'react-hook-form';
import { memo, useMemo } from 'react';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import { DataTable, EditableTextArea } from '@/components/data-table';
import { createMutationSuccessFn, translationWithNamespace } from '@/lib/i18nUtils';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import 'dayjs/locale/vi';
import { Combobox } from '@/components/combobox';
import { useAuth } from '@/hooks';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';
import { createPostMutateCustomPathFn } from '@/services';

dayjs.extend(weekOfYear);
dayjs.extend(localizedFormat);
dayjs.locale('vi');

type WeeklyProjectSchedulePlanEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  budgetYearValue?: number | null | undefined;
};

const t = translationWithNamespace('weeklyProjectSchedulePlan');
const onWeeklyProjectSchedulePlanMutationSuccess = createMutationSuccessFn(
  'weeklyProjectSchedulePlan'
);
export const WeeklyProjectSchedulePlanEditableTable = memo(
  ({ role, calculateForm, budgetYearValue }: WeeklyProjectSchedulePlanEditableTableProps) => {
    const {
      reset,
      watch,
      formState: { errors },
    } = useFormContext<{ items: WeeklyProjectSchedulePlanDetail[] }>();
    const { projects } = useAuth();

    const editableData = watch('items');

    const createWeekColumns = (
      weekIndex: number,
      year: number
    ): ColumnDef<WeeklyProjectSchedulePlanDetail>[] => {
      const weekNum = String(weekIndex).padStart(2, '0');
      const weekKey = `week${weekNum}`;
      const landKey = `landWeek${weekNum}`;

      const startDate = dayjs().year(year).week(weekIndex).day(1); // day(1) là thứ Hai
      const endDate = dayjs().year(year).week(weekIndex).day(6); // day(6) là thứ Bảy
      const displayStartDate =
        startDate.year() === year ? startDate : dayjs().year(year).startOf('year');
      const displayEndDate = endDate.year() === year ? endDate : dayjs().year(year).endOf('year');

      const dateRange = `(${displayStartDate.format('DD/MM')}-${displayEndDate.format('DD/MM')})`;

      return [
        {
          id: weekKey,
          accessorKey: weekKey,
          header: `${t('fields.weeklyProjectSchedulePlanDetailFields.weekHeader')} ${weekNum} ${dateRange}`,
          cell: (props: CellContext<WeeklyProjectSchedulePlanDetail, unknown>) => (
            <EditableTextArea {...props} />
          ),
          size: 400,
        },
        {
          id: landKey,
          accessorKey: landKey,
          header: `${t('fields.weeklyProjectSchedulePlanDetailFields.landWeekHeader')} ${weekNum} ${dateRange}`,
          cell: (props: CellContext<WeeklyProjectSchedulePlanDetail, unknown>) => (
            <EditableTextArea {...props} />
          ),
          size: 400,
        },
      ];
    };
    // Move otherColumns inside useMemo to avoid recreating it on every render
    const { tableColumns, initialColumnVisibility } = useMemo(() => {
      const otherColumns = [
        {
          id: 'projectId',
          accessorKey: 'projectId',
          header: t('fields.weeklyProjectSchedulePlanDetailFields.projectId'),
          cell: (props: CellContext<WeeklyProjectSchedulePlanDetail, unknown>) => (
            <Combobox
              // disabled
              showFields={['name']}
              options={projects}
              isTable={true}
              className="rounded-none border-none disabled:opacity-100"
              value={props.getValue<number>()}
              onChange={value => {
                props.table.options.meta?.updateData(props.row.index, props.column.id, value);
              }}
              disabled
            />
          ),
          size: 250,
        },
        {
          id: 'issuesAndRecommendations',
          accessorKey: 'issuesAndRecommendations',
          header: () => {
            const headerText = t(
              'fields.weeklyProjectSchedulePlanDetailFields.issuesAndRecommendations'
            );
            const tooltipText = t('fields.tooltips.issuesAndRecommendationsTooltip');
            return <span title={tooltipText}>{headerText}</span>;
          },
          cell: (props: CellContext<WeeklyProjectSchedulePlanDetail, unknown>) => (
            <EditableTextArea {...props} />
          ),
          size: 400,
        },
      ];
      const calculationYear = budgetYearValue;

      const allWeekColumns = Array.from({ length: 52 }, (_, i) =>
        createWeekColumns(i + 1, calculationYear!)
      ).flat();

      const currentWeek = dayjs().week();
      let startWeek: number;
      let endWeek: number;

      if (currentWeek === 1) {
        startWeek = 1;
        endWeek = 3;
      } else if (currentWeek === 52) {
        startWeek = 50;
        endWeek = 52;
      } else {
        startWeek = currentWeek - 1;
        endWeek = currentWeek + 1;
      }

      const getWeekNumberFromId = (id: string | undefined): number | null => {
        if (!id) return null;
        const match = id.match(/(?:week|landWeek)(\d{2})$/);
        return match ? parseInt(match[1], 10) : null;
      };

      const visibilityState: Record<string, boolean> = {};
      allWeekColumns.forEach(col => {
        if (col.id) {
          const weekNum = getWeekNumberFromId(col.id);
          const isVisibleByDefault = weekNum !== null && weekNum >= startWeek && weekNum <= endWeek;
          visibilityState[col.id] = isVisibleByDefault;
        }
      });
      visibilityState['removeRow'] = true;

      const saveRowColumn: ColumnDef<WeeklyProjectSchedulePlanDetail> = {
        id: 'removeRow',
        header: '',
        size: 20,
        enablePinning: true,
        cell: (props: CellContext<WeeklyProjectSchedulePlanDetail, unknown>) => {
          return (
            <Button
              type="button"
              size="icon"
              variant={'ghost'}
              className="h-4 w-4 min-w-4 p-0 text-primary-600 transition-all hover:text-primary-600"
              onClick={() => {
                const row = props.row.original;
                createPostMutateCustomPathFn<WeeklyProjectSchedulePlanDetail>(
                  `weekly-project-schedule-plan/update-detail?id=${row.id}`
                )({
                  ...row,
                })
                  .then(() => {
                    console.log('updated');
                    onWeeklyProjectSchedulePlanMutationSuccess(1);
                  })
                  .catch(error => {
                    console.error('Error fetching data:', error);
                  });
              }}
            >
              <Save size={16} />
            </Button>
          );
        },
      };

      return {
        tableColumns: [...otherColumns, ...allWeekColumns, saveRowColumn],
        initialColumnVisibility: visibilityState,
      };
    }, [budgetYearValue, projects]);

    return (
      <div>
        <DataTable
          tableId={TABLES.WEEKLY_PROJECT_SCHEDULE_PLAN_DETAIL}
          sortColumn="id"
          role={role}
          editableData={editableData}
          setEditableData={editedData => {
            reset({ items: editedData });
            calculateForm?.();
          }}
          // onAddButtonClick={table => {
          //   const newRow = { ...defaultRow, id: -getRandomNumber() };
          //   table.options.meta?.addNewRow(newRow);
          // }}
          syncQueryParams={false}
          columns={tableColumns}
          initialState={{
            columnVisibility: initialColumnVisibility,
          }}
          customToolbar={() => {
            let firstError;
            if (Array.isArray(errors)) {
              firstError = errors.find(detailError => detailError?.projectId?.message);
            }

            // Get the message from the found error, if any
            const errorMessage = firstError?.projectId?.message;

            return (
              <>
                {errorMessage && ( // Render only if an error message exists
                  <ErrorMessage message={errorMessage} />
                )}
              </>
            );
          }}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if role or calculateForm changes
    return prevProps.role === nextProps.role && prevProps.calculateForm === nextProps.calculateForm;
  }
);
