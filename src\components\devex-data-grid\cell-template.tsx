import React from 'react';
import { createRoot } from 'react-dom/client';
import { ColumnCellTemplateData } from 'devextreme/ui/data_grid';

// P là kiểu prop bổ sung mà bạn muốn tru<PERSON>ền thêm cho Component
export const createReactCellTemplate = <TData = any, P extends object = Record<string, never>>(
  Component: React.ComponentType<{ options: ColumnCellTemplateData<TData> } & P>,
  extraProps?: P
) => {
  return (container: HTMLElement, options: ColumnCellTemplateData<TData>) => {
    const root = createRoot(container);
    root.render(<Component options={options} {...(extraProps as P)} />);

    return () => {
      root.unmount();
    };
  };
};
