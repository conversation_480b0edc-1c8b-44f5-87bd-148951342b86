// import { <PERSON><PERSON> } from '@/components/ui/button';
import { BasicDialog } from '@/components/basic-dialog';
import { DialogFooter } from '@/components/ui/dialog';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import {
  COMPONENT,
  MUTATE,
  QUERIES,
  closeLabel,
  enterLabel,
  saveLabel,
  selectLabel,
} from '@/constant';
import { useAuth, useFormHandler } from '@/hooks';
import { useEntity } from '@/hooks/use-entity';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import { DocumentGroup, documentGroupSchema } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { But<PERSON>, TextBox } from 'devextreme-react';
import { useTranslation } from 'react-i18next';

const onDocumentGroupMutationSuccess = createMutationSuccessFn('documentGroup');
type CreateDirectoryFormProps = {
  editId: number;
  toggle: () => void;
  isEditDialogOpen: boolean;
  defaultValues?: DocumentGroup;
  onCreated?: () => void;
};
export const CreateDirectoryForm = ({
  editId,
  toggle,
  defaultValues,
  isEditDialogOpen,
  onCreated,
}: CreateDirectoryFormProps) => {
  const { t } = useTranslation('documentGroup');
  const toggleForm = () => {
    if (toggle) toggle();
  };

  const { mutateQueryItem } = useEntity<DocumentGroup>({
    queryKey: [QUERIES.DOCUMENT_GROUP],
    model: 'document-group',
  });

  const queryClient = useQueryClient();

  const { handleSubmit, methods } = useFormHandler<DocumentGroup>({
    queryKey: [MUTATE.DOCUMENT_GROUP, editId],
    mutateKey: [MUTATE.DOCUMENT_GROUP],
    invalidateKey: [QUERIES.DOCUMENT_GROUP],
    queryId: editId,
    readFn: createQueryByIdFn<DocumentGroup>('document-group'),
    createFn: createPostMutateFn<DocumentGroup>('document-group'),
    updateFn: createPutMutateFn<DocumentGroup>('document-group'),
    formatPayloadFn: values => {
      return values;
    },
    formatResponseFn: response => {
      mutateQueryItem(response);
      return response;
    },
    onCreateSuccess: (data, variables) => {
      mutateQueryItem({ ...variables, id: data });
      onDocumentGroupMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.DOCUMENT_GROUP, COMPONENT] });
      onCreated?.();
      toggleForm();
    },
    onUpdateSuccess: data => {
      onDocumentGroupMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.DOCUMENT_GROUP, COMPONENT] });
      onCreated?.();
      toggleForm();
    },
    formOptions: {
      resolver: zodResolver(documentGroupSchema),
      defaultValues: defaultValues,
      context: { schema: documentGroupSchema },
    },
  });
  const { projects } = useAuth();
  const { list: documentGroups } = useEntity<DocumentGroup>({
    queryKey: [QUERIES.DOCUMENT_GROUP],
    model: 'document-group',
  });
  const [id, projectId] = methods.watch(['id', 'projectId']);
  return (
    <BasicDialog
      className="w-full md:w-auto"
      open={isEditDialogOpen}
      toggle={toggle}
      title={editId ? t('page.form.rename') : t('page.form.addNew')}
    >
      <Form {...methods}>
        <form
          className="p-1"
          autoComplete="off"
          onSubmit={e => {
            methods.setValue(
              'code',
              `${methods.getValues('code')}/${removeAccents(methods.getValues('name').replace(' ', ''))}`
            );
            handleSubmit(e);
          }}
        >
          <div className="space-y-4">
            {/* projectId */}
            <div className="flex items-center">
              <FormLabel htmlFor="projectId" name="projectId" className="hidden w-[89px] md:block">
                {t('fields.projectId')}
              </FormLabel>
              <FormField
                label={t('fields.projectId')}
                name="projectId"
                id="projectId"
                className="min-w-0 flex-1 md:w-[500px]"
              >
                <FormCombobox
                  options={projects}
                  queryKey={[QUERIES.PROJECT]}
                  showFields={['name']}
                  placeholder={`${selectLabel} ${t('fields.projectId')}`}
                  onSelectItem={selectedItem => {
                    const documentGroup = documentGroups.find(
                      item => item.id !== id && item.projectId === selectedItem?.id
                    );
                    methods.setValue('parentId', documentGroup?.id);
                  }}
                />
              </FormField>
            </div>
            {/* parentId */}
            <div className="flex items-center">
              <FormLabel name="parentId" className="hidden w-[89px] md:block">
                {t('fields.parentId')}
              </FormLabel>
              <FormField
                isRequired
                label={t('fields.parentId')}
                name="parentId"
                className="min-w-0 flex-1 md:w-[500px]"
              >
                <FormCombobox
                  options={documentGroups}
                  queryKey={[QUERIES.DOCUMENT_GROUP]}
                  showFields={['name']}
                  placeholder={`${selectLabel} ${t('fields.parentId')}`}
                  filter={item => item.id !== id && item.projectId === projectId}
                />
              </FormField>
            </div>
            {/* name */}
            <div className="flex items-center">
              <FormLabel name="name" className="hidden w-[89px] md:block">
                {t('fields.name')}
              </FormLabel>
              <FormField
                isRequired
                label={t('fields.name')}
                name="name"
                className="min-w-0 flex-1 md:w-[500px]"
              >
                <TextBox placeholder={`${enterLabel} ${t('fields.name')}`} />
              </FormField>
            </div>
          </div>

          <DialogFooter className="mt-8 flex flex-row-reverse gap-x-2 bg-white py-1">
            <Button
              stylingMode="contained"
              text={saveLabel}
              icon="save"
              type="success"
              useSubmitBehavior
            />
            <Button
              stylingMode="outlined"
              text={closeLabel}
              icon="close"
              onClick={toggleForm}
              type="default"
            />
          </DialogFooter>
        </form>
      </Form>
    </BasicDialog>
  );
};
