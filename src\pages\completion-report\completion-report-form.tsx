import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { RecordEditableTable } from '@/components/records-attachment';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AGENCY_TYPE,
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import { useAuth, useFormHandler, useFormOperation, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { extractYMDFromDate, toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { getValidId, sanitizeObject } from '@/lib/utils';
import {
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
  createQueryPaginationFn,
} from '@/services';
import { Agency, Contract, Project, ProjectStatus } from '@/types';
import { RowSelectionState } from '@tanstack/react-table';
import { DateBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { CompletionReport, completionReportSchema, defaultValuesCompletionReport } from '@/types';
import { DocumentExportPreparation } from '@/components/document-export-preparation';
import { cloneDeep } from 'lodash';
import { useQuery } from '@tanstack/react-query';

const onCompletionReportMutationSuccess = createMutationSuccessFn('completionReport');
const departmentTypeDirector = 4;
export const CompletionReportForm = ({ projectStatus }: { projectStatus: ProjectStatus }) => {
  const { id: editId } = useParams();

  const { t } = useTranslation('completionReport');
  const role = usePermission(PERMISSIONS.COMPLETION_REPORT);
  const { user, projects } = useAuth();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const path = PATHS.COMPLETION_REPORT(projectStatus.code);
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesCompletionReport,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<CompletionReport>({
    queryKey: [MUTATE.COMPLETION_REPORT, editId],
    mutateKey: [MUTATE.COMPLETION_REPORT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.COMPLETION_REPORT],
    readFn: createQueryByIdFn<CompletionReport>('completion-report'),
    createFn: createPostMutateFn<CompletionReport>('completion-report'),
    updateFn: createPutMutateFn<CompletionReport>('completion-report'),
    formatPayloadFn: data => ({
      ...data,
      approvalDate: toLocaleDate(data.approvalDate!),
      completionReportTime: toLocaleDate(data.completionReportTime!),
      itemsRecordManagement: data.itemsRecordManagement
        .filter(item => item.content)
        .map(itemRecord => ({
          ...itemRecord,
          id: getValidId(itemRecord.id),
          dateCreate: toLocaleDate(itemRecord.dateCreate!),
          itemFile: itemRecord.itemFile
            .filter(file => file.fileName)
            .map(file => ({ ...file, id: getValidId(file.id) })),
        })),
    }),
    formatResponseFn: response => {
      const data = {
        ...response,
        approvalDate: toDateType(response.approvalDate!),

        completionReportTime: toDateType(response.completionReportTime!),
        itemsRecordManagement: response.itemsRecordManagement.map(i => ({
          ...i,
          dateCreate: toDateType(i.dateCreate!),
        })),
      };
      return data;
    },
    onCreateSuccess: data => {
      onCompletionReportMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onCompletionReportMutationSuccess,
    formOptions: {
      resolver: zodResolver(completionReportSchema),
      defaultValues,
    },
  });

  const [userCreatedName] = methods.watch(['userCreatedName']);

  const { reset, onTimeChange } = useFormOperation<CompletionReport>({
    model: 'completion-report',
    fieldTime: 'completionReportTime',
    createCodeKey: [QUERIES.COMPLETION_REPORT],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  const [projectId] = methods.watch(['projectId']);

  const { data: project } = useQuery({
    queryKey: [QUERIES.PROJECT, projectId],
    queryFn: () => createQueryByIdFn<Project>('project')(projectId),
    enabled: !!projectId,
  });
  const { data: contracts } = useQuery({
    queryKey: [QUERIES.CONTRACT],
    queryFn: () =>
      createQueryPaginationFn<Contract>('contract')({
        pageIndex: 1,
        pageSize: 1000,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [
          {
            column: 'ProjectId',
            expression: '=',
            keySearch: String(projectId),
          },
        ],
      }),
    enabled: !!projectId,
  });

  const getPrintDataSource = () => {
    const data = cloneDeep(methods.getValues());
    // cập nhật giá trị của data là '...' nếu giá trị đó là null
    sanitizeObject(data);
    const dot = '...';

    const tenderPackageContract = contracts?.items
      .map(
        contract =>
          `<span style='padding-left: 28.35pt;'>- ${contract.tenderPackageTenderPackageName || dot}: ${contract.contractorName || dot} </span><br/>`
      )
      .join('\n');
    const { date, month, year } = extractYMDFromDate(data?.approvalDate || null);
    const {
      date: startDate,
      month: startMonth,
      year: startYear,
    } = extractYMDFromDate(project?.constructionStartDate || null);
    const {
      date: endDate,
      month: endMonth,
      year: endYear,
    } = extractYMDFromDate(project?.constructionCompletionDate || null);
    const pmoDirectorName = project?.projectHumanResources.find(
      item => item.departmentType === departmentTypeDirector
    )?.memberName;

    return {
      ...data,
      constructionName: project?.name || dot,
      location: project?.wardName || dot,
      pmDirector: project?.projectManagementDirectorName || dot,
      phone: project?.contactPhone || dot,
      projectSize: project?.designCapacity || dot,
      constructionStartDate: project?.constructionStartDate || dot,
      constructionCompletionDate: project?.constructionCompletionDate || dot,
      pmoDirector: pmoDirectorName || dot,
      tenderPackageContract,
      date,
      month,
      year,
      startDate,
      startMonth,
      startYear,
      endDate,
      endMonth,
      endYear,
    };
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <DocumentExportPreparation
                  data={getPrintDataSource()}
                  profession={PROFESSIONS.COMPLETION_REPORT}
                  exportFileName={methods.getValues('projectOrSectionName') || ''}
                />
              </>
            }
          >
            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4 xl:max-w-screen-2xl">
              {/* Cột 1 */}
              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Dự án */}
                  <div className="flex items-center">
                    <FormLabel
                      name="projectId"
                      htmlFor="projectId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.projectId')}
                    </FormLabel>
                    <FormField
                      label={t('fields.projectId')}
                      id="projectId"
                      name="projectId"
                      isRequired
                      className="min-w-0 flex-1"
                    >
                      <FormCombobox<Project>
                        options={projects}
                        showFields={['name']}
                        placeholder={`${selectLabel} ${t('fields.projectId')}`}
                        key="id"
                        queryKey={[QUERIES.PROJECT]}
                        onSelectItem={item => {
                          methods.setValue('projectOrSectionName', item?.name);
                        }}
                      />
                    </FormField>
                  </div>
                  {/* Tên công trình (hoặc phần công trình) */}
                  <div className="flex items-center">
                    <FormLabel
                      name="projectOrSectionName"
                      htmlFor="projectOrSectionName"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.projectOrSectionName')}
                    </FormLabel>
                    <FormField
                      id="projectOrSectionName"
                      name="projectOrSectionName"
                      className="min-w-0 flex-1"
                      label={t('fields.projectOrSectionName')}
                    >
                      <TextArea
                        autoResizeEnabled={true}
                        placeholder={`${enterLabel} ${t('fields.projectOrSectionName')}`}
                      />
                    </FormField>
                  </div>
                  <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-2 xl:max-w-screen-2xl">
                    {/* Số quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalNumber"
                        htmlFor="approvalNumber"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalNumber')}
                      </FormLabel>
                      <FormField
                        id="approvalNumber"
                        name="approvalNumber"
                        className="min-w-0 flex-1"
                        label={t('fields.approvalNumber')}
                      >
                        <TextBox placeholder={`${enterLabel} ${t('fields.approvalNumber')}`} />
                      </FormField>
                    </div>
                    {/* Ngày quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalDate"
                        htmlFor="approvalDate"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalDate')}
                      </FormLabel>
                      <FormField
                        id="approvalDate"
                        name="approvalDate"
                        className="min-w-0 flex-1"
                        type="date"
                        label={t('fields.approvalDate')}
                      >
                        <DateBox
                          placeholder={`${selectLabel} ${t('fields.approvalDate')}`}
                          pickerType="calendar"
                          focusStateEnabled={false}
                        />
                      </FormField>
                    </div>
                  </div>
                  {/* Cơ quan */}
                  <div className="flex items-center">
                    <FormLabel
                      name="agencyId"
                      htmlFor="agencyId"
                      className="hidden w-[90px] leading-4 md:block"
                    >
                      {t('fields.agencyId')}
                    </FormLabel>
                    <FormField
                      id="agencyId"
                      name="agencyId"
                      className="min-w-0 flex-1"
                      label={t('fields.agencyId')}
                    >
                      <FormCombobox<Agency>
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.agencyId')}`}
                        model="agency"
                        queryKey={[QUERIES.AGENCY]}
                        filter={item => item.agencyType === AGENCY_TYPE.ISSUE}
                      />
                    </FormField>
                  </div>
                  {/* Nội dung quyết định */}
                  <div className="flex items-center">
                    <FormLabel
                      name="approvalContent"
                      htmlFor="approvalContent"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.approvalContent')}
                    </FormLabel>
                    <FormField
                      id="approvalContent"
                      name="approvalContent"
                      className="min-w-0 flex-1"
                      label={t('fields.approvalContent')}
                    >
                      <TextArea
                        autoResizeEnabled={true}
                        placeholder={`${enterLabel} ${t('fields.approvalContent')}`}
                      />
                    </FormField>
                  </div>

                  {/* Diên giải */}
                  <div className="flex items-center">
                    <FormLabel name="note" htmlFor="note" className="hidden w-[90px] md:block">
                      {t('fields.note')}
                    </FormLabel>
                    <FormField
                      id="note"
                      name="note"
                      className="min-w-0 flex-1"
                      label={t('fields.note')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Cột 3 */}
              <div className="col-span-1">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Ngày lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="completionReportTime"
                      htmlFor="completionReportTime"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.completionReportTime')}
                    </FormLabel>
                    <FormField
                      id="completionReportTime"
                      name="completionReportTime"
                      className="min-w-0 flex-1"
                      type="date"
                      onChange={e => {
                        onTimeChange(e.target.value);
                      }}
                      label={t('fields.completionReportTime')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.completionReportTime')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Mã phiếu */}
                  <div className="flex items-center">
                    <FormLabel name="code" htmlFor="code" className="hidden w-[90px] md:block">
                      {t('fields.code')}
                    </FormLabel>
                    <FormField
                      id="code"
                      name="code"
                      className="min-w-0 flex-1"
                      label={t('fields.code')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly={true} />
                    </FormField>
                  </div>

                  {/* Người lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="userCreatedId"
                      htmlFor="userCreatedId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.userCreatedId')}
                    </FormLabel>
                    <FormField
                      id="userCreatedId"
                      name="userCreatedId"
                      className="min-w-0 flex-1"
                      label={t('fields.userCreatedId')}
                    >
                      <FormCombobox
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                        model="user"
                        queryKey={[QUERIES.USERS]}
                        disabled
                      />
                    </FormField>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="attachment">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="attachment">{t('page.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="completion-report"
                    profession={PROFESSIONS.COMPLETION_REPORT}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
