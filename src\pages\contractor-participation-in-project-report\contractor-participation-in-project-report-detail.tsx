import { DevexDataGrid } from '@/components/devex-data-grid';
import { TABLES } from '@/constant';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout } from '@/lib/utils';
import { ContractorParticipationInProjectReport } from '@/types/contractor-participation-in-project-report';
import { Column, Editing, Export, Grouping, Pager, Paging } from 'devextreme-react/data-grid';
import { snakeCase } from 'lodash';

const t = translationWithNamespace('contractorParticipationInProjectReport');
const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

type Props = {
  items: ContractorParticipationInProjectReport[];
  refetch: () => Promise<unknown>;
};
export const ContractorParticipationInProjectReportDetailTable = ({ items, refetch }: Props) => {
  return (
    <DevexDataGrid
      id={TABLES.CONTRACTOR_PARTICIPATION_IN_PROJECT_REPORT}
      dataSource={items}
      onRefresh={() => {
        callbackWithTimeout(refetch);
      }}
      onExporting={onExporting}
      keyExpr={'ordinalNumber'}
      // stateStoring={{ enabled: false }}
    >
      <Export enabled={true} />
      <Editing allowUpdating={false} allowDeleting={false} useIcons />
      <Grouping autoExpandAll={true} />
      <Paging enabled defaultPageSize={10} />
      <Pager
        visible
        showInfo
        showNavigationButtons
        showPageSizeSelector
        displayMode="adaptive"
        allowedPageSizes={[5, 10, 50, 100]}
      />

      <Column dataField="projectCode" caption={t('fields.projectCode')} visible={false} />
      <Column dataField="projectName" caption={t('fields.projectName')} />

      <Column dataField="contractorCode" caption={t('fields.contractorCode')} />
      <Column dataField="contractorName" caption={t('fields.contractorName')} />

      <Column dataField="employeeNameKeyPersonnel" caption={t('fields.employeeNameKeyPersonnel')} />
      <Column dataField="projectStatusName" caption={t('fields.projectStatusName')} />
    </DevexDataGrid>
  );
};
