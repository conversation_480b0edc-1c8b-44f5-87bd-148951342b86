import { TABLES } from '@/constant';
import { IUserPermission, WeeklyProjectSchedulePlanDetail } from '@/types';
import { useFormContext } from 'react-hook-form';
import { memo, useMemo } from 'react';
import { createMutationSuccessFn, translationWithNamespace } from '@/lib/i18nUtils';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import 'dayjs/locale/vi';
import { useAuth } from '@/hooks';
import { createPostMutateCustomPathFn } from '@/services';
import { Column, ColumnCellTemplateData } from 'devextreme/ui/data_grid';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { DxElement } from 'devextreme/core/element';

dayjs.extend(weekOfYear);
dayjs.extend(localizedFormat);
dayjs.locale('vi');

type WeeklyProjectSchedulePlanEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  budgetYearValue?: number | null | undefined;
};

const t = translationWithNamespace('weeklyProjectSchedulePlan');
const onWeeklyProjectSchedulePlanMutationSuccess = createMutationSuccessFn(
  'weeklyProjectSchedulePlan'
);
const displayCellTemplate = (
  container: DxElement,
  options: ColumnCellTemplateData<any, any>
): void => {
  const div = document.createElement('div');
  div.textContent = options.value ?? '';
  Object.assign(div.style, {
    maxHeight: '200px',
    minHeight: '60px',
    overflowY: 'auto',
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    width: '100%',
    lineHeight: '1',
  });
  container.appendChild(div);
};

export const WeeklyProjectSchedulePlanEditableTableDevextreme = memo(
  ({ role, budgetYearValue }: WeeklyProjectSchedulePlanEditableTableProps) => {
    const { watch } = useFormContext<{ items: WeeklyProjectSchedulePlanDetail[] }>();
    const { projects } = useAuth();

    const editableData = watch('items');

    const createWeekColumns = (weekIndex: number, year: number): Column[] => {
      const weekNum = String(weekIndex).padStart(2, '0');
      const weekKey = `week${weekNum}`;
      const landKey = `landWeek${weekNum}`;

      const startDate = dayjs().year(year).week(weekIndex).day(1); // day(1) là thứ Hai
      const endDate = dayjs().year(year).week(weekIndex).day(6); // day(6) là thứ Bảy
      const displayStartDate =
        startDate.year() === year ? startDate : dayjs().year(year).startOf('year');
      const displayEndDate = endDate.year() === year ? endDate : dayjs().year(year).endOf('year');

      const dateRange = `(${displayStartDate.format('DD/MM')}-${displayEndDate.format('DD/MM')})`;

      return [
        {
          dataField: weekKey,
          caption: `${t('fields.weeklyProjectSchedulePlanDetailFields.weekHeader')} ${weekNum} ${dateRange}`,
          dataType: 'string',
          width: 400,
          // cssClass: 'whitespace-pre-wrap',
          cellTemplate: displayCellTemplate,
        },
        {
          dataField: landKey,
          caption: `${t('fields.weeklyProjectSchedulePlanDetailFields.landWeekHeader')} ${weekNum} ${dateRange}`,
          dataType: 'string',
          width: 400,
          // cssClass: 'whitespace-pre-wrap',
          cellTemplate: displayCellTemplate,
        },
      ];
    };
    // Move otherColumns inside useMemo to avoid recreating it on every render
    const { tableColumns } = useMemo(() => {
      const otherColumns: Column[] = [
        {
          cellTemplate: (container, options) => {
            const pageIndex = options.component.pageIndex();
            const pageSize = options.component.pageSize();
            const serialNumber = pageIndex * pageSize + options.rowIndex + 1;
            container.textContent = serialNumber.toString();
          },
          caption: 'STT',
          fixed: true,
          fixedPosition: 'left',
          alignment: 'center',
          width: 50,
          dataField: 'serialNumber',
          allowSorting: false,
          allowFiltering: false,
          allowEditing: false,
          format: ',##0,##',
        },
        {
          dataField: 'projectId',
          caption: t('fields.weeklyProjectSchedulePlanDetailFields.projectId'),
          lookup: { dataSource: projects, valueExpr: 'id', displayExpr: 'name' },
          allowEditing: false,
          fixedPosition: 'left',
          fixed: true,
        },
        {
          dataField: 'issuesAndRecommendations',
          // header: () => {
          //   const headerText = t(
          //     'fields.weeklyProjectSchedulePlanDetailFields.issuesAndRecommendations'
          //   );
          //   const tooltipText = t('fields.tooltips.issuesAndRecommendationsTooltip');
          //   return <span title={tooltipText}>{headerText}</span>;
          // },
          caption: t('fields.weeklyProjectSchedulePlanDetailFields.issuesAndRecommendations'),
          dataType: 'string',
          width: 400,
          cellTemplate: displayCellTemplate,
        },
      ];
      const calculationYear = budgetYearValue;

      const allWeekColumns = Array.from({ length: 52 }, (_, i) =>
        createWeekColumns(i + 1, calculationYear!)
      ).flat();

      const currentWeek = dayjs().week();
      let startWeek: number;
      let endWeek: number;

      if (currentWeek === 1) {
        startWeek = 1;
        endWeek = 3;
      } else if (currentWeek === 52) {
        startWeek = 50;
        endWeek = 52;
      } else {
        startWeek = currentWeek - 1;
        endWeek = currentWeek + 1;
      }

      const getWeekNumberFromId = (id: string | undefined): number | null => {
        if (!id) return null;
        const match = id.match(/(?:week|landWeek)(\d{2})$/);
        return match ? parseInt(match[1], 10) : null;
      };

      allWeekColumns.forEach(col => {
        if (col.dataField) {
          const weekNum = getWeekNumberFromId(col.dataField);
          const isVisibleByDefault = weekNum !== null && weekNum >= startWeek && weekNum <= endWeek;
          if (!isVisibleByDefault) {
            col.visible = false;
          }
        }
      });

      const saveRowColumn: Column = {
        width: 50,
        type: 'buttons',
        buttons: [
          {
            // name: 'save',
            icon: 'save',
            onClick: e => {
              const row = e.row?.data as WeeklyProjectSchedulePlanDetail;
              createPostMutateCustomPathFn<WeeklyProjectSchedulePlanDetail>(
                `weekly-project-schedule-plan/update-detail?id=${row.id}`
              )({
                ...row,
              })
                .then(() => {
                  onWeeklyProjectSchedulePlanMutationSuccess(1);
                })
                .catch(error => {
                  console.error('Error fetching data:', error);
                });
            },
          },
        ],
      };

      return {
        tableColumns: [...otherColumns, ...allWeekColumns, saveRowColumn],
      };
    }, [budgetYearValue, projects]);

    return (
      <div>
        <DevexDataGrid
          id={TABLES.WEEKLY_PROJECT_SCHEDULE_PLAN_DETAIL}
          dataSource={editableData}
          columns={tableColumns}
          editing={{
            mode: 'cell',
            allowUpdating: role?.isUpdate,
            allowDeleting: false,
            allowAdding: false,
            useIcons: true,
          }}
          hideSerialNumber
          onEditorPreparing={e => {
            if (e.dataField !== 'projectId' && e.parentType === 'dataRow') {
              e.editorName = 'dxTextArea';
            }
          }}
          onEditorPrepared={e => {
            if (e.dataField !== 'projectId' && e.parentType === 'dataRow') {
              const element = e.editorElement.querySelector(
                '.dx-texteditor-input'
              ) as HTMLTextAreaElement;
              if (element) {
                element.style.lineHeight = '1';
                element.style.minHeight = '200px';
              }
            }
          }}
          // onCellPrepared={e => {
          //   if (e.rowType === 'data' && e.column.dataField !== 'projectId') {
          //     e.cellElement.style.whiteSpace = 'pre-wrap';
          //     e.cellElement.style.wordBreak = 'break-all';
          //     e.cellElement.style.lineHeight = '1';
          //   }
          // }}
        >
          {/* <ColumnDevex
            dataField="issuesAndRecommendations"
            editorOptions={{ editorType: 'dxTextArea' }}
            showEditorAlways
            allowEditing
          /> */}
        </DevexDataGrid>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if role or calculateForm changes
    return prevProps.role === nextProps.role && prevProps.calculateForm === nextProps.calculateForm;
  }
);
