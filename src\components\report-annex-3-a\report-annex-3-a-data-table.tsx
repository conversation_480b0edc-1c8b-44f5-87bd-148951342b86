/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useAuth, useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn, Model } from '@/services';
import { ReportAnnex3a } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent, RowDblClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('reportAnnex3A');

const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const ReportAnnex3aDataTable = ({
  type = 'project-management',
}: {
  type?: 'finance' | 'project-management';
}) => {
  const isForFinance = type === 'finance';

  let path = PATHS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT;
  if (isForFinance) {
    path = PATHS.REPORT_ANNEX_3A_FINANCE;
  }
  const navigate = useNavigate();
  const { t } = useTranslation('reportAnnex3A');
  const { projects, user } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;

  let permission = PERMISSIONS.REPORT_ANNEX_3A_PROJECT_MANAGEMENT;
  if (isForFinance) {
    permission = PERMISSIONS.REPORT_ANNEX_3A_FINANCE;
  }
  const role = usePermission(permission);

  const getTargetAlias = (target: ReportAnnex3a | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };

  //Thuộc nguồn vốn
  const { list: completionAcceptances } = useEntity({
    queryKey: [QUERIES.COMPLETION_ACCEPTANCE],
    model: 'completion-acceptance',
  });

  let initialQuery = {
    filterColumn: [
      {
        column: 'ProjectId',
        expression: 'IN',
        keySearch: `(${projectIds || 0})`,
      },
    ],
  };

  if (isForFinance) {
    initialQuery = {
      filterColumn: [
        ...initialQuery.filterColumn,
        {
          column: 'IsForward',
          expression: '=',
          keySearch: 'true',
        },
      ],
    };
  }

  let model: Model = 'report-annex-3a';
  if (isForFinance) {
    model = 'report-annex-3a-finance';
  }

  let queryKey = QUERIES.REPORT_ANNEX_3A_PROJECT_MANAGEMENT;
  if (isForFinance) {
    queryKey = QUERIES.REPORT_ANNEX_3A_FINANCE;
  }

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<ReportAnnex3a, PeriodFilter>({
    queryRangeName: 'reportAnnex3aTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<ReportAnnex3a>(model),
    deleteKey: [MUTATE.DELETE_REPORT_ANNEX_3A_PROJECT_MANAGEMENT],

    invalidateKey: [queryKey],
    initialQuery: initialQuery,
  });

  const { data, refetch } = useQuery({
    queryKey: [queryKey],
    queryFn: () => {
      return createQueryPaginationFn<ReportAnnex3a>(model)({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'ReportAnnex3aTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<ReportAnnex3a>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<ReportAnnex3a>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };
  const onDoubleClickRow = (e: RowDblClickEvent) => {
    if (e?.data) {
      navigate(`${path}/` + e.data?.id, { state: path });
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn('reportAnnex3aTime', from!, to!);
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.REPORT_ANNEX_3A}
        dataSource={items}
        onAddNewClick={isForFinance ? undefined : onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
        onEditDoubleClick={onDoubleClickRow}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete && !isForFinance} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column dataField="code" caption={t('fields.code')} alignment="left" />
        <Column dataField="projectId" caption={t('fields.projectId')}>
          <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="sumContractValue"
          caption={t('fields.sumContractValue')}
          customizeText={customizeNumberCell(0)}
          dataType="number"
        />
        <Column
          dataField="sumAdvancePaymentValueForThePeriod"
          caption={t('fields.sumAdvancePaymentValueForThePeriod')}
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="sumAdvancePaymentExecution"
          caption={t('fields.sumAdvancePaymentExecution')}
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="sumCompletedVolumeThisPeriod"
          caption={t('fields.sumCompletedVolumeThisPeriod')}
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="sumAdvancePaymentRecovery"
          caption={t('fields.sumAdvancePaymentRecovery')}
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="sumDisbursementRequestValue"
          caption={t('fields.sumDisbursementRequestValue')}
          customizeText={customizeNumberCell(0)}
        />
        <Column
          dataField="sumCumulativeDisbursedValue"
          caption={t('fields.sumCumulativeDisbursedValue')}
          customizeText={customizeNumberCell(0)}
        />
        <Column dataField="completionAcceptanceId" caption={t('fields.completionAcceptanceId')}>
          <Lookup
            dataSource={completionAcceptances}
            displayExpr={displayExpr(['code'])}
            valueExpr={'id'}
          />
        </Column>
        <Column
          dataField="reservefund"
          caption={t('fields.reservefund')}
          customizeText={customizeNumberCell()}
        />
        <Column dataField="note" caption={t('fields.note')} alignment="left" />
        <Column
          dataField="reportAnnex3aTime"
          caption={t('fields.reportAnnex3aTime')}
          dataType="date"
          alignment="left"
        />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="reportAnnex3A"
      />
    </PageLayout>
  );
};
