import { BasicDialog } from '@/components/basic-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { QUERIES, TABLES } from '@/constant';
import { useBoolean, useDataTable, useEntity } from '@/hooks';
import { toDateType } from '@/lib/date';
import { getRandomNumber } from '@/lib/number';
import { callbackWithTimeout, cn, displayExpr } from '@/lib/utils';
import { createQueryByIdFn, createQueryPaginationFn } from '@/services';
import { CapitalIncreasePlan } from '@/types';
import { AdjustedCapitalIncreasePlan } from '@/types/adjusted-capital-increase-plan';
import { useQuery } from '@tanstack/react-query';
import Button from 'devextreme-react/button';
import { Column, DataGridTypes, Editing, Lookup, Selection } from 'devextreme-react/data-grid';
import { Check } from 'lucide-react';
import { useCallback, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const queryPagination = createQueryPaginationFn<CapitalIncreasePlan>('capital-increase-plan');
export const CapitalIncreasePlanSelectTable = () => {
  const { t } = useTranslation(['capitalIncreasePlan']);
  const { state: open, toggle, setFalse } = useBoolean();

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: budgetFunds } = useEntity({
    queryKey: [QUERIES.BUDGET_FUND],
    model: 'budget-fund',
  });

  // const [selectedRow, setSelectedRows] = useState<TenderPackage>();
  const selectedRowRef = useRef<{ instance: CapitalIncreasePlan | null }>({ instance: null });

  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<CapitalIncreasePlan, PeriodFilter>({
    queryRangeName: 'capitalIncreasePlanTime',
    invalidateKey: [QUERIES.CAPITAL_INCREASE_PLAN],
    initialQuery: {
      filterColumn: [],
    },
  });

  const queryFn = async () => {
    const params = {
      pageIndex: 1,
      pageSize: -1,
      sortColumn: 'Id',
      sortOrder: 1,
      isPage: false,
      filterColumn: [],
      ...queryListParams,
    };

    const res = await queryPagination(params);

    return res;
  };

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.CAPITAL_INCREASE_PLAN],
    queryFn,
  });

  const { items } = data || { items: [] };

  const onSelectionChanged = useCallback(
    ({ selectedRowsData }: DataGridTypes.SelectionChangedEvent) => {
      const data = selectedRowsData[0];
      selectedRowRef.current.instance = data as CapitalIncreasePlan;
    },
    []
  );
  const { setValue } = useFormContext<AdjustedCapitalIncreasePlan>();

  const { refetch: getCapitalIncreasePlan } = useQuery({
    queryKey: [QUERIES.CAPITAL_INCREASE_PLAN, 0],
    queryFn: () => {
      return createQueryByIdFn<CapitalIncreasePlan>('capital-increase-plan')(
        selectedRowRef.current.instance?.id || 0
      );
    },
    enabled: false,
  });

  const onApply = () => {
    if (selectedRowRef.current.instance) {
      getCapitalIncreasePlan()
        .then(response => {
          const selectedRow = response.data as CapitalIncreasePlan;
          setValue('budgetFundId', selectedRow.budgetFundId);
          setValue('budgetYear', toDateType(selectedRow.budgetYear || null));
          setValue('capitalIncreasePlanId', selectedRow.id);
          setValue(
            'adjustedCapitalIncreasePlanDetails',
            selectedRow.capitalIncreasePlanDetails.map(item => ({
              ...item,
              id: -getRandomNumber(),
              adjustedCapitalIncreasePlanId: 0,
              capitalIncreasePlanDetailId: item.id,
            }))
          );
        })
        .catch(error => {
          console.error('Error fetching capital increase plan:', error);
        });
    }
    setFalse();
  };

  return (
    <>
      <Button
        icon="plus"
        text={t('page.form.buttonCapitalIncreasePlan', { ns: 'adjustedCapitalIncreasePlan' })}
        onClick={toggle}
        type="default"
        stylingMode="contained"
      />
      <BasicDialog
        open={open}
        toggle={toggle}
        // title nhớ lấy từ file đa ngôn ngữ
        title={t('page.selectFromCapitalIncreasePlan', { ns: 'adjustedCapitalIncreasePlan' })}
      >
        <div className="max-w-fit overflow-scroll">
          <div className={cn('h-full opacity-100')}>
            <PeriodFilterForm
              defaultSearchValues={{
                range: [queryListParams.fromDate!, queryListParams.toDate!],
              }}
              onSearch={values => {
                if (values.range) {
                  const [from, to] = values.range;
                  queryListMethods.addOrReplaceFilterDateColumn(
                    'capitalIncreasePlanTime',
                    from!,
                    to!
                  );
                }

                callbackWithTimeout(refetch);
              }}
            ></PeriodFilterForm>
            <div className="grid grid-cols-24">
              <div className="col-span-24">
                <DevexDataGrid
                  id={TABLES.TENDER_PACKAGE}
                  dataSource={items}
                  onRefresh={() => {
                    callbackWithTimeout(refetch);
                  }}
                  onSelectionChanged={onSelectionChanged}
                >
                  <Editing allowUpdating={false} allowDeleting={false} useIcons />
                  <Selection mode="single" allowSelectAll={false} />
                  {/* Mã phiếu */}
                  <Column dataField="code" caption={t('fields.code')} alignment="left" />

                  {/* Ngày lập */}
                  <Column
                    dataField="capitalIncreasePlanTime"
                    caption={t('fields.capitalIncreasePlanTime')}
                    dataType="date"
                    alignment="left"
                  />

                  {/* Người lập */}
                  <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
                    <Lookup
                      dataSource={users}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>

                  {/* Số quyết định */}
                  <Column
                    dataField="approvalNumber"
                    caption={t('fields.approvalNumber')}
                    alignment="left"
                  />

                  {/* Nguồn ngân sách */}
                  <Column dataField="budgetFundId" caption={t('fields.budgetFundId')}>
                    <Lookup
                      dataSource={budgetFunds}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>

                  {/* Ngày quyết định */}
                  <Column
                    dataField="approvalDate"
                    caption={t('fields.approvalDate')}
                    dataType="date"
                    alignment="left"
                  />

                  {/* Năm ngân sách */}
                  <Column
                    dataField="budgetYear"
                    caption={t('fields.budgetYear')}
                    dataType="date"
                    alignment="left"
                    format="yyyy"
                  />

                  {/* Tổng tiền */}
                  <Column
                    dataField="totalAmount"
                    caption={t('fields.totalAmount')}
                    dataType="number"
                    alignment="right"
                    customizeText={customizeNumberCell()}
                  />

                  {/* Ghi chú */}
                  <Column dataField="note" caption={t('fields.note')} alignment="left" />
                </DevexDataGrid>
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-24">
          <div className="flex justify-end">
            <Button
              className={cn(
                'items-center border-solid  border-gray-400/60 bg-white px-1.5 hover:bg-gray-200'
              )}
              onClick={onApply}
            >
              <Check size={16} className="text-sky-500" strokeWidth={3} />
              <span className="ml-1">
                {t('page.form.okLabel', { ns: 'adjustedCapitalIncreasePlan' })}
              </span>
            </Button>
          </div>
        </div>
      </BasicDialog>
    </>
  );
};
