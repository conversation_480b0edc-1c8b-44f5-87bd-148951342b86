import { CapitalPlanDisbursementProgressReport } from '@/types';
import Chart, {
  ArgumentAxis,
  CommonSeriesSettings,
  Series,
  Legend,
  Title,
  ValueAxis,
} from 'devextreme-react/chart';
import { RowLabel } from './row-label';
import { Link } from 'react-router-dom';

export const DasboardCapitalPlanDisbursementProgressReport = ({
  items,
}: {
  items: CapitalPlanDisbursementProgressReport[];
}) => {
  return (
    <div className="bg-white p-4">
      <div className="mb-2 text-xl font-bold">
        <Link to="/summary-report/project-deployment-status-report" className="!text-black">
          Báo cáo tiến độ giải ngân KH vốn
        </Link>
      </div>
      <Chart dataSource={items} className="bg-white">
        <CommonSeriesSettings argumentField="budgetFundName" type="bar" />

        {/* <Series valueField="totalProject" name="Tổng số công trình">
          <RowLabel />
        </Series> */}
        <Series valueField="totalPlannedCapitalMillionVnd" name="Tổng kế hoạch vốn">
          <RowLabel />
        </Series>
        <Series valueField="cumulativeDisbursedValueMillionVnd" name="Lũy kế giá trị giải ngân">
          <RowLabel />
        </Series>

        <ArgumentAxis
          title={{ text: 'Tên nguồn ngân sách', font: { weight: 'bold' } }}
          label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
        />
        <ValueAxis
          title={{ text: 'Số tiền (triệu VNĐ)', font: { weight: 'bold' } }}
          valueType="numeric"
          // logarithmBase={10}
          type="continuous"
          label={{
            font: { weight: 'bold' },
          }}
        />

        <Legend verticalAlignment="bottom" horizontalAlignment="center" font={{ weight: 800 }} />
        <Title text=""></Title>
      </Chart>
    </div>
  );
};
