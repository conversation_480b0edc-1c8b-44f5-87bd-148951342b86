import { useTranslation } from 'react-i18next';

import { DialogFooter } from '@/components/ui/dialog';
import { FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  addLabel,
  applyLabel,
  closeLabel,
  enterLabel,
  PERMISSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';

import { useAuth, useBoolean, useEntity, usePermission } from '@/hooks';
import { displayExpr } from '@/lib/utils';
import { Agency, DocumentGroup, IUserPermission, RecordAttachment } from '@/types';
import { Button, DateBox, SelectBox, TextBox } from 'devextreme-react';
import { useFormContext } from 'react-hook-form';
import { BasicDialog } from '@/components/basic-dialog';
import { DocumentTypeForm } from '@/pages/document-type';
import { DocumentGroupForm } from '@/pages/document-group';
import { useState } from 'react';
import { LoadingOverlay } from '@/components/loading-overlay';
import { RecordFilesEditableTableFileSystem } from './record-files-editable-table';

type Props = {
  onClose: () => void;
  onOk: (updatedRow: RecordAttachment) => void;
  role?: IUserPermission;
  folder?: string;
};

export const RecordFormFileSystem = ({ onClose, onOk, role, folder }: Props) => {
  const { t } = useTranslation('recordsAttachment');
  const methods = useFormContext<RecordAttachment>();
  const projectId = methods.watch(`projectId`);
  const { projects } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const groupDocRole = usePermission(PERMISSIONS.DOCUMENT_TYPE);
  const typeDocRole = usePermission(PERMISSIONS.DOCUMENT_GROUP);
  const { state: isAddNewGroupDocFormOpen, toggle: toggleAddNewGroupDocForm } = useBoolean(false);
  const { state: isAddNewTypeDocFormOpen, toggle: toggleAddNewTypeDocForm } = useBoolean(false);

  const { fetch: fetchDocumentGroups } = useEntity({
    model: 'document-type',
    queryKey: [QUERIES.DOCUMENT_GROUP],
    externalParams: { pageSize: -1 },
  });

  const { fetch: fetchDocumentTypes } = useEntity({
    model: 'document-type',
    queryKey: [QUERIES.DOCUMENT_TYPE],
    externalParams: { pageSize: -1 },
  });

  return (
    <div>
      <div className="space-y-4 ">
        <div className="flex items-center">
          <FormLabel
            name="projectId"
            htmlFor="projectId"
            className="hidden w-[105px] text-nowrap md:block lg:w-[114px]"
          >
            {t('fields.projectId')}
          </FormLabel>
          <FormField
            id="projectId"
            name={`projectId`}
            className="w-full  lg:w-[500px] xl:w-[600px]"
            label={t('fields.projectId')}
          >
            <SelectBox
              items={projects}
              searchExpr={['name', 'code']}
              valueExpr="id"
              onFocusIn={e => {
                const input = e.element.querySelector(
                  'input.dx-texteditor-input'
                ) as HTMLInputElement;
                if (input) input.select();
              }}
              searchEnabled
              searchMode="contains"
              displayExpr={displayExpr(['name'])}
              showClearButton
              focusStateEnabled={false}
            />
          </FormField>
        </div>
        <div className="flex flex-col items-stretch lg:flex-row lg:items-start lg:space-x-4">
          {/* Cột 1 */}
          <div className=" space-y-4 ">
            <div className="flex  items-center">
              <FormLabel
                name="groupDocId"
                htmlFor="groupDocId"
                className="required hidden w-[114px]  text-nowrap md:block"
              >
                {t('fields.groupDocId')}
              </FormLabel>
              <FormField
                id="groupDocId"
                name={`groupDocId`}
                className="w-full lg:flex-1  xl:w-[300px]"
                label={t('fields.groupDocId')}
              >
                <FormCombobox<DocumentGroup>
                  model="document-group"
                  queryKey={[QUERIES.DOCUMENT_GROUP]}
                  placeholder={`${selectLabel} ${t('fields.groupDocId')}`}
                  externalParams={{ pageSize: -1 }}
                  onSelectItem={item => {
                    if (!item) return;

                    methods.setValue(`content`, item.name);
                    methods.setValue(`groupDocCode`, item.code);
                  }}
                  filter={item => item.projectId === projectId}
                />
              </FormField>
              <div className="block items-center justify-center lg:hidden">
                <Button
                  stylingMode="text"
                  type="default"
                  className="ml-1 mt-3 "
                  icon="plus"
                  onClick={toggleAddNewGroupDocForm}
                />
              </div>
            </div>
            <div className="flex  items-center">
              <FormLabel
                name="typeDocId"
                htmlFor="typeDocId"
                className="hidden w-[114px] text-nowrap  md:block"
              >
                {t('fields.typeDocId')}
              </FormLabel>
              <FormField
                id="typeDocId"
                name={`typeDocId`}
                className="w-full lg:flex-1  xl:w-[300px]"
                label={t('fields.typeDocId')}
              >
                <FormCombobox
                  model="document-type"
                  queryKey={[QUERIES.DOCUMENT_TYPE]}
                  placeholder={`${selectLabel} ${t('fields.typeDocId')}`}
                  externalParams={{ pageSize: -1 }}
                  onSelectItem={item => {
                    if (!item) return;
                    methods.setValue(`content`, item.name);
                  }}
                />
              </FormField>
              <div className="block items-center justify-center lg:hidden">
                <Button
                  stylingMode="text"
                  type="default"
                  className="ml-1 mt-3 "
                  icon="plus"
                  onClick={toggleAddNewTypeDocForm}
                />
              </div>
            </div>
            <div className="flex items-center">
              <FormLabel
                name="noDoc"
                htmlFor="noDoc"
                className="hidden w-[114px] text-nowrap md:block"
              >
                {t('fields.noDoc')}
              </FormLabel>
              <FormField
                id="noDoc"
                name={`noDoc`}
                className="w-full lg:flex-1"
                label={t('fields.noDoc')}
              >
                <TextBox placeholder={`${enterLabel} ${t('fields.noDoc')}`} />
              </FormField>
            </div>
            <div className="flex items-center">
              <FormLabel
                name="dateCreate"
                htmlFor="dateCreate"
                className="hidden w-[114px] text-nowrap md:block"
              >
                {t('fields.dateCreate')}
              </FormLabel>
              <FormField
                id="dateCreate"
                name={`dateCreate`}
                className="w-full lg:flex-1"
                label={t('fields.dateCreate')}
                type="date"
              >
                <DateBox
                  placeholder={`${selectLabel} ${t('fields.dateCreate')}`}
                  pickerType="calendar"
                  focusStateEnabled={false}
                />
              </FormField>
            </div>
            <div className="flex items-center">
              <FormLabel
                name="agencyId"
                htmlFor="agencyId"
                className="hidden  text-nowrap md:block lg:w-[114px]"
              >
                {t('fields.agencyId')}
              </FormLabel>
              <FormField
                id="agencyId"
                name={`agencyId`}
                className="w-full lg:flex-1"
                label={t('fields.agencyId')}
              >
                <FormCombobox<Agency>
                  model="agency"
                  queryKey={[QUERIES.AGENCY]}
                  placeholder={`${selectLabel} ${t('fields.agencyId')}`}
                  externalParams={{ pageSize: -1 }}
                />
              </FormField>
            </div>
          </div>
          {/* Cột 2 */}
          <div className=" w-full space-y-4 ">
            <div className="hidden lg:block">
              <Button
                stylingMode="text"
                type="default"
                text={`${addLabel} ${t('fields.groupDocId')}`}
                className="mt-1.5 "
                icon="plus"
                onClick={toggleAddNewGroupDocForm}
              />
            </div>
            <div className="hidden lg:block">
              <Button
                stylingMode="text"
                type="default"
                text={`${addLabel} ${t('fields.typeDocId')}`}
                className="mt-1.5 "
                icon="plus"
                onClick={toggleAddNewTypeDocForm}
              />
            </div>

            <div className="flex items-center xl:w-[500px] 2xl:w-[600px]">
              <FormLabel
                name="content"
                htmlFor="content"
                className="hidden w-[97px] md:block lg:w-[57px]"
              >
                {t('fields.content')}
              </FormLabel>
              <FormField
                id="content"
                name={`content`}
                className="w-full flex-1"
                label={t('fields.content')}
              >
                <TextBox placeholder={`${enterLabel} ${t('fields.content')}`} />
              </FormField>
            </div>
            <div className="flex items-center xl:w-[500px] 2xl:w-[600px]">
              <FormLabel
                name="note"
                htmlFor="note"
                className="hidden w-[97px] md:block  lg:w-[57px]"
              >
                {t('fields.note')}
              </FormLabel>
              <FormField
                id="note"
                name={`note`}
                className="w-full flex-1 md:block"
                label={t('fields.note')}
              >
                <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
              </FormField>
            </div>
          </div>
        </div>
      </div>
      <BasicDialog
        title={t('page.form.addNew', { ns: 'documentGroup' })}
        open={isAddNewGroupDocFormOpen}
        toggle={() => {
          toggleAddNewGroupDocForm();
          fetchDocumentGroups({});
        }}
        className="max-w-[100vw] md:w-auto"
      >
        <DocumentGroupForm editId={0} role={groupDocRole} projectId={projectId} />
      </BasicDialog>
      <BasicDialog
        title={t('page.form.addNew', { ns: 'documentType' })}
        open={isAddNewTypeDocFormOpen}
        toggle={() => {
          toggleAddNewTypeDocForm();
          fetchDocumentTypes({});
        }}
        className="max-w-[100vw] md:w-auto"
      >
        <DocumentTypeForm editId={0} role={typeDocRole} />
      </BasicDialog>
      <div className="mt-8 grid grid-cols-1">
        <Tabs defaultValue="detail" className="shadow-none">
          <div className="w-full">
            <TabsList>
              <TabsTrigger value="detail">{t('page.form.files.detail')}</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="detail" className="col-span-1 mt-4 overflow-x-auto">
            <RecordFilesEditableTableFileSystem
              role={role}
              folder={folder}
              setIsLoading={setIsLoading}
            />
          </TabsContent>
        </Tabs>
      </div>
      <DialogFooter className="mt-8 flex flex-row-reverse gap-x-2 bg-white py-1">
        <Button
          stylingMode="contained"
          text={applyLabel}
          icon="save"
          type="success"
          onClick={() => {
            const updatedItem = methods.getValues();
            onOk(updatedItem);
          }}
        />
        <Button
          stylingMode="outlined"
          text={closeLabel}
          icon="close"
          // disabled={loading || (editId ? !role?.isUpdate : !role?.isCreate)}
          onClick={onClose}
          type="default"
        />
      </DialogFooter>
      {isLoading && <LoadingOverlay loading={isLoading} text="Đang tải file" />}
    </div>
  );
};
