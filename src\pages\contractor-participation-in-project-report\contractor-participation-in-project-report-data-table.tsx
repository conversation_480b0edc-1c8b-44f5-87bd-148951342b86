/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { PageLayout } from '@/components/page-layout';
import { QUERIES } from '@/constant';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { callbackWithTimeout } from '@/lib/utils';
import { createQueryReport } from '@/services';
import { useQuery } from '@tanstack/react-query';

import { YearProjectDataFilter } from './year-project-data-filter';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { ContractorParticipationInProjectReportDetailTable } from './contractor-participation-in-project-report-detail';
import { useDataTable } from '@/hooks';
import { PeriodFilter } from '@/components/period-filter-form';
import { ContractorParticipationInProjectReport } from '@/types/contractor-participation-in-project-report';

const t = translationWithNamespace('contractorParticipationInProjectReport');

const queryTypeSchema = z.object({
  year: z.date(),
});

type QueryType = z.infer<typeof queryTypeSchema>;

export const ContractorParticipationInProjectReportDataTable = () => {
  const queryForm = useForm<QueryType>({
    defaultValues: { year: new Date() },
  });

  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<ContractorParticipationInProjectReport, PeriodFilter>({
    initialQuery: {
      objParam: {
        year: new Date(),
      },
      filterColumn: [],
    },
  });

  const { data: dataDetails, refetch: refetchDetails } = useQuery({
    queryKey: [QUERIES.CONTRACTOR_PARTICIPATION_IN_PROJECT_REPORT],
    queryFn: () => {
      return createQueryReport<ContractorParticipationInProjectReport>(
        'contractor-participation-in-project-report'
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items: details } = dataDetails || { items: [] };

  return (
    <PageLayout header={t('page.header')}>
      <div className="items-top flex gap-x-4">
        <YearProjectDataFilter
          form={queryForm}
          onSearch={() => {
            const values = queryForm.getValues();
            queryListMethods.setQueryListParams(prev => ({
              ...prev,
              objParam: {
                year: values.year,
              },
            }));

            callbackWithTimeout(refetchDetails);
          }}
        />
      </div>
      <div className="mt-8">
        <ContractorParticipationInProjectReportDetailTable
          items={details}
          refetch={refetchDetails}
        />
      </div>
    </PageLayout>
  );
};
