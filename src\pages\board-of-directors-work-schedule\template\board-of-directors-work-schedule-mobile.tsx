/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { useState, memo, useCallback } from 'react';
import { formatISO } from 'date-fns';

import { useQuery } from '@tanstack/react-query';
import axiosInstance, { request } from '@/axios-instance';
import { BoardOfDirectorsWorkScheduleDetail, PaginationResponse } from '@/types';
import { MapPin, User, Users } from 'lucide-react';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { LoadingOverlay } from '@/components/loading-overlay';
import CalendarBoardDirectors from '@/components/calendar-board-directors';

const BoardOfDirectorsWorkScheduleMobile = memo(() => {
  const t = translationWithNamespace('boardOfDirectorsWorkSchedule');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  // Memoize callback để tránh re-render
  const handleSelectedDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  // Sử dụng formatISO từ date-fns để format ngày theo múi giờ địa phương
  const formatDateToLocalISOString = useCallback((date: Date) => formatISO(date), []);

  // Hàm để lấy giờ từ chuỗi datetime ISO
  const formatTimeFromISO = useCallback((isoString: string) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  }, []);

  const { data, isPending } = useQuery({
    queryKey: ['get-detail-all-board-of-directors-work-schedule', selectedDate],
    queryFn: async () =>
      await request<PaginationResponse<BoardOfDirectorsWorkScheduleDetail>>(
        axiosInstance.post(`/board-of-directors-work-schedule/get-detail-all`, {
          dateTime: formatDateToLocalISOString(selectedDate),
        })
      ),
    enabled: !!selectedDate,
  });
  return (
    <div className="flex w-full items-center justify-center bg-white">
      {isPending && <LoadingOverlay loading={isPending} />}
      <div className="flex w-full max-w-[768px] flex-col bg-white">
        <div className="sticky -top-1 z-10 bg-white ">
          <CalendarBoardDirectors onSelectedDateChange={handleSelectedDateChange} />
        </div>
        <div className="mt-3 overflow-y-auto pb-5">
          {data?.items && data?.items.length > 0 && !isPending ? (
            data?.items.map((i, idx) => (
              <div key={idx} className="flex flex-row ">
                <div className="flex">
                  <div className=" mt-1 font-semibold">
                    <p className="text-slate-800">
                      {formatTimeFromISO(i?.workTime?.toString() ?? '')}
                    </p>
                    <p className="text-slate-800">{`${i.session === 1 ? t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.morning') : t('fields.boardOfDirectorsWorkScheduleDetails.sessionOptions.afternoon')}`}</p>
                  </div>
                  <div className="mx-1 flex flex-col items-center py-1">
                    <div className=" flex h-4 w-4 flex-col items-center justify-center rounded-full border p-1">
                      <div className="min-h-2 min-w-2 rounded-full bg-blue-700" />
                    </div>
                    <div className=" h-full border-l border-dashed border-blue-400 " />
                  </div>
                </div>
                <div className=" flex flex-col py-1 ">
                  <p className="mb-1 font-semibold text-slate-800">{i.content}</p>
                  <div className="mb-1 flex flex-row items-start">
                    <User size={15} className="min-w-4 text-blue-700" />
                    <p className="ml-1 leading-5 text-slate-800">{`${i.chairpersonName || ''} ${i.chairpersonOther ? ',' + i.chairpersonOther : ''}`}</p>
                  </div>
                  <div className="mb-1 flex flex-row items-start">
                    <MapPin size={15} color="red" className="mt-[2px] min-w-4" />
                    <p className="ml-1 leading-5 text-slate-800">{`${i.location || ''} `}</p>
                  </div>

                  <div className="mb-1 flex flex-row items-start">
                    <Users size={15} color="green" className="min-w-4" />
                    <p className="ml-1 leading-5 text-slate-800">{`${(i.memberNames || '').replace(/^,\s*/, '')} ${i.memberOther ? ',' + i.memberOther : ''}`}</p>
                  </div>

                  <div className="h-[1px] w-full bg-slate-200" />
                </div>
              </div>
            ))
          ) : (
            <div className="mt-5 w-full text-center">
              <p className="font-semibold text-slate-800">{t('page.notFound')}</p>
              <img src="/nodata.jpg" alt={t('page.notFound')} className="mx-auto mt-4 max-w-xs" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

BoardOfDirectorsWorkScheduleMobile.displayName = 'BoardOfDirectorsWorkScheduleMobile';

export default BoardOfDirectorsWorkScheduleMobile;
