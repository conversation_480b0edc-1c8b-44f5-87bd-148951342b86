import { useState, useEffect, useRef } from 'react';
import { Button } from 'devextreme-react/button';
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
  AIMessage,
  mapChatMessagesToStoredMessages,
} from '@langchain/core/messages';
import { LocalAuthUser } from '@/types';
import { LOCAL_STORE_KEYS, TOKEN_KEY } from '@/constant';
import { getLocalStorage } from '@/lib/localStorage';
import { saveData, getData } from '@/lib/indexedDB';
import { getCookie } from '@/lib/cookie';
import { SendHorizonal } from 'lucide-react';

interface ChatDialogProps {
  onClose: () => void;
}

const STORAGE_KEY = 'chat-ai-messages';
const MAX_MESSAGES = 50;

// Helper functions for localStorage

export default function ChatDialog({ onClose }: ChatDialogProps) {
  const user = getLocalStorage(LOCAL_STORE_KEYS.USER) as LocalAuthUser;
  // const [isExpanded, setIsExpanded] = useState(false);
  const getDefaultMessages = (): BaseMessage[] => [
    new SystemMessage(
      `Đây là thông tin user đang đăng nhập userId=${user?.userId} và userName=${user?.userName}`
    ),
  ];
  const saveMessagesToStorage = async (messages: BaseMessage[]) => {
    try {
      // Chỉ lưu 50 tin nhắn gần nhất (bao gồm cả SystemMessage)
      const limitedMessages = messages.slice(-MAX_MESSAGES);
      const serializedMessages = limitedMessages.map(msg => ({
        type: msg.getType(),
        content: msg.content,
      }));
      await saveData(STORAGE_KEY, serializedMessages);
    } catch (error) {
      console.error('Error saving messages to IndexedDB:', error);
    }
  };

  const loadMessagesFromStorage = async (): Promise<BaseMessage[]> => {
    try {
      const stored = await getData<
        Array<{
          type: string;
          content: string | undefined;
          [key: string]: unknown;
        }>
      >(STORAGE_KEY);

      if (!stored) {
        return getDefaultMessages();
      }

      const messages = stored.map(
        (msg: {
          type: string;
          content: string | undefined;
          [key: string]: unknown;
        }): BaseMessage => {
          const content = msg.content || '';
          switch (msg.type) {
            case 'system':
              return new SystemMessage(content);
            case 'human':
              return new HumanMessage(content);
            case 'ai':
              return new AIMessage(content);
            default:
              return new SystemMessage(content);
          }
        }
      );

      // Đảm bảo luôn có SystemMessage đầu tiên
      if (messages.length === 0 || !(messages[0] instanceof SystemMessage)) {
        return getDefaultMessages();
      }

      return messages;
    } catch (error) {
      console.error('Error loading messages from IndexedDB:', error);
      return getDefaultMessages();
    }
  };
  const [inputMessage, setInputMessage] = useState('');
  const [messages, setMessages] = useState<BaseMessage[]>(getDefaultMessages());
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousMessagesLength = useRef(0);

  // Load messages from IndexedDB on component mount
  useEffect(() => {
    const loadInitialMessages = async () => {
      try {
        const loadedMessages = await loadMessagesFromStorage();
        setMessages(loadedMessages);
      } catch (error) {
        console.error('Error loading initial messages:', error);
        setMessages(getDefaultMessages());
      } finally {
        setIsLoadingMessages(false);
      }
    };

    void loadInitialMessages();
  }, []);

  // Save messages to IndexedDB whenever messages change
  useEffect(() => {
    if (!isLoadingMessages) {
      void saveMessagesToStorage(messages);
    }
  }, [messages, isLoadingMessages]);

  // Scroll to bottom immediately when component mounts and messages are loaded
  useEffect(() => {
    if (!isLoadingMessages && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'instant' });
      previousMessagesLength.current = messages.length;
    }
  }, [isLoadingMessages]);

  // Smooth scroll only when new messages are added
  useEffect(() => {
    if (
      !isLoadingMessages &&
      messages.length > previousMessagesLength.current &&
      messagesEndRef.current
    ) {
      // Sử dụng setTimeout để đảm bảo DOM đã được render hoàn toàn
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
      previousMessagesLength.current = messages.length;
    }
  }, [messages, isLoadingMessages]);

  // Scroll khi tin nhắn AI được cập nhật (từ loading thành response thực tế)
  useEffect(() => {
    if (!isLoadingMessages && messagesEndRef.current) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage instanceof AIMessage && lastMessage.content !== 'Chờ phản hồi') {
        // Delay để đảm bảo bảng markdown đã được render
        setTimeout(() => {
          if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
          }
        }, 200);
      }
    }
  }, [messages.map(m => m.content).join(''), isLoadingMessages]);

  const clearChatHistory = async () => {
    try {
      const defaultMessages = getDefaultMessages();
      setMessages(defaultMessages);
      await saveData(STORAGE_KEY, []);
    } catch (error) {
      console.error('Error clearing chat history from IndexedDB:', error);
    }
  };

  async function sendMessage() {
    if (!inputMessage.trim()) return;

    setIsLoading(true);

    // Hiển thị tin nhắn người dùng ngay lập tức
    const userMessage = new HumanMessage(inputMessage);
    // const loadingMessage = new AIMessage('Chờ phản hồi');
    const messageHistoryWithUser = [...messages, userMessage];

    setMessages(messageHistoryWithUser);
    setInputMessage('');

    console.log(
      'mapChatMessagesToStoredMessages(messageHistory)',
      mapChatMessagesToStoredMessages([...messages, userMessage])
    );

    try {
      //Gọi API NestJS thay vì server action Next.js
      // const response = await fetch(
      //   'http://localhost:3004/ai-agent/message',
      //   // 'https://api-qlda-hocmon-dev.phanmemviet.net.vn/ai-agent/message',
      //   {
      //     method: 'POST',
      //     headers: {
      //       'Content-Type': 'application/json',
      //     },
      //     body: JSON.stringify({
      //       messages: mapChatMessagesToStoredMessages([...messages, userMessage]),
      //     }),
      //   }
      // );
      // const baseURL = //'https://755e2301ac58.ngrok.app';//import.meta.env.VITE_API_URL;
      const baseURL = import.meta.env.VITE_API_URL;
      const response = await fetch(`${baseURL}/ai-agent/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getCookie<string>(TOKEN_KEY)}`,
        },
        body: JSON.stringify({
          messages: mapChatMessagesToStoredMessages([...messages, userMessage]),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Thay thế tin nhắn loading bằng response thực tế
      const finalMessageHistory = [...messages, userMessage];
      if (data.response) {
        finalMessageHistory.push(new AIMessage(data.response as string));
      } else {
        finalMessageHistory.push(new AIMessage('Xin lỗi, không nhận được phản hồi từ AI.'));
      }

      setMessages(finalMessageHistory);
    } catch (error) {
      console.error('Error calling NestJS API:', error);
      // Thay thế tin nhắn loading bằng thông báo lỗi
      const errorMessageHistory = [
        ...messages,
        userMessage,
        new AIMessage('Xin lỗi, đã có lỗi xảy ra khi xử lý yêu cầu của bạn.'),
      ];
      setMessages(errorMessageHistory);
    }

    setIsLoading(false);
  }

  return (
    // <div className={` ${isExpanded ? 'expanded' : ''} chat-wrapper  flex h-full flex-col`}>
    <div className={`chat-wrapper  flex h-full flex-col`}>
      <header className="flex-shrink-0 border-b bg-gradient-to-r from-[#379ae6] via-[#4da6ea] via-[#5bb3ed] to-[#69c0f0] p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="ml-2 font-semibold text-white">Trợ lý AI</span>
            <span className="ml-2 text-xs text-white/80">({messages.length - 1} tin nhắn)</span>
          </div>
          <div className="flex items-center space-x-2">
            {/* <Button
              icon={isExpanded ? 'arrowup' : 'arrowdown'} // hoặc dùng 'fullscreen' / 'restore'
              stylingMode="text"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-blue-600"
              hint={isExpanded ? 'Thu nhỏ' : 'Phóng to'}
            /> */}
            <Button
              icon="trash"
              stylingMode="text"
              onClick={() => void clearChatHistory()}
              className="text-white/80 [&_.dx-icon-trash:before]:!text-white hover:[&_.dx-icon-trash:before]:!text-red-400"
              hint="Xóa lịch sử chat"
            />
            <Button
              icon="close"
              stylingMode="text"
              onClick={onClose}
              className="text-white/80 hover:text-white  [&_.dx-icon-close:before]:!text-white"
            />
          </div>
        </div>
      </header>
      <div className="flex-1 overflow-y-auto p-1 pb-32">
        {isLoadingMessages ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-gray-500">Đang tải tin nhắn...</div>
          </div>
        ) : (
          messages.length > 0 &&
          messages.map((message, index) => {
            if (message instanceof HumanMessage) {
              return (
                <div key={message.getType() + index} className="flex justify-end rounded-lg p-3">
                  <div className="flex flex-row-reverse items-start">
                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-orange-400 text-sm text-white">
                      Me
                    </div>
                    <div className="relative mr-3 rounded-xl bg-gradient-to-r from-[#379ae6] via-[#4da6ea] via-[#5bb3ed] to-[#69c0f0] px-4 py-2 text-sm text-white shadow">
                      <div>{message.content as string}</div>
                      {/* Gốc nhọn trỏ về phía avatar */}
                      <div className="absolute -right-2 top-2 h-0 w-0 border-b-[15px] border-l-[10px] border-t-[6px] border-b-transparent border-l-[#69c0f0] border-t-transparent" />
                    </div>
                  </div>
                </div>
              );
            }

            if (message instanceof AIMessage) {
              return (
                <div key={message.getType() + index} className="flex justify-start rounded-lg  p-3">
                  <div className="flex max-w-full flex-row items-start">
                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-400 text-sm text-white">
                      AI
                    </div>

                    <div
                      className="relative ml-3 rounded-xl bg-gray-100 text-sm shadow"
                      style={{ minWidth: '300px', maxWidth: '800px' }}
                    >
                      {/* Gốc nhọn trỏ về phía avatar AI */}
                      <div className="absolute -left-2 top-2 h-0 w-0 border-b-[15px] border-r-[10px] border-t-[6px] border-b-transparent border-r-gray-100 border-t-transparent" />
                      <div className="whitespace-pre-wrap">
                        {/* Hiển thị loading state cho AI message */}
                        {(message.content as string) === 'Chờ phản hồi' ? (
                          <div className="flex items-center px-4 py-2">
                            <div
                              className="mr-1 h-2 w-2 animate-typing-bounce rounded-full bg-gray-500"
                              style={{ animationDelay: '0s' }}
                            ></div>
                            <div
                              className="mr-1 h-2 w-2 animate-typing-bounce rounded-full bg-gray-500"
                              style={{ animationDelay: '0.2s' }}
                            ></div>
                            <div
                              className="mr-2 h-2 w-2 animate-typing-bounce rounded-full bg-gray-500"
                              style={{ animationDelay: '0.4s' }}
                            ></div>
                            <span className="text-gray-500">{message.content as string}</span>
                          </div>
                        ) : message.content &&
                          (message.content as string).includes('|') &&
                          (message.content as string).includes('---') ? (
                          <div>
                            {/* Hiển thị text trước bảng */}
                            <div className="mb-3 px-4 py-2">
                              {(message.content as string)
                                .split('\n')
                                .filter(
                                  line =>
                                    !line.includes('|') && !line.includes('---') && line.trim()
                                )
                                .join('\n')}
                            </div>
                            {/* Hiển thị bảng */}
                            <div className="max-h-[400px] overflow-y-auto rounded-lg border border-gray-300">
                              <div className="w-full overflow-x-auto">
                                <table className="w-full border-collapse bg-white">
                                  <thead>
                                    <tr className="bg-gray-50">
                                      {(() => {
                                        const lines = (message.content as string).split('\n');
                                        const headerLine = lines.find((line, index) => {
                                          const nextLine = lines[index + 1];
                                          return (
                                            line.includes('|') &&
                                            !line.includes('---') &&
                                            nextLine &&
                                            nextLine.includes('---')
                                          );
                                        });
                                        if (!headerLine) return null;
                                        const headerCells = headerLine.split('|');
                                        // Loại bỏ cell đầu và cuối nếu rỗng (do split với |)
                                        const cleanHeaderCells = headerCells.slice(
                                          headerCells[0].trim() === '' ? 1 : 0,
                                          headerCells[headerCells.length - 1].trim() === ''
                                            ? -1
                                            : headerCells.length
                                        );
                                        return cleanHeaderCells.map((header, idx) => (
                                          <th
                                            key={idx}
                                            className={`sticky top-0 border border-gray-300 px-3 py-2 text-left text-sm font-semibold ${
                                              idx === 0
                                                ? 'min-w-[50px] bg-gray-50'
                                                : 'min-w-[100px] bg-gray-50'
                                            }`}
                                          >
                                            {header.trim()}
                                          </th>
                                        ));
                                      })()}
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {(() => {
                                      const lines = (message.content as string).split('\n');
                                      const headerIndex = lines.findIndex((line, index) => {
                                        const nextLine = lines[index + 1];
                                        return (
                                          line.includes('|') &&
                                          !line.includes('---') &&
                                          nextLine &&
                                          nextLine.includes('---')
                                        );
                                      });

                                      return lines
                                        .filter((line, index) => {
                                          return (
                                            line.includes('|') &&
                                            !line.includes('---') &&
                                            index > headerIndex && // Loại trừ header
                                            line.trim() !== ''
                                          );
                                        })
                                        .map((row, rowIdx) => (
                                          <tr
                                            key={rowIdx}
                                            className="transition-colors hover:bg-blue-50"
                                          >
                                            {(() => {
                                              const cells = row.split('|');
                                              // Loại bỏ cell đầu và cuối nếu rỗng (do split với |)
                                              const cleanCells = cells.slice(
                                                cells[0].trim() === '' ? 1 : 0,
                                                cells[cells.length - 1].trim() === ''
                                                  ? -1
                                                  : cells.length
                                              );
                                              return cleanCells.map((cell, cellIdx) => (
                                                <td
                                                  key={cellIdx}
                                                  className={`border border-gray-300 px-3 py-2 text-sm ${
                                                    cellIdx === 0
                                                      ? `min-w-[120px] ${
                                                          rowIdx % 2 === 0
                                                            ? 'bg-white'
                                                            : 'bg-gray-50'
                                                        }`
                                                      : 'min-w-[100px]'
                                                  }`}
                                                >
                                                  {cell.trim()}
                                                </td>
                                              ));
                                            })()}
                                          </tr>
                                        ));
                                    })()}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="px-4 py-2">{message.content as string}</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            }
          })
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="absolute bottom-0 left-0 right-0 rounded-tl-lg rounded-tr-lg bg-gray-50 py-2">
        <div className="flex  w-full flex-row items-center rounded-tl-lg  rounded-tr-lg bg-gray-50 px-2">
          <div className="ml-1 flex-grow">
            <div className="relative w-full">
              <textarea
                disabled={isLoading}
                value={inputMessage}
                placeholder="Nhập nội dung..."
                onChange={e => setInputMessage(e.target.value)}
                className="flex max-h-16 min-h-10 w-full resize-none rounded-md border p-2 focus:border-gray-300 focus:outline-none"
                rows={1}
                onInput={e => {
                  const textarea = e.target as HTMLTextAreaElement;
                  textarea.style.height = 'auto';
                  textarea.style.height = `${textarea.scrollHeight}px`;
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (inputMessage.trim()) {
                      sendMessage().catch(console.error);
                    }
                  }
                }}
              />
            </div>
          </div>
          <div className="ml-4">
            <button
              disabled={isLoading}
              onClick={() => {
                if (inputMessage.trim()) sendMessage().catch(console.error);
              }}
              className="flex flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-[#379ae6] via-[#4da6ea] via-[#5bb3ed] to-[#69c0f0] px-4 py-2 text-white hover:bg-info-400"
            >
              <SendHorizonal size={15} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
